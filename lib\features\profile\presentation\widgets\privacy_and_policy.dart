import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PrivacyAndPolicy extends StatelessWidget {
  const PrivacyAndPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'Privacy Policy',
            style: GoogleFonts.outfit(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Last Updated on Nov 05, 2018 17:03',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                Si<PERSON><PERSON><PERSON>(height: 16.h),
                _buildPolicySection(
                  'We recognize that the privacy of our client\'s personal and '
                  'financial information is a fundamental element of public trust '
                  'and confidence in our Internet Banking service. We take all '
                  'reasonable precautionary measures to protect your personally '
                  'identifiable information from loss, misuse or unauthorized '
                  'access. We are well aware of our customer\'s online privacy '
                  'concerns and as such, adopt responsible privacy standards to '
                  'provide our customers with privacy protections in the online '
                  'environment. Please view our privacy policy for further details '
                  'of our commitment towards ensuring our client\'s privacy. We '
                  'have developed this policy so that you may feel confident about '
                  'the privacy and security of your personal information noted '
                  'below and will ensure before we do so, that there is adequate '
                  'protection as required by relevant Data Protection legislation.',
                ),
                SizedBox(height: 24.h),
                Text(
                  '1. Use of Personal Information',
                  style: GoogleFonts.outfit(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 16.h),
                _buildPolicySection(
                  'Where we collect personal information about you, this personal '
                  'information may be shared with and used by other members of '
                  'the Group.\n\n'
                  'You do not need to register to access our website, which is the '
                  'case with this website, and where you do not register, personal '
                  'information about you will not be collected by us unless you give '
                  'us this voluntarily in order to process a specific request. We will '
                  'use that data only to fulfill that request.\n\n'
                  'If you register for our internet banking services, any details you '
                  'provide may be stored for use on future visits, as we do not want '
                  'you to give us any information more than once. The details you '
                  'give us may be combined with information about your use of the '
                  'website and information from other of our '
                  'records in order to provide you with the online products and '
                  'services you request. If you register for our internet banking '
                  'services, we will treat you as having consented to our using your '
                  'personal data in the manner described in this Policy.\n\n'
                  'We currently may hold personal and financial information about '
                  'you and may obtain or receive such information about you in the '
                  'future. Such information may include the identity of suppliers of '
                  'goods and services to you and the general nature of such goods '
                  'and services and may also include sensitive personal data such '
                  'as information relating to your health, criminal convictions or '
                  'proceedings where we need to hold such data for the purposes '
                  'of the product or services we provide to you or it is in the normal '
                  'course of business to do so.',
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildPolicySection(String text) {
    return Text(
      text,
      style: GoogleFonts.outfit(
        fontSize: 15.h,
        height: 1.5,
        color: Colors.black54,
      ),
    );
  }
}
