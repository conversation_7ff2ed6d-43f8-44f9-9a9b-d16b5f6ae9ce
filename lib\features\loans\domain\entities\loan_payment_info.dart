import 'package:equatable/equatable.dart';

/// Unified loan payment info entity for all loan types
class LoanPaymentInfo extends Equatable {
  final String loanId;
  final ProductDetails productDetails;
  final PaymentDetails paymentDetails;
  final LoanDetails loanDetails;

  const LoanPaymentInfo({
    required this.loanId,
    required this.productDetails,
    required this.paymentDetails,
    required this.loanDetails,
  });

  @override
  List<Object> get props => [loanId, productDetails, paymentDetails, loanDetails];
}

/// Unified payment details for loan calculations
class PaymentDetails extends Equatable {
  final double upfrontPaymentAmount;
  final String upfrontPaymentPercentage;
  final double upfrontFacilitationFee;
  final double loanAmount;
  final double monthlyPayment;
  final double monthlyFacilitationFee;
  final double totalMonthlyPayment;
  final String totalPayment;
  final String totalInterest;
  final double interestRate;

  const PaymentDetails({
    required this.upfrontPaymentAmount,
    required this.upfrontPaymentPercentage,
    required this.upfrontFacilitationFee,
    required this.loanAmount,
    required this.monthlyPayment,
    required this.monthlyFacilitationFee,
    required this.totalMonthlyPayment,
    required this.totalPayment,
    required this.totalInterest,
    required this.interestRate,
  });

  @override
  List<Object> get props => [
        upfrontPaymentAmount,
        upfrontPaymentPercentage,
        upfrontFacilitationFee,
        loanAmount,
        monthlyPayment,
        monthlyFacilitationFee,
        totalMonthlyPayment,
        totalPayment,
        totalInterest,
        interestRate,
      ];
}

/// Unified loan details for all loan types
class LoanDetails extends Equatable {
  final int loanPeriod;
  final Map<String, dynamic> applicationFee;
  final Map<String, dynamic> facilitationRate;
  final Map<String, dynamic> penaltyStructure;
  final int gracePeriodInDays;
  final String amortizationType;
  final double dailyPenaltyFee;

  const LoanDetails({
    required this.loanPeriod,
    required this.applicationFee,
    required this.facilitationRate,
    required this.penaltyStructure,
    required this.gracePeriodInDays,
    required this.amortizationType,
    required this.dailyPenaltyFee,
  });

  @override
  List<Object> get props => [
        loanPeriod,
        applicationFee,
        facilitationRate,
        penaltyStructure,
        gracePeriodInDays,
        amortizationType,
        dailyPenaltyFee,
      ];
}

/// Unified product details for all loan types
class ProductDetails extends Equatable {
  final String productId;
  final String productName;
  final String productDescription;

  const ProductDetails({
    required this.productId,
    required this.productName,
    required this.productDescription,
  });

  @override
  List<Object> get props => [productId, productName, productDescription];
}
