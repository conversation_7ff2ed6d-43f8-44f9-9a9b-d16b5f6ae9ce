import 'package:cbrs/features/mortgage_loan/application/bloc/mortgage_bank_bloc.dart';
import 'package:cbrs/features/mortgage_loan/application/bloc/mortgage_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TermsAndConditionsContainer extends StatefulWidget {
  const TermsAndConditionsContainer({super.key});

  @override
  State<TermsAndConditionsContainer> createState() => _TermsAndConditionsContainerState();
}

class _TermsAndConditionsContainerState extends State<TermsAndConditionsContainer> {
  String? cachedContent;

  @override
  Widget build(BuildContext context) {
    return Container(
     
                 padding: const EdgeInsets.only(bottom:16),

      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: BlocConsumer<MortgageBankBloc, MortgageBankState>(
        listenWhen: (previous, current) => current is LoanTermsFetched,
        listener: (context, state) {
          if (state is LoanTermsFetched) {
            cachedContent = state.content;
          }
        },
        buildWhen: (previous, current) {
          return current is FetchingLoanTerms ||
              current is LoanTermsFetched ||
              current is LoanTermsError ||
              current is MortgageLoanApplying ||
              current is MortgageLoanApplyFailure ||
              current is MortgageLoanApplySuccess;
        },
        builder: (context, state) {
          if (state is FetchingLoanTerms) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // Use cached content or get from state
          final content = cachedContent ?? _getContentFromState(state);

          if (content != null) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),

            physics: ClampingScrollPhysics(),
            
              child: HtmlWidget(
                content,
                textStyle: GoogleFonts.outfit(
                  fontSize: 14,
                  color: const Color(0xff595959),
                  height: 1.5,
                ),
                customStylesBuilder: (element) {
                  if (element.localName == 'p') {
                    return {
                      'margin': '8px 0',
                      'padding': '0',
                    };
                  }
                  return null;
                },
              ),
            );
          } else if (state is LoanTermsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade400,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load terms and conditions',
                    style: GoogleFonts.outfit(
                      color: Colors.red.shade400,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.outfit(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            );
          }
          return const Center(
            child: Text('No terms and conditions available'),
          );
        },
      ),
    );
  }

  String? _getContentFromState(MortgageBankState state) {
    if (state is LoanTermsFetched) {
      return state.content;
    } else if (state is MortgageLoanApplyFailure) {
      return state.termsContent;
    } else if (state is MortgageLoanApplySuccess) {
      return state.termsContent;
    }
    return null;
  }
}
