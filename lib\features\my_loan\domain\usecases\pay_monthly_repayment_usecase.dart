import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:equatable/equatable.dart';

class GenerateMonthlyRepaymentUseCase
    extends UsecaseWithParams<MonthlyRepaymentEntity, MonthlyRepaymentParams> {
  GenerateMonthlyRepaymentUseCase(this._repositiory);
  final LoanRepaymentRepository _repositiory;

  @override
  ResultFuture<MonthlyRepaymentEntity> call(
      MonthlyRepaymentParams params) async {
    return _repositiory.generateMonthlyRepayment(
        loanId: params.loanId, months: params.months);
  }
}

class MonthlyRepaymentParams extends Equatable {
  const MonthlyRepaymentParams({required this.loanId, required this.months});
  final String loanId;
  final String months;

  @override
  List<Object?> get props => [loanId];
}
