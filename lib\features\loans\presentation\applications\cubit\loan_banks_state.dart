import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:equatable/equatable.dart';

/// Comprehensive state for loan banks management
abstract class LoanBanksState extends Equatable {
  const LoanBanksState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class LoanBanksInitial extends LoanBanksState {
  const LoanBanksInitial();
}

/// Loading state with optional message for better UX
class LoanBanksLoading extends LoanBanksState {
  const LoanBanksLoading({
    this.message = 'Loading banks...',
    this.isRefreshing = false,
  });

  final String message;
  final bool isRefreshing;

  @override
  List<Object?> get props => [message, isRefreshing];
}

/// Success state with comprehensive data
class LoanBanksLoaded extends LoanBanksState {
  const LoanBanksLoaded({
    required this.banks,
    required this.loanType,
    required this.productId,
    this.selectedBank,
    this.selectedLoanProduct,
    this.selectedUpfrontPayment,
    this.paymentInfo,
    this.isFromCache = false,
    this.lastUpdated,
    this.hasMoreBanks = false,
    this.currentPage = 1,
  });

  final List<LoanBank> banks;
  final LoanItemType loanType;
  final String productId;
  final LoanBank? selectedBank;
  final LoanProduct? selectedLoanProduct;
  final UpfrontPayment? selectedUpfrontPayment;
  final dynamic paymentInfo;
  final bool isFromCache;
  final DateTime? lastUpdated;
  final bool hasMoreBanks;
  final int currentPage;

  bool get isDataFresh {
    if (lastUpdated == null) return false;
    return DateTime.now().difference(lastUpdated!).inMinutes < 5;
  }

  List<UpfrontPayment> get availableUpfrontPayments {
    if (selectedBank == null || selectedLoanProduct == null) return [];
    return selectedLoanProduct!.upfrontPaymentOptions;
  }

  bool get isSelectionComplete {
    return selectedBank != null &&
        selectedLoanProduct != null &&
        selectedUpfrontPayment != null;
  }

  /// Get the best recommended bank (first one with lowest rates)
  LoanBank? get recommendedBank {
    if (banks.isEmpty) return null;

    // Sort by facilitation rate if available, otherwise return first
    try {
      return banks.reduce((a, b) {
        final aRate =
            double.tryParse(a.loans.first.facilitationRate) ?? double.infinity;
        final bRate =
            double.tryParse(b.loans.first.facilitationRate) ?? double.infinity;
        return aRate <= bRate ? a : b;
      });
    } catch (e) {
      return banks.first;
    }
  }

  LoanBanksLoaded copyWith({
    List<LoanBank>? banks,
    LoanItemType? loanType,
    String? productId,
    LoanBank? selectedBank,
    LoanProduct? selectedLoanProduct,
    UpfrontPayment? selectedUpfrontPayment,
    dynamic paymentInfo,
    bool? isFromCache,
    DateTime? lastUpdated,
    bool? hasMoreBanks,
    int? currentPage,
    bool clearPaymentInfo = false,
  }) {
    return LoanBanksLoaded(
      banks: banks ?? this.banks,
      loanType: loanType ?? this.loanType,
      productId: productId ?? this.productId,
      selectedBank: selectedBank ?? this.selectedBank,
      selectedLoanProduct: selectedLoanProduct ?? this.selectedLoanProduct,
      selectedUpfrontPayment:
          selectedUpfrontPayment ?? this.selectedUpfrontPayment,
      paymentInfo: clearPaymentInfo ? null : (paymentInfo ?? this.paymentInfo),
      isFromCache: isFromCache ?? this.isFromCache,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      hasMoreBanks: hasMoreBanks ?? this.hasMoreBanks,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  @override
  List<Object?> get props => [
        banks,
        loanType,
        productId,
        selectedBank,
        selectedLoanProduct,
        selectedUpfrontPayment,
        paymentInfo,
        isFromCache,
        lastUpdated,
        hasMoreBanks,
        currentPage,
      ];
}

/// Error state with retry capabilities
class LoanBanksError extends LoanBanksState {
  const LoanBanksError({
    required this.message,
    required this.errorType,
    this.canRetry = true,
    this.retryCount = 0,
    this.lastAttempt,
    this.cachedBanks,
  });

  final String message;
  final LoanBanksErrorType errorType;
  final bool canRetry;
  final int retryCount;
  final DateTime? lastAttempt;
  final List<LoanBank>? cachedBanks;

  /// Check if we should show cached data while retrying
  bool get shouldShowCachedData =>
      cachedBanks != null && cachedBanks!.isNotEmpty;

  /// Get user-friendly error message
  String get userFriendlyMessage {
    switch (errorType) {
      case LoanBanksErrorType.network:
        return 'Please check your internet connection and try again';
      case LoanBanksErrorType.server:
        return 'Our servers are temporarily unavailable. Please try again';
      case LoanBanksErrorType.timeout:
        return 'Request timed out. Please try again';
      case LoanBanksErrorType.noData:
        return 'No banks available for this loan type';
      case LoanBanksErrorType.unknown:
        return 'Something went wrong. Please try again';
    }
  }

  @override
  List<Object?> get props => [
        message,
        errorType,
        canRetry,
        retryCount,
        lastAttempt,
        cachedBanks,
      ];
}

/// Types of errors that can occur
enum LoanBanksErrorType {
  network,
  server,
  timeout,
  noData,
  unknown,
}

/// State for payment info loading
class LoanBanksPaymentInfoLoading extends LoanBanksState {
  const LoanBanksPaymentInfoLoading({
    required this.banks,
    required this.selectedBank,
    required this.selectedLoanProduct,
    required this.selectedUpfrontPayment,
  });

  final List<LoanBank> banks;
  final LoanBank selectedBank;
  final LoanProduct selectedLoanProduct;
  final UpfrontPayment selectedUpfrontPayment;

  @override
  List<Object?> get props =>
      [banks, selectedBank, selectedLoanProduct, selectedUpfrontPayment];
}
