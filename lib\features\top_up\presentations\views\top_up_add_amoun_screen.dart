import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_number_keyboard.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/airtime_shortcode.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/change_to_birr/application/bloc/change_to_birr_bloc.dart';
import 'package:cbrs/features/change_to_birr/data/models/change_to_birr_response.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/top_up/applications/top_up_bloc.dart';
import 'package:cbrs/features/top_up/applications/top_up_event.dart';
import 'package:cbrs/features/top_up/applications/top_up_state.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_providers_entity.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_success_response_entities.dart';
import 'package:cbrs/features/top_up/presentations/widgets/amount/custom_top_up_amount.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;

class TopUpAddAmounScreen extends StatefulWidget {
  const TopUpAddAmounScreen({
    required this.phoneNumber,
    required this.provider,
    super.key,
  });
  final String phoneNumber;
  final ProvidersEntity provider;

  @override
  State<TopUpAddAmounScreen> createState() => _TopUpAddAmounScreenState();
}

class _TopUpAddAmounScreenState extends State<TopUpAddAmounScreen> {
  final TextEditingController _amountController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;
  final TextEditingController _pinController = TextEditingController();
  bool _isLoading = false;
  final List<String> _definedCards = [
    '5',
    '10',
    '15',
    '25',
    '50',
    '100',
    '150',
    '200',
    '500',
  ];

  String operatorShortCode = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    operatorShortCode = airTimeShortCode(widget.provider.name);

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.topUp,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo:
                    (context.read<TopUpBloc>().state as TopUpCreatedState)
                        .billRefNo,
                transactionType: tx_type.TransactionType.topUp,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  void _showConfirmScreenBottomSheet(TopUpSuccessDataEntity response) {
    debugPrint('response.originalCurrency: ${response.originalCurrency}');
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Top Up',
        'Sender Name': response.senderName,
        'Recipent Phone Number':
            '(+251) $operatorShortCode-${widget.phoneNumber}',
        'amount': '${response.billAmount} ETB',
        'Date': AppMapper.safeFormattedDate(response.createdAt),
      },
      billAmount: response.billAmount,
      totalAmount: response.billAmount,
      originalCurrency: 'ETB',
      confirmButtonText: 'Confirm',
      status: 'Unpaid',
      requiresOtp: response.authorizationType == 'PIN_AND_OTP',
      billRefNo: response.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      totalAmount: transaction.totalAmount,
      billAmount: transaction.billAmount,
      originalCurrency: transaction.originalCurrency,
      status: 'Paid',
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,
      {
        'Transaction Type': 'Top Up',
        'Sender Name': transaction.senderName,
        'Recipent Phone': '(+251) $operatorShortCode-${widget.phoneNumber}',
        'billRefNo': transaction.billRefNo,
        'Date': AppMapper.safeFormattedDate(transaction.createdAt.toString()),
        'amount': '${transaction.billAmount} ETB',
      },
      title: 'Your Top-up transaction was successfully completed',
    );
  }

  void handleCardSelect(String selectedCard) {
    setState(() {
      _amountController.text = selectedCard;
    });
  }

  void _updateAmount(String value) {
    final amount = _amountController.text;
    setState(() {
      _amountController.text = value;
    });
  }

  void _handleContinue({String? valueAmount}) {
    final amount = valueAmount ?? _amountController.text;

    if (amount.isEmpty) {
      CustomToastification(
        context,
        message: 'Please enter or choose defined airtimes',
      );
      return;
    }

    debugPrint('amount $amount');
    setState(() {
      _isLoading = true;
    });
    context.read<TopUpBloc>().add(
          CreateTopUpEvent(
            amount: amount,
            phoneNumber: "+251$operatorShortCode${widget.phoneNumber}",
            beneficiaryId: widget.provider.id,
          ),
        );

    // context.pushNamed(
    //   AppRouteName.topUpConfrim,
    //   extra: {
    //     'totalAmount': amount,
    //     'operatorCode': operatorShortCode,
    //     'phoneNumber': widget.phoneNumber,
    //   },
    // );
  }

  @override
  @override
  Widget build(BuildContext context) {
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Mobile Top-up'),
      ),
      body: BlocConsumer<TopUpBloc, TopUpState>(
        listener: (context, state) {
          debugPrint('stat is tekl 11 $state');

          if (state is CreateToupErrorState) {
            setState(() {
              _isLoading = false;
            });

            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
            CustomToastification(
              context,
              message: '${state.message} ',
            );
          }
          if (state is TopUpCreatedState) {
            setState(() {
              _isLoading = false;
            });
            debugPrint(
              'state.topUpSuccessDataEntity.status ${state.topUpSuccessDataEntity.status}  and state.topUpSuccessDataEntity.authType: ${state.topUpSuccessDataEntity.authorizationType}',
            );
            // if (state.topUpSuccessDataEntity.status == 'COMPLETED') {
            //   context.pushNamed(
            //     AppRouteName.topUpSuccess,
            //     extra: {
            //       'totalAmount': widget.totalAmount,
            //       'operatorCode': operatorShortCode,
            //       'phoneNumber': widget.phoneNumber,
            //       'topUpSuccessDataEntity': state.topUpSuccessDataEntity,
            //     },
            //   );
            // }
            _showConfirmScreenBottomSheet(state.topUpSuccessDataEntity);

            // if (state.topUpSuccessDataEntity.authorizationType == 'PIN') {
            //   debugPrint('stat is tekl $state');
            //   _showConfirmScreenBottomSheet(state.topUpSuccessDataEntity);

            // } else if (state.topUpSuccessDataEntity.authorizationType ==
            //     'PIN_AND_OTP') {
            //   context.pushNamed(
            //     AppRouteName.topUpConfirmOtp,
            //     extra: {
            //       'totalAmount': widget.totalAmount,
            //       'operatorCode': operatorShortCode,
            //       'phoneNumber': widget.phoneNumber,
            //       'authType': 'PIN_AND_OTP',
            //       'billRefNo': state.topUpSuccessDataEntity.billRefNo,
            //     },
            //   );
            // }
          }

          // if (state is TopUpPinConfirmedState) {
          //   context.pop(true); // pop pin bottom sheet
          //   context.pushNamed(
          //     AppRouteName.topUpSuccess,
          //     extra: {
          //       'totalAmount': widget.totalAmount,
          //       'operatorCode': operatorShortCode,
          //       'phoneNumber': widget.phoneNumber,
          //       'topUpSuccessDataEntity': state.topUpSuccessDataEntity,
          //     },
          //   );
          // }
        },
        builder: (context, state) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(height: 16.h),
                        const CustomPageHeader(
                          pageTitle: 'Add Amount',
                          description:
                              'Enter or choose the top-up amount for the entered phone number.',
                        ),
                        SizedBox(height: 8.h),
                        SelectedActionPreview(
                          phoneNumber: widget.phoneNumber,
                          operatorName: widget.provider.name,
                          operatorLogo: operatorShortCode == '9'
                              ? MediaRes.ethMiniIcon
                              : MediaRes.safariMiniIcon,
                          operatorShortCode: operatorShortCode,
                        ),
                        // SizedBox(height: 32.h),
                        // SizedBox(
                        //   height: MediaQuery.sizeOf(context).height * 0.1,
                        // ),
                        Column(
                          children: [
                            SelectandChooseAirtime(
                              amountController: _amountController,
                              definedCards: _definedCards,
                              handleCardSelect: handleCardSelect,
                              handleContinue: (value) =>
                                  _handleContinue(valueAmount: value),
                            ),
                            const SizedBox(height: 12),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                // Use Visibility widget to hide the button when the keyboard is visible
                CustomRoundedBtn(
                  btnText: 'Continue',
                  onTap: () {
                    debugPrint('where are we');
                    _handleContinue();
                  },
                  isLoading: _isLoading,
                  isBtnActive: _amountController.text.isNotEmpty,
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class SelectedActionPreview extends StatelessWidget {
  const SelectedActionPreview({
    required this.phoneNumber,
    required this.operatorLogo,
    required this.operatorName,
    required this.operatorShortCode,
    super.key,
  });

  final String phoneNumber;
  final String operatorLogo;
  final String operatorName;
  final String operatorShortCode;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            // padding: const EdgeInsets.all(12),
            clipBehavior: Clip.antiAlias,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: const DecorationImage(
                image: AssetImage(
                  MediaRes.birrRecipentBgColor,
                ),
                fit: BoxFit.fill,
              ),
            ),
            // decoration: ShapeDecoration(
            //   color: Colors.white,
            //   shape: RoundedRectangleBorder(
            //     side: const BorderSide(width: 0.30, color: Color(0x66065234)),
            //     borderRadius: BorderRadius.circular(16),
            //   ),
            //   shadows: const [
            //     BoxShadow(
            //       color: Color(0x0F000000),
            //       blurRadius: 24,
            //     ),
            //   ],
            // ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: double.infinity,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 48.w,
                        height: 48.h,
                        clipBehavior: Clip.antiAlias,
                        decoration: ShapeDecoration(
                          image: DecorationImage(
                            image: AssetImage(operatorLogo),
                            fit: BoxFit.cover,
                          ),
                          shape: RoundedRectangleBorder(
                            // side: const BorderSide(
                            //   width: 0.40,
                            //   strokeAlign: BorderSide.strokeAlignCenter,
                            //   color: Color(0xFF16553A),
                            // ),
                            borderRadius: BorderRadius.circular(200),
                          ),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Container(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '(+251) $operatorShortCode-${phoneNumber.substring(0, 4)}-${phoneNumber.substring(4)}',
                                      style: GoogleFonts.outfit(
                                        color: Colors.black,
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                operatorName,
                                style: GoogleFonts.outfit(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class SelectandChooseAirtime extends StatefulWidget {
  const SelectandChooseAirtime({
    required this.amountController,
    required this.definedCards,
    required this.handleCardSelect,
    required this.handleContinue,
    super.key,
  });
  final List<String> definedCards;
  final TextEditingController amountController;
  final void Function(String) handleCardSelect;
  final Function(String? amount) handleContinue;

  @override
  State<SelectandChooseAirtime> createState() => _SelectandChooseAirtimeState();
}

class _SelectandChooseAirtimeState extends State<SelectandChooseAirtime> {
  bool showKeyPad = false;
  String selectedAirTime = '0.00';
  double walletBalance = 0;

  void handleShowKeyPad() {
    setState(() {
      showKeyPad = !showKeyPad;
      selectedAirTime = widget.amountController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 16.h,
          ),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select top-up amount',
                      style: GoogleFonts.outfit(
                        color: Colors.black,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    Row(
                      children: [
                        Expanded(child: definedCardsList(context)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 26.h,
          ),
          GestureDetector(
            onTap: _handleCustomInput,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(32),
                border: Border.all(color: Theme.of(context).primaryColor),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.add,
                    color: Theme.of(context).primaryColor,
                    size: 16,
                  ),
                  CustomBuildText(
                    text: 'Send custom amount',
                    caseType: '',
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 14.sp,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleCustomInput() {
    CustomTopUpAmount.show(
      context,
      walletBalance: AppMapper.safeDouble(walletBalance),
      onContinue: (value) => widget.handleContinue(value),
    );

    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     backgroundColor: Colors.transparent,
    //     builder: (context) => Container(
    //           padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16),
    //           decoration: BoxDecoration(
    //             color: Colors.white,
    //             borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
    //           ),
    //           child:
    //            CustomTopUpAmount.show(context, walletBalance: AppMapper.safeDouble(walletBalance)),
    //         ),

    //         );
  }

  Widget definedCardsList(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    final cardWidth = screenWidth / 3 - 20;

    return Wrap(
      spacing: 10.w,
      runSpacing: 10.h,
      children: List.generate(
        widget.definedCards.length,
        (index) {
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedAirTime = widget.definedCards[index];
              });
              widget.handleCardSelect(widget.definedCards[index]);
            },
            child: Container(
              width: cardWidth,
              padding: EdgeInsets.symmetric(vertical: 8.h),
              decoration: ShapeDecoration(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: Theme.of(context).primaryColor.withOpacity(0.2),
                    width: 0.6,
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: const Color(0x0F000000).withOpacity(0.04),
                    blurRadius: 24,
                  ),
                ],
                gradient: widget.amountController.text ==
                            widget.definedCards[index] ||
                        selectedAirTime == widget.definedCards[index]
                    ? const LinearGradient(
                        colors: [
                          Color(0xFF69AC5C),
                          Color(0xFF085905),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        stops: [0, 100],
                      )
                    : const LinearGradient(
                        colors: [
                          Colors.white,
                          Colors.white,
                        ],
                      ),
              ),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                // color: Colors.red,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Image.asset(
                      MediaRes.topUpSimCard,
                      width: 30.w,
                      height: 30.h,
                      color: widget.amountController.text ==
                                  widget.definedCards[index] ||
                              selectedAirTime == widget.definedCards[index]
                          ? Colors.white
                          : Theme.of(context).primaryColor,
                    ),
                    Text(
                      '${widget.definedCards[index]}.00',
                      style: GoogleFonts.outfit(
                        color: widget.amountController.text ==
                                    widget.definedCards[index] ||
                                selectedAirTime == widget.definedCards[index]
                            ? Colors.white
                            : Colors.black,
                        fontSize: 22.sp,
                        // textStyle: const TextStyle(
                        //   height: .8,
                        // ),
                        fontWeight: FontWeight.w700,
                        // backgroundColor: Colors.red,
                      ),
                    ),
                    Text(
                      'ETB',
                      style: GoogleFonts.outfit(
                        color: widget.amountController.text ==
                                    widget.definedCards[index] ||
                                selectedAirTime == widget.definedCards[index]
                            ? Colors.white
                            : Colors.black.withOpacity(0.4),
                        // backgroundColor: Colors.yellow,
                        textStyle: const TextStyle(
                          height: .9,
                        ),
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
