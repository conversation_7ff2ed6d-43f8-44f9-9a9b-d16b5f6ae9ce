import 'package:equatable/equatable.dart';

/// Unified entity for all loan items (cars, houses, etc.)
class LoanItem extends Equatable {
  final String id;
  final String name;
  final String description;
  final double price;
  final String featureImage;
  final List<GalleryImage> galleryImages;
  final LoanItemType type;
  final LoanItemCategory category;
  final int loanPeriod;
  final bool isActive;
  final bool isDeleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Car-specific fields (nullable for houses)
  final String? model;
  final String? transmission;
  final int? numberOfSeats;
  final String? engineCapacity;
  final String? color;
  final String? make;
  final String? driveTrain;
  final int? engineSize;
  final String? fuelType;
  final int? mileage;
  final String? fuelEfficiency;
  final int? horsePower;
  final int? manufactureYear;

  // House-specific fields (nullable for cars)
  final Location? location;
  final List<Amenity>? amenities;
  final String? condition;
  final int? bathroom;
  final int? bedroom;
  final double? area;
  final String? virtualTour;

  const LoanItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.featureImage,
    required this.galleryImages,
    required this.type,
    required this.category,
    required this.loanPeriod,
    required this.isActive,
    required this.isDeleted,
    required this.createdAt,
    required this.updatedAt,
    // Car-specific
    this.model,
    this.transmission,
    this.numberOfSeats,
    this.engineCapacity,
    this.color,
    this.make,
    this.driveTrain,
    this.engineSize,
    this.fuelType,
    this.mileage,
    this.fuelEfficiency,
    this.horsePower,
    this.manufactureYear,
    // House-specific
    this.location,
    this.amenities,
    this.condition,
    this.bathroom,
    this.bedroom,
    this.area,
    this.virtualTour,
  });

  /// Factory constructor for creating a car loan item
  factory LoanItem.car({
    required String id,
    required String name,
    required String description,
    required double price,
    required String featureImage,
    required List<GalleryImage> galleryImages,
    required LoanItemCategory category,
    required int loanPeriod,
    required bool isActive,
    required bool isDeleted,
    required DateTime createdAt,
    required DateTime updatedAt,
    required String model,
    required String transmission,
    required int numberOfSeats,
    required String engineCapacity,
    required String color,
    required String make,
    required String driveTrain,
    required int engineSize,
    required String fuelType,
    required int mileage,
    required String fuelEfficiency,
    required int horsePower,
    required int manufactureYear,
  }) {
    return LoanItem(
      id: id,
      name: name,
      description: description,
      price: price,
      featureImage: featureImage,
      galleryImages: galleryImages,
      type: LoanItemType.car,
      category: category,
      loanPeriod: loanPeriod,
      isActive: isActive,
      isDeleted: isDeleted,
      createdAt: createdAt,
      updatedAt: updatedAt,
      model: model,
      transmission: transmission,
      numberOfSeats: numberOfSeats,
      engineCapacity: engineCapacity,
      color: color,
      make: make,
      driveTrain: driveTrain,
      engineSize: engineSize,
      fuelType: fuelType,
      mileage: mileage,
      fuelEfficiency: fuelEfficiency,
      horsePower: horsePower,
      manufactureYear: manufactureYear,
    );
  }

  /// Factory constructor for creating a house loan item
  factory LoanItem.house({
    required String id,
    required String name,
    required String description,
    required double price,
    required String featureImage,
    required List<GalleryImage> galleryImages,
    required LoanItemCategory category,
    required int loanPeriod,
    required bool isActive,
    required bool isDeleted,
    required DateTime createdAt,
    required DateTime updatedAt,
    required Location location,
    required List<Amenity> amenities,
    required String condition,
    required int bathroom,
    required int bedroom,
    required double area,
    String? virtualTour,
  }) {
    return LoanItem(
      id: id,
      name: name,
      description: description,
      price: price,
      featureImage: featureImage,
      galleryImages: galleryImages,
      type: LoanItemType.house,
      category: category,
      loanPeriod: loanPeriod,
      isActive: isActive,
      isDeleted: isDeleted,
      createdAt: createdAt,
      updatedAt: updatedAt,
      location: location,
      amenities: amenities,
      condition: condition,
      bathroom: bathroom,
      bedroom: bedroom,
      area: area,
      virtualTour: virtualTour,
    );
  }

  /// Get specifications based on loan item type
  Map<String, dynamic> get specifications {
    if (type == LoanItemType.car) {
      return {
        'make': make,
        'model': model,
        'color': color,
        'seats': numberOfSeats,
        'engine': engineCapacity,
        'fuelType': fuelType,
        'driveTrain': driveTrain,
        'transmission': transmission,
        'mileage': mileage,
        'horsePower': horsePower,
        'manufactureYear': manufactureYear,
      };
    } else if (type == LoanItemType.house) {
      return {
        'condition': condition,
        'bathroom': bathroom,
        'bedroom': bedroom,
        'area': area,
        'location': location?.fieldName,
        'amenities': amenities?.length ?? 0,
      };
    }
    return {};
  }

  /// Check if this is a car loan item
  bool get isCar => type == LoanItemType.car;

  /// Check if this is a house loan item
  bool get isHouse => type == LoanItemType.house;

  /// Get the main image URL (alias for featureImage)
  String get imageUrl => featureImage;

  /// Get subtitle based on loan item type
  String? get subtitle {
    if (type == LoanItemType.car) {
      return '$make $model';
    } else if (type == LoanItemType.house) {
      return location?.fieldName;
    }
    return null;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        featureImage,
        galleryImages,
        type,
        category,
        loanPeriod,
        isActive,
        isDeleted,
        createdAt,
        updatedAt,
        // Car fields
        model,
        transmission,
        numberOfSeats,
        engineCapacity,
        color,
        make,
        driveTrain,
        engineSize,
        fuelType,
        mileage,
        fuelEfficiency,
        horsePower,
        manufactureYear,
        // House fields
        location,
        amenities,
        condition,
        bathroom,
        bedroom,
        area,
        virtualTour,
      ];
}

/// Enum for loan item types
enum LoanItemType {
  car,
  house,
  general,
}

/// Unified category entity for both cars and houses
class LoanItemCategory extends Equatable {
  final String id;
  final String name;
  final String description;
  final String icon;
  final bool isDeleted;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const LoanItemCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    this.isDeleted = false,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        icon,
        isDeleted,
        createdAt,
        updatedAt,
      ];
}

/// Gallery image entity (shared between cars and houses)
class GalleryImage extends Equatable {
  const GalleryImage({
    required this.type,
    required this.url,
  });

  final String type;
  final String url;

  @override
  List<Object> get props => [type, url];
}

/// Location entity for houses
class Location extends Equatable {
  const Location({
    required this.fieldName,
    required this.lng,
    required this.lat,
  });

  final String fieldName;
  final String lng;
  final String lat;

  @override
  List<Object> get props => [fieldName, lng, lat];
}

/// Amenity entity for houses
class Amenity extends Equatable {
  const Amenity({
    required this.amenity,
    required this.detail,
  });

  final AmenityDetail amenity;
  final String detail;

  Map<String, dynamic> toJson() {
    return {
      'amenity': amenity.toJson(),
      'detail': detail,
    };
  }

  @override
  List<Object> get props => [amenity, detail];
}

/// Amenity detail entity
class AmenityDetail extends Equatable {
  const AmenityDetail({
    required this.name,
    required this.icon,
  });

  final String name;
  final String icon;

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': icon,
    };
  }

  @override
  List<Object> get props => [name, icon];
}
