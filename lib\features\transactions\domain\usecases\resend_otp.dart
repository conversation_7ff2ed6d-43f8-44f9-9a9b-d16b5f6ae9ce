import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:equatable/equatable.dart';

class ResendTransactionOtp extends UsecaseWithParams<dynamic, ResendOtpParams> {
  const ResendTransactionOtp(this._repository);
  final TransactionRepository _repository;

  @override
  ResultFuture<dynamic> call(ResendOtpParams params) async {
    return _repository.resendOtp(
      billRefNo: params.billRefNo,
      otpFor: params.otpFor,
    );
  }
}

class ResendOtpParams extends Equatable {
  const ResendOtpParams({
    required this.billRefNo,
    required this.otpFor,
  });
  final String billRefNo;
  final String otpFor;

  @override
  List<Object?> get props => [billRefNo, otpFor];
}
