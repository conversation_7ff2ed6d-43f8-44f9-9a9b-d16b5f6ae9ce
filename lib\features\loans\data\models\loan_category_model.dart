import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/core/utils/app_mapper.dart';

class LoanCategoryModel extends LoanCategory {
  const LoanCategoryModel({
    required super.id,
    required super.name,
    required super.description,
    required super.icon,
    super.isDeleted = false,
    super.createdAt,
    super.updatedAt,
  });

  factory LoanCategoryModel.fromJson(Map<String, dynamic> json) {
    return LoanCategoryModel(
      id: AppMapper.safeString(json['id'] ?? json['_id'] ?? ''),
      name: AppMapper.safeString(json['name'] ?? ''),
      description: AppMapper.safeString(json['description'] ?? ''),
      icon: AppMapper.safeString(json['icon'] ?? ''),
      isDeleted: json['isDeleted'] == true,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'].toString())
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'].toString())
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        '_id': id,
        'name': name,
        'description': description,
        'icon': icon,
        'isDeleted': isDeleted,
        if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
        if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      };

  // Factory method to create from CarBodyType-like data
  factory LoanCategoryModel.fromCarBodyType({
    required String id,
    required String name,
    required String description,
    required String icon,
  }) {
    return LoanCategoryModel(
      id: id,
      name: name,
      description: description,
      icon: icon,
    );
  }

  // Factory method to create from HouseType-like data
  factory LoanCategoryModel.fromHouseType({
    required String id,
    required String name,
    required String icon,
    bool isDeleted = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    String description = '',
  }) {
    return LoanCategoryModel(
      id: id,
      name: name,
      description: description,
      icon: icon,
      isDeleted: isDeleted,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
