// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';

abstract class RepaymentLoanEvent {}

class FetchLoansEvent extends RepaymentLoanEvent {
  FetchLoansEvent({required this.loanStatus});
  final String loanStatus;
}

class FetchLoanInfoEventEvent extends RepaymentLoanEvent {
  FetchLoanInfoEventEvent({required this.loanId, required this.months});
  final String loanId;
  final String months;
}

class PayRepaymentEvent extends RepaymentLoanEvent {
  PayRepaymentEvent(
      {required this.loanId, required this.isRepayment, required this.months});
  final String loanId;
  final String months;

  final bool isRepayment;
}

class PayUpfrontPaymentEvent extends RepaymentLoanEvent {
  PayUpfrontPaymentEvent({
    required this.loanId,
  });
  final String loanId;
}

class FetchRepaymentHistoryEvent extends RepaymentLoanEvent {
  FetchRepaymentHistoryEvent({
    required this.loanPaymentId,
  });
  final String loanPaymentId;
}

class PayLoanEvent extends RepaymentLoanEvent {
  PayLoanEvent(this.loanId);
  final String loanId;
}

class ClearLoansEvent extends RepaymentLoanEvent {}

class ConfirmTransferEvent extends RepaymentLoanEvent {
  ConfirmTransferEvent({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
  });
  final String pin;
  final String billRefNo;
  final TransactionType transactionType;

  @override
  List<Object?> get props => [
        pin,
        billRefNo,
        transactionType,
      ];
}

class GenerateMonthlyRepaymentEvent extends RepaymentLoanEvent {
  final String loanId;
  final String months;
  GenerateMonthlyRepaymentEvent({
    required this.loanId,
    required this.months,
  });

}

class ConfirmMonthlyRepaymentEvent extends RepaymentLoanEvent {
  ConfirmMonthlyRepaymentEvent({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
    required this.months,
    required this.loanPaymentId,
  });

  final String pin;
  final String billRefNo;
  final String months;
  final String loanPaymentId;

  final TransactionType transactionType;

  @override
  List<Object?> get props => [
        pin,
        billRefNo,
        transactionType,
      ];
}

class VerifyOtpEvent extends RepaymentLoanEvent {
  VerifyOtpEvent({
    required this.billRefNo,
    required this.otpFor,
    required this.otpCode,
  });
  final String billRefNo;
  final String otpFor;
  final int otpCode;

  @override
  List<Object?> get props => [billRefNo, otpFor, otpCode];
}

class ResendOtpEvent extends RepaymentLoanEvent {
  ResendOtpEvent({
    required this.billRefNo,
    required this.otpFor,
  });
  final String billRefNo;
  final String otpFor;

  @override
  List<Object?> get props => [billRefNo, otpFor];
}
