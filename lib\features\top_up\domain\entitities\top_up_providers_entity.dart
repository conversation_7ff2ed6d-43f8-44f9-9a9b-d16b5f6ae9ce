import 'package:equatable/equatable.dart';

class TopUpProvidersEntity extends Equatable {
  TopUpProvidersEntity({required this.providers});
 final List<ProvidersEntity> providers;
  @override
  // TODO: implement props
   List<Object?> get props => throw UnimplementedError();
}

class ProvidersEntity extends Equatable {
  const ProvidersEntity({
    required this.id,
    required this.name,
    required this.code,
    required this.merchantType,
    required this.logo,
  });
  final String id;
  final String name;
  final String code;
  final String merchantType;
  final String logo;

  @override
  // TODO: implement props
  List<Object?> get props => throw UnimplementedError();
}
