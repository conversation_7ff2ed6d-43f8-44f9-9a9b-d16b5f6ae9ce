import 'package:cbrs/core/constants/mini_apps_urls.dart';
// import 'package:core/constants/mini_apps_urls.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

class MiniAppsWebView extends StatefulWidget {
  final String url;
  final String appName;

  const MiniAppsWebView({
    super.key,
    required this.url,
    required this.appName,
  });

  @override
  State<MiniAppsWebView> createState() => _MiniAppsWebViewState();
}

class _MiniAppsWebViewState extends State<MiniAppsWebView> {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;
  bool _hasCameraPermission = true;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  @override
  void initState() {
    super.initState();
    _checkCameraPermission();
    _initWebView();
  }

  Future<void> _checkCameraPermission() async {
    final status = await Permission.camera.status;
    if (status.isGranted) {
      setState(() => _hasCameraPermission = true);
    }
  }

  void _initWebView() {
    if (widget.url.isEmpty) {
      setState(() => _hasError = true);
      return;
    }

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..enableZoom(true)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (url) {
            if (mounted) {
              setState(() => _isLoading = false);
            }
          },
          onWebResourceError: (error) {
            debugPrint('Web resource error: ${error.description}');
            if (mounted) {
              setState(() {
                _hasError = true;
                _retryCount++;
              });
            }
          },
          onNavigationRequest: (request) {
            return NavigationDecision.navigate;
            if (MiniAppsConfig.isAllowedDomain(request.url)) {}
            // Open non-allowed domains in external browser
            launchUrl(Uri.parse(request.url),
                mode: LaunchMode.externalApplication);
            return NavigationDecision.prevent;
          },
        ),
      )
      ..addJavaScriptChannel(
        'CameraPermission',
        onMessageReceived: (message) async {
          if (!_hasCameraPermission) {
            final status = await Permission.camera.request();
            if (status.isGranted) {
              setState(() => _hasCameraPermission = true);
              _controller.runJavaScript('window.onCameraPermissionGranted()');
            } else {
              _showPermissionDialog(context);
            }
          }
        },
      );

    _loadWebView();
  }

  void _loadWebView() {
    try {
      _controller.loadRequest(
        Uri.parse(widget.url),
        headers: {
          'Content-Security-Policy': "frame-ancestors 'self'",
          'X-Frame-Options': 'SAMEORIGIN'
        },
      );
    } catch (e) {
      debugPrint('Error loading WebView: $e');
      if (mounted) {
        setState(() => _hasError = true);
      }
    }
  }

  void _showPermissionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Camera Permission Required',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'This mini app needs camera access to function properly. Please grant camera permission to continue.',
          style: GoogleFonts.outfit(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.outfit(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF065234),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              'Open Settings',
              style: GoogleFonts.outfit(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        title: Text(
          widget.appName,
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () async {
            if (await _controller.canGoBack()) {
              await _controller.goBack();
            } else {
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            }
          },
        ),
        // actions: [
        //   IconButton(
        //     icon: const Icon(Icons.refresh),
        //     onPressed: _loadWebView,
        //   ),
        // ],
      ),
      body: SafeArea(
        child: Stack(
          children: [
            if (!_hasError) WebViewWidget(controller: _controller),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
            if (_hasError && _retryCount < _maxRetries)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Failed to load ${widget.appName}',
                      style: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 16.h),
                    ElevatedButton(
                      onPressed: _loadWebView,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF065234),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(100.r),
                        ),
                      ),
                      child: Text(
                        'Retry',
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
