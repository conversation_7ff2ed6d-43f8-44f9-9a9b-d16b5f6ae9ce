import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_confirmation.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

/// Use case for confirming loan payment with PIN
class ConfirmLoanPayment extends UsecaseWithParams<LoanPaymentConfirmation,
    ConfirmLoanPaymentParams> {
  const ConfirmLoanPayment(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<LoanPaymentConfirmation> call(
    ConfirmLoanPaymentParams params,
  ) async {
    return repository.confirmLoanPayment(
      transactionType: params.transactionType,
      billRefNo: params.billRefNo,
      pin: params.pin,
    );
  }
}

/// Parameters for confirming loan payment
class ConfirmLoanPaymentParams extends Equatable {
  const ConfirmLoanPaymentParams({
    required this.transactionType,
    required this.billRefNo,
    required this.pin,
  });

  final String transactionType;
  final String billRefNo;
  final String pin;

  @override
  List<Object?> get props => [
        transactionType,
        billRefNo,
        pin,
      ];
}
