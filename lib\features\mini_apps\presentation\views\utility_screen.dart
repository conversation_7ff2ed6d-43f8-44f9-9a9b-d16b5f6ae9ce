import 'package:cbrs/features/mini_apps/presentation/views/utility_payment_permission_screen.dart';
import 'package:flutter/material.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/mini_apps/domain/app_model.dart';
import 'package:cbrs/features/mini_apps/domain/mini_app_permission.dart';
import 'package:cbrs/core/constants/mini_apps_urls.dart';
import 'package:cbrs/features/mini_apps/presentation/views/mini_apps_webview.dart';

class UtilityScreen extends StatelessWidget {
  const UtilityScreen({super.key});

  void _showUtilityPaymentPermissionModal(BuildContext context, AppModel app) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => UtilityPaymentPermissionModal(
        utilityName: 'Utility Name',
        amount: '1,500.00',
        walletBalance: '20,000.00',
        customerName: '<PERSON>',
        accountNumber: '**********',
        onNext: () {
          Navigator.pop(context);
          // Add navigation logic here for the next screen
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Utility Payment',
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const CustomPageHeader(
                pageTitle: 'Utility Payment',
                description:
                    'Select a utility from the list and pay your bill easily',
              ),
              SizedBox(height: 16.h),
              _buildUtilityGrid(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUtilityGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 122.w / 128.h,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: utilityApps.length,
      itemBuilder: (context, index) {
        return _buildUtilityItem(context, utilityApps[index]);
      },
    );
  }

  Widget _buildUtilityItem(BuildContext context, AppModel app) {
    return GestureDetector(
      onTap: () {
        if (app.url.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${app.name} is coming soon!')),
          );
          return;
        }

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MiniAppsWebView(
              url: app.url,
              appName: app.name,
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 24,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 56.w,
              height: 56.h,
              padding: EdgeInsets.all(8.w),
              child: Image.asset(
                app.iconPath,
                width: 44.w,
                height: 44.h,
                fit: BoxFit.contain,
              ),
            ),
            SizedBox(height: 8.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text(
                app.name,
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1A1A1A),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

final List<AppModel> utilityApps = [
  AppModel(
    name: 'SchoolFee',
    iconPath: 'assets/icons/schoolFee.png',
    url: MiniAppsUrls.SchoolFeeUrl,
    permissions: const MiniAppPermission(requiresPayment: true),
  ),
  AppModel(
    name: 'Ethiopian Airlines',
    iconPath: 'assets/icons/ethiopian.png',
    url: MiniAppsUrls.ethiopianAirlinesUrl,
    permissions: const MiniAppPermission(
      requiresLocation: true,
      requiresPayment: true,
    ),
  ),
  AppModel(
    name: 'DSTV',
    iconPath: 'assets/icons/dstv.png',
    url: MiniAppsUrls.dstvUrl,
    permissions: const MiniAppPermission(requiresPayment: true),
  ),
  AppModel(
    name: 'Traffic Penalty',
    iconPath: 'assets/icons/TrafficPenalty.png',
    url: MiniAppsUrls.TrafficPenaltyUrl,
    permissions: const MiniAppPermission(requiresPayment: true),
  ),
  AppModel(
    name: 'Fuel Payment',
    iconPath: 'assets/icons/FuelPayment.png',
    url: MiniAppsUrls.fuelPaymentUrl,
    permissions: const MiniAppPermission(
      requiresPayment: true,
      requiresCamera: true,
    ),
  ),
];
