import 'dart:ui';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';

class DashedLinePainter extends CustomPainter {
  DashedLinePainter({required this.color});
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = color
      ..strokeWidth = 2;

    var max = size.height;
    var dashWidth = 4;
    var dashSpace = 4;
    double startY = 0;
    while (startY < max) {
      canvas.drawLine(Offset(0, startY), Offset(0, startY + dashWidth), paint);
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class LoanDocumentTimeline extends StatelessWidget {
  final List<LoanDocumentItem> items = const [
    LoanDocumentItem(
      title: 'Proof of Identity',
      description:
          'Please prepare a valid document, like your ID card, passport or driver\'s license.',
    ),
    LoanDocumentItem(
      title: 'Proof of Residence',
      description:
          'Prepare your residential document to upload as part of the verification process.',
    ),
    LoanDocumentItem(
      title: 'Income Proof',
      description:
          'Please prepare documents like your recent payslips, tax returns, or bank statements.',
    ),
    LoanDocumentItem(
      title: 'Credit Report',
      description:
          'Please prepare your credit report to upload, as it helps for the loan application.',
    ),
  ];

  LoanDocumentTimeline({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: Colors.black,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: items.length,
        itemBuilder: (context, index) {
          return LoanTimelineItem(
            item: items[index],
            isLast: index == items.length - 1,
          );
        },
      ),
    );
  }
}

class LoanTimelineItem extends StatelessWidget {
  final LoanDocumentItem item;
  final bool isLast;

  const LoanTimelineItem({
    Key? key,
    required this.item,
    this.isLast = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(
                      left: 10, bottom: 4, top: 4, right: 8),
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: 40.w,
                            height: 40.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Image.asset(
                              'assets/vectors/passport.png',
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomBuildText(
                                  text: item.title,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  caseType: '',
                                ),
                                SizedBox(height: 4.h),
                                CustomBuildText(
                                  text: item.description,
                                  fontSize: 14.sp,
                                  color: Color(0xff595959),
                                  fontWeight: FontWeight.w400,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (!isLast) ...[
                        Column(
                          children: [
                            SizedBox(height: 32.h),
                            Container(
                              height: 1.h,
                              width: double.infinity,
                              child: CustomPaint(
                                painter: DottedLinePainter(
                                  color: Colors.black.withValues(alpha: 0.3),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LoanDocumentItem {
  final String title;
  final String description;

  const LoanDocumentItem({
    required this.title,
    required this.description,
  });
}

class CircularProgressPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = Colors.grey[300]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc
    final progressPaint = Paint()
      ..color = const Color(0xFF004D40)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -1.5708, // Start from top (-90 degrees)
      3.14159, // Half circle (180 degrees)
      false,
      progressPaint,
    );

    // Text
    final textPainter = TextPainter(
      text: const TextSpan(
        text: '1 of 2',
        style: TextStyle(
          color: Colors.black,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
