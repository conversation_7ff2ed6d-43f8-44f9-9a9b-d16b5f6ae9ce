import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/calculate_loan_payment.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_payment_info_state.dart';

/// Cubit for managing loan payment information
class LoanPaymentInfoCubit extends Cubit<LoanPaymentInfoState> {
  LoanPaymentInfoCubit({
    required CalculateLoanPayment calculateLoanPayment,
  })  : _calculateLoanPayment = calculateLoanPayment,
        super(const LoanPaymentInfoInitial());

  final CalculateLoanPayment _calculateLoanPayment;

  // Retry mechanism
  Timer? _retryTimer;
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  /// Calculate payment information for selected loan options
  Future<void> calculatePaymentInfo({
    required LoanBank bank,
    required LoanProduct loanProduct,
    required UpfrontPayment upfrontPayment,
    required String productId,
    required LoanItemType loanType,
    int retryCount = 0,
  }) async {
    debugPrint(
      '💰 Calculating payment info for ${bank.name} '
      '(${upfrontPayment.percentage}% down)',
    );

    // Emit loading state
    emit(LoanPaymentInfoLoading(
      bank: bank,
      loanProduct: loanProduct,
      upfrontPayment: upfrontPayment,
      productId: productId,
      loanType: loanType,
    ));

    try {
      final result = await _calculateLoanPayment(CalculateLoanPaymentParams(
        bankId: bank.id,
        loanProductId: loanProduct.id,
        upfrontPaymentPercentage: upfrontPayment.percentage,
        productId: productId,
        interestRate:
            double.tryParse(upfrontPayment.interestRate.toString()) ?? 0.0,
        loanType: loanType,
      ));

      result.fold(
        (failure) => _handleCalculationError(
          failure,
          bank,
          loanProduct,
          upfrontPayment,
          productId,
          loanType,
          retryCount,
        ),
        (paymentInfo) => _handleCalculationSuccess(
          paymentInfo,
          bank,
          loanProduct,
          upfrontPayment,
          productId,
          loanType,
        ),
      );
    } catch (e) {
      debugPrint('❌ Unexpected error calculating payment: $e');
      _handleCalculationError(
        ServerFailure(message: e.toString()),
        bank,
        loanProduct,
        upfrontPayment,
        productId,
        loanType,
        retryCount,
      );
    }
  }

  /// Handle successful payment calculation
  void _handleCalculationSuccess(
    Map<String, dynamic> paymentInfo,
    LoanBank bank,
    LoanProduct loanProduct,
    UpfrontPayment upfrontPayment,
    String productId,
    LoanItemType loanType,
  ) {
    debugPrint('✅ Payment calculation successful');

    emit(LoanPaymentInfoLoaded(
      paymentInfo: paymentInfo,
      bank: bank,
      loanProduct: loanProduct,
      upfrontPayment: upfrontPayment,
      productId: productId,
      loanType: loanType,
      lastUpdated: DateTime.now(),
    ));
  }

  /// Handle payment calculation error with retry logic
  void _handleCalculationError(
    Failure failure,
    LoanBank bank,
    LoanProduct loanProduct,
    UpfrontPayment upfrontPayment,
    String productId,
    LoanItemType loanType,
    int retryCount,
  ) {
    final errorType = _determineErrorType(failure);

    debugPrint(
      '❌ Error calculating payment: ${failure.message} '
      '(attempt ${retryCount + 1})',
    );

    // Try to retry if possible
    if (retryCount < _maxRetryAttempts && _shouldRetry(errorType)) {
      debugPrint(
          '🔄 Retrying payment calculation in ${_retryDelay.inSeconds} seconds...');

      _retryTimer?.cancel();
      _retryTimer = Timer(_retryDelay, () {
        calculatePaymentInfo(
          bank: bank,
          loanProduct: loanProduct,
          upfrontPayment: upfrontPayment,
          productId: productId,
          loanType: loanType,
          retryCount: retryCount + 1,
        );
      });
      return;
    }

    // Emit error state
    emit(LoanPaymentInfoError(
      message: failure.message,
      errorType: errorType,
      bank: bank,
      loanProduct: loanProduct,
      upfrontPayment: upfrontPayment,
      productId: productId,
      loanType: loanType,
      canRetry: _shouldRetry(errorType),
      retryCount: retryCount,
    ));
  }

  /// Retry payment calculation
  void retryCalculation() {
    final currentState = state;
    if (currentState is LoanPaymentInfoError &&
        currentState.bank != null &&
        currentState.loanProduct != null &&
        currentState.upfrontPayment != null &&
        currentState.productId != null &&
        currentState.loanType != null) {
      calculatePaymentInfo(
        bank: currentState.bank!,
        loanProduct: currentState.loanProduct!,
        upfrontPayment: currentState.upfrontPayment!,
        productId: currentState.productId!,
        loanType: currentState.loanType!,
      );
    }
  }

  /// Clear payment information
  void clearPaymentInfo() {
    _retryTimer?.cancel();
    emit(const LoanPaymentInfoInitial());
  }

  /// Determine error type from failure
  PaymentInfoErrorType _determineErrorType(Failure failure) {
    if (failure is ServerFailure) {
      if (failure.message.toLowerCase().contains('network') ||
          failure.message.toLowerCase().contains('connection')) {
        return PaymentInfoErrorType.network;
      }
      if (failure.message.toLowerCase().contains('timeout')) {
        return PaymentInfoErrorType.timeout;
      }
      if (failure.message.toLowerCase().contains('invalid') ||
          failure.message.toLowerCase().contains('parameter')) {
        return PaymentInfoErrorType.invalidData;
      }
      return PaymentInfoErrorType.server;
    }
    return PaymentInfoErrorType.unknown;
  }

  /// Check if we should retry for this error type
  bool _shouldRetry(PaymentInfoErrorType errorType) {
    switch (errorType) {
      case PaymentInfoErrorType.network:
      case PaymentInfoErrorType.timeout:
      case PaymentInfoErrorType.server:
        return true;
      case PaymentInfoErrorType.invalidData:
      case PaymentInfoErrorType.unknown:
        return false;
    }
  }

  @override
  Future<void> close() {
    _retryTimer?.cancel();
    return super.close();
  }
}
