import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:google_fonts/google_fonts.dart';

class LoanTermsAndConditionsContainer extends StatefulWidget {
  const LoanTermsAndConditionsContainer({
    required this.bankId,
    required this.loanType,
    super.key,
  });

  final String bankId;
  final String loanType; // 'car' or 'mortgage'

  @override
  State<LoanTermsAndConditionsContainer> createState() =>
      _LoanTermsAndConditionsContainerState();
}

class _LoanTermsAndConditionsContainerState
    extends State<LoanTermsAndConditionsContainer> {
  @override
  void initState() {
    super.initState();
    // Fetch loan terms from API when widget initializes
    context.read<LoanTermsCubit>().fetchLoanTerms(
          bankId: widget.bankId,
          loanType: widget.loanType,
        );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFEFEFEF)),
      ),
      child: BlocConsumer<LoanTermsCubit, LoanTermsState>(
        listener: (context, state) {
          if (state is LoanTermsError) {
            debugPrint('❌ Loan terms error: ${state.message}');
          } else if (state is LoanTermsLoaded) {
            debugPrint('✅ Loan terms loaded successfully');
          }
        },
        builder: (context, state) {
          if (state is LoanTermsLoading) {
            return Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).primaryColor,
              ),
            );
          }

          if (state is LoanTermsLoaded) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              physics: const ClampingScrollPhysics(),
              child: HtmlWidget(
                state.content,
                textStyle: GoogleFonts.outfit(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            );
          }

          if (state is LoanTermsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade400,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load terms and conditions',
                    style: GoogleFonts.outfit(
                      color: Colors.red.shade400,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.outfit(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<LoanTermsCubit>().refreshLoanTerms(
                            bankId: widget.bankId,
                            loanType: widget.loanType,
                          );
                    },
                    child: Text(
                      'Retry',
                      style: GoogleFonts.outfit(),
                    ),
                  ),
                ],
              ),
            );
          }

          // Initial state
          return Center(
            child: Text(
              'Loading terms and conditions...',
              style: GoogleFonts.outfit(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          );
        },
      ),
    );
  }
}
