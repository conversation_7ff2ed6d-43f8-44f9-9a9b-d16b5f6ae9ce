enum TransactionType {
  bankTransfer('bank_transfer'),

  walletTransfer('wallet_transfer'),
  changeToBirr('change_to_birr'),
  loadToWallet('load_to_wallet'),
  addMoney('add_money'),
  upfrontPayment('upfront_payment'),


  merchantPayment('merchant_payment'),
  billPayment('bill_payment'),
  loanRepayment('repayment'),
  topUp('merchant_payment'),
  agentCashOut('cash_out'),

  moneyRequest('money_request'),

  utility('mini_app_transfer'),
  giftPackage('gift_package');

  final String value;
  const TransactionType(this.value);

  factory TransactionType.fromString(String value) {
    return TransactionType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => throw ArgumentError('Unknown transaction type: $value'),
    );
  }
}
