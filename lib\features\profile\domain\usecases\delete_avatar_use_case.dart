import 'dart:io';

import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/profile/domain/repositories/profile_repositories.dart';

class DeleteAvatarUseCase extends UsecaseWithoutParams<void> {
  final ProfileRepository repository;

  DeleteAvatarUseCase(this.repository);

  @override
  ResultFuture<void> call() {
    return repository.deleteAvatar();
  }
}

