import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class LoanItemCard extends StatefulWidget {
  const LoanItemCard({
    required this.loanItem,
    required this.onTap,
    super.key,
    this.isGuest = false,
  });

  final LoanItem loanItem;
  final VoidCallback onTap;
  final bool isGuest;

  @override
  State<LoanItemCard> createState() => _LoanItemCardState();
}

class _LoanItemCardState extends State<LoanItemCard> {
  int _currentImageIndex = 0;
  final CarouselSliderController _carouselController =
      CarouselSliderController();
  late List<String> _filteredUrls;

  @override
  void initState() {
    super.initState();
    _filteredUrls = _filterMediaUrls(_getImageUrls());
  }

  /// Get image URLs from the loan item
  List<String> _getImageUrls() {
    final urls = <String>[];

    // Add feature image if available
    if (widget.loanItem.featureImage.isNotEmpty) {
      urls.add(widget.loanItem.featureImage);
    }

    // Add gallery images
    for (final galleryImage in widget.loanItem.galleryImages) {
      if (galleryImage.url.isNotEmpty) {
        urls.add(galleryImage.url);
      }
    }

    return urls;
  }

  /// Filter out video URLs or other non-image formats
  List<String> _filterMediaUrls(List<String> urls) {
    return urls.where((url) {
      final lowercaseUrl = url.toLowerCase();
      return !lowercaseUrl.endsWith('.mp4') &&
          !lowercaseUrl.endsWith('.mov') &&
          !lowercaseUrl.endsWith('.avi') &&
          !lowercaseUrl.endsWith('.m3u8') &&
          !lowercaseUrl.endsWith('.wmv');
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    // If no images available after filtering, show placeholder
    if (_filteredUrls.isEmpty) {
      _filteredUrls = ['placeholder'];
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 20,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Carousel Section
            _buildImageCarousel(),
            SizedBox(height: 10.h),
            // Content Section
            _buildContent(),
          ],
        ),
      ),
    );
  }

  /// Build the image carousel section
  Widget _buildImageCarousel() {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        CarouselSlider(
          carouselController: _carouselController,
          options: CarouselOptions(
            height: 172.h,
            viewportFraction: 1,
            autoPlay: _filteredUrls.length > 1,
            autoPlayInterval: const Duration(seconds: 10),
            autoPlayCurve: Curves.easeInOut,
            onPageChanged: (index, reason) {
              setState(() {
                _currentImageIndex = index;
              });
            },
          ),
          items: _filteredUrls.map((imageUrl) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: Stack(
                  children: [
                    // Shimmer loading placeholder
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: double.infinity,
                        height: 172.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                    ),
                    // Image or placeholder
                    _buildImageWidget(imageUrl),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        // Image indicators
        if (_filteredUrls.length > 1) _buildImageIndicators(),
      ],
    );
  }

  /// Build image widget (cached network image or placeholder)
  Widget _buildImageWidget(String imageUrl) {
    if (imageUrl == 'placeholder') {
      return Container(
        width: double.infinity,
        height: 172.h,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: Icon(
            widget.loanItem.isCar ? Icons.directions_car : Icons.home,
            color: Colors.grey[400],
            size: 60.r,
          ),
        ),
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: double.infinity,
      height: 172.h,
      fit: BoxFit.cover,
      fadeInDuration: const Duration(milliseconds: 400),
      fadeOutDuration: const Duration(milliseconds: 400),
      placeholder: (context, url) => const SizedBox(),
      errorWidget: (context, url, error) => Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: Icon(
            Icons.error_outline,
            color: Colors.grey[400],
            size: 40.r,
          ),
        ),
      ),
    );
  }

  /// Build image indicators
  Widget _buildImageIndicators() {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Container(
        decoration: ShapeDecoration(
          color: const Color(0xFF2C2B34),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x4CFFFFFF),
              blurRadius: 20,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: _filteredUrls.asMap().entries.map((entry) {
            return GestureDetector(
              onTap: () => _carouselController.animateToPage(entry.key),
              child: Container(
                width: 6.0.w,
                height: 6.0.h,
                margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.h),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentImageIndex == entry.key
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.5),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Build the content section (title, price, category, specifications)
  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Title and subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBuildText(
                    text: widget.loanItem.name,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w600,
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    _getSubtitle(),
                    style: GoogleFonts.outfit(
                      fontSize: 13.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            // Category and price
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 4.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Text(
                    widget.loanItem.category.name,
                    style: GoogleFonts.outfit(
                      color: Colors.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '\$${_formatPrice(widget.loanItem.price)}',
                  style: GoogleFonts.outfit(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 12.h),
        // Dotted line separator
        CustomPaint(
          painter: DottedLinePainter(
            dashWidth: 3,
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          ),
          size: const Size(double.infinity, 1),
        ),
        SizedBox(height: 16.h),
        // Specifications
        _buildSpecifications(),
      ],
    );
  }

  /// Get subtitle based on loan item type
  String _getSubtitle() {
    if (widget.loanItem.isCar) {
      return '${widget.loanItem.manufactureYear ?? 'Unknown'} Model';
    } else if (widget.loanItem.isHouse) {
      return widget.loanItem.location?.fieldName ?? 'Location not specified';
    }
    return 'Loan Item';
  }

  /// Format price with proper formatting
  String _formatPrice(double price) {
    if (price >= 1000000) {
      return '${(price / 1000000).toStringAsFixed(1)}M';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(0)}K';
    }
    return price.toStringAsFixed(0);
  }

  /// Build specifications section based on loan item type
  Widget _buildSpecifications() {
    if (widget.loanItem.isCar) {
      return _buildCarSpecifications();
    } else if (widget.loanItem.isHouse) {
      return _buildHouseSpecifications();
    }
    return const SizedBox.shrink();
  }

  /// Build car-specific specifications
  Widget _buildCarSpecifications() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildSpecItem(
          MediaRes.manualAutoIcon,
          widget.loanItem.transmission ?? 'Unknown',
        ),
        _buildSpecItem(
          MediaRes.carSeatIcon,
          '${widget.loanItem.numberOfSeats ?? 0} Seats',
        ),
        _buildSpecItem(
          MediaRes.carPowerIcon,
          '${widget.loanItem.horsePower ?? 0} HP',
        ),
      ],
    );
  }

  /// Build house-specific specifications
  Widget _buildHouseSpecifications() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildSpecItem(
          Icons.bed,
          '${widget.loanItem.bedroom ?? 0} Beds',
        ),
        _buildSpecItem(
          Icons.bathroom,
          '${widget.loanItem.bathroom ?? 0} Baths',
        ),
        _buildSpecItem(
          Icons.square_foot,
          '${widget.loanItem.area?.toStringAsFixed(0) ?? '0'} sqft',
        ),
      ],
    );
  }

  /// Build individual specification item
  Widget _buildSpecItem(dynamic icon, String text) {
    return Row(
      children: [
        if (icon is String)
          Image.asset(
            icon,
            width: 16.w,
            height: 16.h,
            color: Colors.black87,
          )
        else if (icon is IconData)
          Icon(
            icon,
            size: 16.r,
            color: Colors.black87,
          ),
        SizedBox(width: 8.w),
        Text(
          text,
          style: GoogleFonts.outfit(
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: Colors.black,
          ),
        ),
      ],
    );
  }
}
