import 'package:equatable/equatable.dart';

/// Entity for loan payment confirmation response
class LoanPaymentConfirmation extends Equatable {
  final String id;
  final String loanId;
  final String productId;
  final String memberId;
  final String status;
  final String upfrontPaymentPercentage;
  final String interestRate;
  final String upfrontPaymentAmount;
  final String loanAmount;
  final String monthlyPayment;
  final ApplicationFeeTransaction? applicationFeeTransaction;
  final UpfrontPaymentTransaction? upfrontPaymentTransaction;
  final String startDate;
  final String endDate;
  final String totalPaid;
  final String totalPrincipalPaid;
  final String totalInterestPaid;
  final String totalPenaltiesPaid;
  final String remainingBalance;
  final String totalPayment;
  final String? lastPaymentDate;
  final String nextPaymentDate;
  final bool isOverdue;
  final int totalInstallments;
  final int paidInstallments;
  final int missedInstallments;
  final bool isAutoRepay;
  final DateTime createdAt;
  final DateTime? completedDate;
  final DateTime updatedAt;
  final DateTime? approvedAt;
  final String? documents;
  final String? approvedBy;
  final String? rejectedBy;
  final DateTime? rejectedAt;
  final String? rejectionReason;
  final DateTime? lastModifiedAt;
  final String? chassisNumber;
  final String? plateNumber;
  final String? libreNumber;
  final String? proformaNumber;
  final String? lienNumber;
  final String? housePlanNumber;
  final String? houseLienNumber;
  final String? contractNumber;
  final String? estimationAmount;
  final String? estimationDate;
  final String loanApplicationCode;

  const LoanPaymentConfirmation({
    required this.id,
    required this.loanId,
    required this.productId,
    required this.memberId,
    required this.status,
    required this.upfrontPaymentPercentage,
    required this.interestRate,
    required this.upfrontPaymentAmount,
    required this.loanAmount,
    required this.monthlyPayment,
    required this.startDate,
    required this.endDate,
    required this.totalPaid,
    required this.totalPrincipalPaid,
    required this.totalInterestPaid,
    required this.totalPenaltiesPaid,
    required this.remainingBalance,
    required this.totalPayment,
    required this.nextPaymentDate,
    required this.isOverdue,
    required this.totalInstallments,
    required this.paidInstallments,
    required this.missedInstallments,
    required this.isAutoRepay,
    required this.createdAt,
    required this.updatedAt,
    required this.loanApplicationCode,
    this.applicationFeeTransaction,
    this.upfrontPaymentTransaction,
    this.lastPaymentDate,
    this.completedDate,
    this.approvedAt,
    this.documents,
    this.approvedBy,
    this.rejectedBy,
    this.rejectedAt,
    this.rejectionReason,
    this.lastModifiedAt,
    this.chassisNumber,
    this.plateNumber,
    this.libreNumber,
    this.proformaNumber,
    this.lienNumber,
    this.housePlanNumber,
    this.houseLienNumber,
    this.contractNumber,
    this.estimationAmount,
    this.estimationDate,
  });

  @override
  List<Object?> get props => [
        id,
        loanId,
        productId,
        memberId,
        status,
        upfrontPaymentPercentage,
        interestRate,
        upfrontPaymentAmount,
        loanAmount,
        monthlyPayment,
        applicationFeeTransaction,
        upfrontPaymentTransaction,
        startDate,
        endDate,
        totalPaid,
        totalPrincipalPaid,
        totalInterestPaid,
        totalPenaltiesPaid,
        remainingBalance,
        totalPayment,
        lastPaymentDate,
        nextPaymentDate,
        isOverdue,
        totalInstallments,
        paidInstallments,
        missedInstallments,
        isAutoRepay,
        createdAt,
        completedDate,
        updatedAt,
        approvedAt,
        documents,
        approvedBy,
        rejectedBy,
        rejectedAt,
        rejectionReason,
        lastModifiedAt,
        chassisNumber,
        plateNumber,
        libreNumber,
        proformaNumber,
        lienNumber,
        housePlanNumber,
        houseLienNumber,
        contractNumber,
        estimationAmount,
        estimationDate,
        loanApplicationCode,
      ];
}

/// Entity for application fee transaction within payment confirmation
class ApplicationFeeTransaction extends Equatable {
  final double vat;
  final String loanId;
  final String status;
  final String elstRef;
  final String bankCode;
  final String bankName;
  final String loanType;
  final DateTime paidDate;
  final String senderId;
  final String billRefNo;
  final DateTime createdAt;
  final double billAmount;
  final double paidAmount;
  final String senderName;
  final String senderEmail;
  final String senderPhone;
  final String beneficiaryId;
  final String paymentMethod;
  final double serviceCharge;
  final DateTime lastModifiedAt;
  final ConfirmationPaymentDetails paymentDetails;
  final ConfirmationProductDetails productDetails;
  final String beneficiaryName;
  final String transactionType;
  final String transactionOwner;
  final String originalCurrency;
  final String paymentReference;
  final String authorizationType;
  final String walletFTNumber;
  final DateTime lastModified;

  const ApplicationFeeTransaction({
    required this.vat,
    required this.loanId,
    required this.status,
    required this.elstRef,
    required this.bankCode,
    required this.bankName,
    required this.loanType,
    required this.paidDate,
    required this.senderId,
    required this.billRefNo,
    required this.createdAt,
    required this.billAmount,
    required this.paidAmount,
    required this.senderName,
    required this.senderEmail,
    required this.senderPhone,
    required this.beneficiaryId,
    required this.paymentMethod,
    required this.serviceCharge,
    required this.lastModifiedAt,
    required this.paymentDetails,
    required this.productDetails,
    required this.beneficiaryName,
    required this.transactionType,
    required this.transactionOwner,
    required this.originalCurrency,
    required this.paymentReference,
    required this.authorizationType,
    required this.walletFTNumber,
    required this.lastModified,
  });

  @override
  List<Object> get props => [
        vat,
        loanId,
        status,
        elstRef,
        bankCode,
        bankName,
        loanType,
        paidDate,
        senderId,
        billRefNo,
        createdAt,
        billAmount,
        paidAmount,
        senderName,
        senderEmail,
        senderPhone,
        beneficiaryId,
        paymentMethod,
        serviceCharge,
        lastModifiedAt,
        paymentDetails,
        productDetails,
        beneficiaryName,
        transactionType,
        transactionOwner,
        originalCurrency,
        paymentReference,
        authorizationType,
        walletFTNumber,
        lastModified,
      ];
}

/// Entity for upfront payment transaction (can be null)
class UpfrontPaymentTransaction extends Equatable {
  // This can be expanded when we have actual data structure
  const UpfrontPaymentTransaction();

  @override
  List<Object?> get props => [];
}

/// Entity for payment details in confirmation
class ConfirmationPaymentDetails extends Equatable {
  final String currency;
  final String walletId;

  const ConfirmationPaymentDetails({
    required this.currency,
    required this.walletId,
  });

  @override
  List<Object> get props => [currency, walletId];
}

/// Entity for product details in confirmation
class ConfirmationProductDetails extends Equatable {
  final String productName;
  final double productPrice;
  final ConfirmationProductInfo productDetails;

  const ConfirmationProductDetails({
    required this.productName,
    required this.productPrice,
    required this.productDetails,
  });

  @override
  List<Object> get props => [productName, productPrice, productDetails];
}

/// Entity for detailed product information
class ConfirmationProductInfo extends Equatable {
  final String id;
  final String make;
  final String name;
  final String color;
  final String model;
  final double price;
  final int mileage;
  final ConfirmationBodyType bodyType;
  final String fuelType;
  final DateTime createdAt;
  final bool isDeleted;
  final DateTime updatedAt;
  final String driveTrain;
  final double engineSize;
  final int horsePower;
  final String featureImage;
  final String transmission;
  final String fuelEfficency;
  final List<ConfirmationGalleryImage> galleryImages;
  final int numberOfSeats;
  final String engineCapacity;
  final int manufactureYear;

  const ConfirmationProductInfo({
    required this.id,
    required this.make,
    required this.name,
    required this.color,
    required this.model,
    required this.price,
    required this.mileage,
    required this.bodyType,
    required this.fuelType,
    required this.createdAt,
    required this.isDeleted,
    required this.updatedAt,
    required this.driveTrain,
    required this.engineSize,
    required this.horsePower,
    required this.featureImage,
    required this.transmission,
    required this.fuelEfficency,
    required this.galleryImages,
    required this.numberOfSeats,
    required this.engineCapacity,
    required this.manufactureYear,
  });

  @override
  List<Object> get props => [
        id,
        make,
        name,
        color,
        model,
        price,
        mileage,
        bodyType,
        fuelType,
        createdAt,
        isDeleted,
        updatedAt,
        driveTrain,
        engineSize,
        horsePower,
        featureImage,
        transmission,
        fuelEfficency,
        galleryImages,
        numberOfSeats,
        engineCapacity,
        manufactureYear,
      ];
}

/// Entity for body type information
class ConfirmationBodyType extends Equatable {
  final String id;
  final String name;
  final String description;

  const ConfirmationBodyType({
    required this.id,
    required this.name,
    required this.description,
  });

  @override
  List<Object> get props => [id, name, description];
}

/// Entity for gallery images
class ConfirmationGalleryImage extends Equatable {
  final String url;
  final String type;

  const ConfirmationGalleryImage({
    required this.url,
    required this.type,
  });

  @override
  List<Object> get props => [url, type];
}
