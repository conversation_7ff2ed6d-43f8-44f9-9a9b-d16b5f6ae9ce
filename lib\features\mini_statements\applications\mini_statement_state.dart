// lib/features/mini_statement/presentation/bloc/mini_statement_state.dart

import 'package:equatable/equatable.dart';

abstract class MiniStatementState extends Equatable {
  const MiniStatementState();

  @override
  List<Object?> get props => [];
}

class MiniStatementInitial extends MiniStatementState {}

class MiniStatementLoading extends MiniStatementState {}

class MiniStatementLoaded extends MiniStatementState {
  final String miniStatementURL;

  const MiniStatementLoaded(this.miniStatementURL);

  @override
  List<Object?> get props => [miniStatementURL];
}

class MiniStatementError extends MiniStatementState {
  final String message;

  const MiniStatementError(this.message);

  @override
  List<Object?> get props => [message];
}
