import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_confirm_otp_response_entity.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

class LoanConfirmWithOtpUsecase extends UsecaseWithParams<
    LoanConfirmOtpResponseEntity, LoanConfirOtpTransferParams> {
  const LoanConfirmWithOtpUsecase(this._repository);
  final LoanRepository _repository;

  @override
  ResultFuture<LoanConfirmOtpResponseEntity> call(
      LoanConfirOtpTransferParams params) async {
    return _repository.confirmWithOtp(
        otpCode: params.otpCode,
        billRefNo: params.billRefNo,
        transactionType: params.transactionType);
  }
}

class LoanConfirOtpTransferParams extends Equatable {
  const LoanConfirOtpTransferParams({
    required this.otpCode,
    required this.billRefNo,
    required this.transactionType,
    this.otp,
  });

  final String otpCode;
  final String billRefNo;
  final String transactionType;
  final String? otp;

  @override
  List<Object?> get props => [otpCode, billRefNo, transactionType, otp];
}
