import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomerSupportScreen extends StatelessWidget {
  const CustomerSupportScreen({super.key});

  Future<void> _launchShortCode(BuildContext context) async {
    final phoneLaunchUri = Uri(
      scheme: 'tel',
      path: '*127#',
    );

    try {
      if (!await launchUrl(phoneLaunchUri)) {
        if (context.mounted) {
          _showErrorDialog(context, 'Could not launch phone app');
        }
      }
    } catch (e) {
      debugPrint('Error launching phone: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'Error launching phone');
      }
    }
  }

  Future<void> _launchPhone(BuildContext context) async {
    final phoneLaunchUri = Uri(
      scheme: 'tel',
      path: '+1234567890',
    );

    try {
      if (!await launchUrl(phoneLaunchUri)) {
        if (context.mounted) {
          _showErrorDialog(context, 'Could not launch phone app');
        }
      }
    } catch (e) {
      debugPrint('Error launching phone: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'Error launching phone');
      }
    }
  }

  Future<void> _launchEmail(BuildContext context) async {
    const email = '<EMAIL>';
    final emailLaunchUri = Uri(
      scheme: 'mailto',
      path: email,
      query: encodeQueryParameters({
        'subject': 'Customer Support Request',
        'body': 'Hello, I need help with...',
      }),
    );

    try {
      if (!await launchUrl(emailLaunchUri)) {
        if (context.mounted) {
          _showErrorDialog(context, 'Could not launch email client');
        }
      }
    } catch (e) {
      debugPrint('Error launching email: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'Error launching email');
      }
    }
  }

  String encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
        )
        .join('&');
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Customer Support',
          style: Theme.of(context)
              .textTheme
              .titleLarge!
              .copyWith(fontSize: 18.sp, fontWeight: FontWeight.w500),
        ),
        backgroundColor: Colors.white,
        shadowColor: Colors.black26,
      ),
      body: SafeArea(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 10.h),
        child: Column(
          children: [
            _buildContactCard(
              icon: Icons.numbers,
              title: 'Short Code',
              value: '*123#',
              context: context,
              onTap: () => _launchShortCode(context),
            ),
            const SizedBox(height: 16),
            _buildContactCard(
              icon: Icons.phone,
              title: 'Mobile number',
              value: '+1234567890',
              context: context,
              onTap: () => _launchPhone(context),
            ),
            const SizedBox(height: 16),
            _buildContactCard(
              icon: Icons.email,
              title: 'Email',
              value: '<EMAIL>',
              context: context,
              onTap: () => _launchEmail(context),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildSocialIcon(
                    context, 'assets/vectors/telegram_icon.png', () {}),
                const SizedBox(width: 24),
                _buildSocialIcon(
                    context, 'assets/vectors/facebook_icon.png', () {}),
                const SizedBox(width: 24),
                _buildSocialIcon(
                  context,
                  'assets/vectors/instagram_icon.png',
                  () {},
                ), // Using flutter_dash as Twitter placeholder
                const SizedBox(width: 24),
                _buildSocialIcon(
                  context,
                  'assets/vectors/twitter_icon.png',
                  () {},
                ), // Using camera_alt as Instagram placeholder
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'If you have any issues, suggestions, comments, or questions while using our app, feel free to contact us. ',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      )),
    );
  }

  Widget _buildContactCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String value,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            // contentPadding:  EdgeInsets.zero,
            // EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),

            CircleAvatar(
              backgroundColor: theme.primaryColor.withOpacity(0.2),
              child: Icon(
                icon,
                color: theme.primaryColor,
                size: 20.h,
              ),
            ),
            SizedBox(width: 12.w),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF000000).withOpacity(0.4),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    color: theme.primaryColor,
                    fontWeight: FontWeight.w700,
                    fontSize: 16.sp,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.h,
              color: theme.primaryColor,
            ),
            // onTap: onTap,
          ],
        ),
      ),
    );
  }

  Widget _buildSocialIcon(
    BuildContext context,
    String imageUrl,
    VoidCallback onPressed,
  ) {
    return GestureDetector(
      onTap: onPressed,
      child: Image.asset(
        imageUrl,
        width: 30,
        height: 30,
        fit: BoxFit.cover,
        color: Theme.of(context).primaryColor,
      ),
    );
  }
}
