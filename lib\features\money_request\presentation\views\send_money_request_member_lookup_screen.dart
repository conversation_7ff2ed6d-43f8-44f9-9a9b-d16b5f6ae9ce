import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_phone_field.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_money_request_member_lookup/send_money_request_member_lookup_bloc.dart';
import 'package:cbrs/features/money_request/presentation/helpers/contact_helper.dart';
import 'package:cbrs/features/money_request/presentation/widgets/contact_selector.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:phone_form_field/phone_form_field.dart';

enum SendMoneyRequestTab { phone, email }

class SendMoneyRequestMemberLookupScreen extends StatefulWidget {
  const SendMoneyRequestMemberLookupScreen({super.key});

  @override
  State<SendMoneyRequestMemberLookupScreen> createState() =>
      _SendMoneyRequestMemberLookupScreenState();
}

class _SendMoneyRequestMemberLookupScreenState
    extends State<SendMoneyRequestMemberLookupScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  bool _isValidEmail = false;
  bool _isValidPhone = false;
  bool _canLookUpMember = true;

  PhoneController phoneFieldController = PhoneController(
    initialValue: const PhoneNumber(
      isoCode: IsoCode.ET,
      nsn: '',
    ),
  );

  final FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateEmail);
    phoneFieldController.addListener(_validatePhone);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    phoneFieldController.removeListener(_validatePhone);
    phoneFieldController.dispose();

    super.dispose();
  }

  void _validateEmail() {
    if (!mounted) return;

    _isValidEmail = EmailValidator.validate(_emailController.text.trim());

    if (!_isValidEmail) {
      setState(() {});

      context.read<SendMoneyRequestMemberLookupBloc>().add(
            const RemoveMemberLookupEvent(),
          );
      return;
    }
    setState(() {});

    final contactSyncService = Get.find<ContactSyncService>();

    contactSyncService.filterWithEmail(_emailController.text);

    if (contactSyncService.searchContacts.isNotEmpty) {
      final contactData = contactSyncService.searchContacts.first;
      context.read<SendMoneyRequestMemberLookupBloc>().add(
            ContactSelectedEvent(
              id: contactData.id,
              avatar: contactData.avatar,
              lastName: contactData.lastName,
              emailAddress: contactData.email,
              firstName: contactData.firstName,
              middleName: contactData.middleName,
              phoneNumber: contactData.phoneNumber,
            ),
          );
    }
  }

  void _handleContactSelected(
    MemberLookupEntity memberInfo,
    String name,
    String phone,
    String avatar,
    bool showPreview,
  ) {
    setState(() {
      _canLookUpMember = false;
      if (phone.isNotEmpty) _isValidPhone = true;

      context.read<SendMoneyRequestMemberLookupBloc>().add(
            ContactSelectedEvent(
              id: memberInfo.id,
              avatar: memberInfo.avatar,
              lastName: memberInfo.lastName,
              emailAddress: memberInfo.emailAddress,
              firstName: memberInfo.firstName,
              middleName: memberInfo.middleName,
              phoneNumber: memberInfo.phoneNumber,
            ),
          );

      _canLookUpMember = true;
    });
  }

  void _validatePhone() {
    _isValidPhone = phoneFieldController.value.isValid();

    if (!_isValidPhone) {
      context.read<SendMoneyRequestMemberLookupBloc>().add(
            const RemoveMemberLookupEvent(),
          );
      return;
    }

    setState(() {});

    final countryCode = phoneFieldController.value.countryCode;
    final nsn = phoneFieldController.value.nsn;
    final fullPhoneNumber = '+$countryCode$nsn';

    if (_canLookUpMember) {
      if (ContactHelper.checkInContact(
        fullPhoneNumber,
        (memberInfo, name, phone, avatar, showPreview) {
          setState(() {
            _canLookUpMember = false;
            context.read<SendMoneyRequestMemberLookupBloc>().add(
                  ContactSelectedEvent(
                    id: memberInfo.id,
                    avatar: memberInfo.avatar,
                    lastName: memberInfo.lastName,
                    emailAddress: memberInfo.emailAddress,
                    firstName: memberInfo.firstName,
                    middleName: memberInfo.middleName,
                    phoneNumber: memberInfo.phoneNumber,
                  ),
                );
          });
        },
      )) {
        return;
      }
    }
  }

  String _selectedTab = 'Phone Number';
  final List<String> tabList = ['Phone Number', 'Email'];

  void onTap(String tabName) {
    if (tabName != _selectedTab) {
      setState(() {
        _selectedTab = tabName;
        _isValidEmail = false;
        _isValidPhone = false;
        if (_selectedTab == 'Email') {
          _emailController.clear();
        } else {
          phoneFieldController.value =
              const PhoneNumber(isoCode: IsoCode.ET, nsn: '');
        }
      });

      context.read<SendMoneyRequestMemberLookupBloc>().add(
            const RemoveMemberLookupEvent(),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SendMoneyRequestMemberLookupBloc,
        SendMoneyRequestState>(
      listener: (context, state) {
        if (state is SendMoneyRequestErrorState) {
          CustomToastification(context, message: state.message);
        }
      },
      buildWhen: (prev, curr) => prev != curr,
      builder: (context, state) {
        return Scaffold(
            appBar: AppBar(
              title: const Text('Send Money Request '),
            ),
            body: SafeArea(
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Padding(
                  padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const CustomPageHeader(
                                pageTitle: 'Send Money Request',
                                description:
                                    'Search for relatives or contacts to send'
                                    ' a money request.',
                              ),
                              SizedBox(height: 24.h),
                              CustomRoundedTabs(
                                onTap: onTap,
                                selectedTab: _selectedTab,
                                tabList: tabList,
                              ),
                              SizedBox(height: 16.h),
                              if (_selectedTab == 'Email')
                                _buildEmailForm
                              else
                                _buildPhoneForm,
                              if (state is MemberFetchedState) ...[
                                SizedBox(height: 16.h),
                                Container(
                                  decoration: const BoxDecoration(
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color(0x0F000000),
                                        blurRadius: 24,
                                      ),
                                    ],
                                  ),
                                  child: RecipientCard(
                                    isBirrTransfer:
                                        Get.find<GetAppThemeController>()
                                            .isBirrTheme
                                            .value,
                                    avatar: state.memberLookup.avatar,
                                    name: state.memberLookup.fullName,
                                    accountNumber: _selectedTab != 'Email'
                                        ? state.memberLookup.phoneNumber
                                        : state.memberLookup.emailAddress,
                                    borderColor: Theme.of(context)
                                        .primaryColor
                                        .withAlpha(76),
                                    onTap: () => context.pushNamed(
                                      AppRouteName
                                          .sendMoneyRequestAddMoneyScreen,
                                      extra: state.memberLookup,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      CustomRoundedBtn(
                        btnText: 'Continue',
                        isLoading: state is MemberLookupLoadingState,
                        onTap: (_selectedTab == 'Email'
                                ? _isValidEmail
                                : _isValidPhone)
                            ? () {
                                FocusScope.of(context).unfocus();

                                if (state is MemberFetchedState) {
                                  context.pushNamed(
                                    AppRouteName.sendMoneyRequestAddMoneyScreen,
                                    extra: state.memberLookup,
                                  );
                                  return;
                                }

                                if (_selectedTab == 'Email') {
                                  context
                                      .read<SendMoneyRequestMemberLookupBloc>()
                                      .add(
                                        MemberLookupEvent(
                                          emailAddress:
                                              _emailController.text.trim(),
                                        ),
                                      );
                                } else {
                                  context
                                      .read<SendMoneyRequestMemberLookupBloc>()
                                      .add(
                                    MemberLookupEvent(
                                      phoneNumber: () {
                                        final code = phoneFieldController
                                            .value.countryCode;
                                        final nsn =
                                            phoneFieldController.value.nsn;
                                        return '+$code$nsn';
                                      }(),
                                    ),
                                  );
                                }
                              }
                            : null,
                        isBtnActive: (_selectedTab == 'Email'
                            ? _isValidEmail
                            : _isValidPhone),
                      ),
                      SizedBox(
                        height: Platform.isIOS ? 24.h : 16,
                      ),
                    ],
                  ),
                ),
              ),
            ));
      },
    );
  }

  Widget get _buildEmailForm => CustomTextInput(
        hintText: 'Enter Email Address',
        inputLabel: 'Email Address',
        controller: _emailController,
        keyboardType: TextInputType.emailAddress,
      );

  Widget get _buildPhoneForm => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Phone Number',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: TransactionPhoneField(
                    controller: phoneFieldController,
                    focusNode: FocusNode(),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              ContactSelector(
                phoneController: phoneFieldController,
                onContactSelected: _handleContactSelected,
              ),
            ],
          ),
        ],
      );
}
