import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:equatable/equatable.dart';

class MyLoanVerifyOtpUseCase extends UsecaseWithParams<bool, VerifyUpfrontOtpParams> {
  const MyLoanVerifyOtpUseCase(this._repository);
  final LoanRepaymentRepository _repository;

  @override
  ResultFuture<bool> call(VerifyUpfrontOtpParams params) async {
    return _repository.confirmWithOtp(
      billRefNo: params.billRefNo,
      transactionType: params.otpFor,
      otpCode: params.otpCode,
    );
  }
}

class VerifyUpfrontOtpParams extends Equatable {
  const VerifyUpfrontOtpParams({
    required this.billRefNo,
    required this.otpFor,
    required this.otpCode,
  });
  final String billRefNo;
  final String otpFor;
  final int otpCode;

  @override
  List<Object?> get props => [billRefNo, otpFor, otpCode];
}
