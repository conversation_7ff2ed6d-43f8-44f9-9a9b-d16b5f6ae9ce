import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class RecipientNameField extends StatelessWidget {
  final TextEditingController controller;
  final bool isTouched;
  final Function(bool) onFocusChanged;
  final ThemeData theme;

  const RecipientNameField({
    super.key,
    required this.controller,
    required this.isTouched,
    required this.onFocusChanged,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: 'Recipient Name',
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: ' *',
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.red,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Focus(
          onFocusChange: onFocusChanged,
          child: CustomTextFormField(
            controller: controller,
            validator: _validateName,
            overrideValidator: true,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]')),
            ],
            style: GoogleFonts.plusJakartaSans(
              color: theme.colorScheme.onSurface,
            ),
            fillColor: theme.colorScheme.onTertiary,
            borderRadius: 8,
            maxLines: 1,
            decoration: InputDecoration(
              hintText: 'Recipient Name',
              hintStyle: GoogleFonts.outfit(
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: theme.colorScheme.onTertiary,
              contentPadding: const EdgeInsets.symmetric(
                vertical: 15,
                horizontal: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String? _validateName(String? value) {
    if (!isTouched) return null;

    if (value == null || value.isEmpty) {
      return 'Recipient name is required';
    }
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value)) {
      return 'Only letters are allowed';
    }
    return null;
  }
}
