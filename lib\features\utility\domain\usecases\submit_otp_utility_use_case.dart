import 'package:cbrs/core/utils/typedef.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/error/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/features/utility/domain/entities/utility.dart';
import 'package:cbrs/features/utility/domain/repositories/utility_repository.dart';

class SubmitOtpUtilityUseCase
    extends UsecaseWithParams<UtilityEntity, UtilityOtpParams> {
  final UtilityRepository _repository;

  const SubmitOtpUtilityUseCase(this._repository);

  @override
  ResultFuture<UtilityEntity> call(UtilityOtpParams params) async {
    return _repository.submitOtp(
      transactionType: params.transactionType,
      otp: params.otp,
      billRefNo: params.billRefNo
    
     
    );
  }
}

class UtilityOtpParams extends Equatable {
  final String transactionType;
  final String otp;
  final String billRefNo;

  const UtilityOtpParams({
    required this.transactionType,
    required this.otp,
    required this.billRefNo,
  });

  @override
  List<Object> get props => [transactionType, otp, billRefNo];
}
