import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';

/// Model for unified loan bank
class LoanBankModel extends LoanBank {
  const LoanBankModel({
    required super.id,
    required super.name,
    super.logo,
    required super.loans,
    required super.type,
  });

  factory LoanBankModel.fromJson(Map<String, dynamic> json, LoanBankType type) {
    return LoanBankModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      name: AppMapper.safeString(json['name']),
      logo: json['logo']?.toString(),
      loans: _parseLoans(json['loans']),
      type: type,
    );
  }

  /// Factory for car loan bank
  factory LoanBankModel.carLoan(Map<String, dynamic> json) {
    return LoanBankModel.fromJson(json, LoanBankType.car);
  }

  /// Factory for mortgage loan bank
  factory LoanBankModel.mortgage(Map<String, dynamic> json) {
    return LoanBankModel.fromJson(json, LoanBankType.mortgage);
  }

  static List<LoanProduct> _parseLoans(dynamic loansJson) {
    if (loansJson is List) {
      return loansJson
          .map((loan) => LoanProductModel.fromJson(loan as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      if (logo != null) 'logo': logo,
      'loans': loans.map((loan) => (loan as LoanProductModel).toJson()).toList(),
      'type': type.name,
    };
  }
}

/// Model for loan product
class LoanProductModel extends LoanProduct {
  const LoanProductModel({
    required super.id,
    required super.loanType,
    required super.loanPeriod,
    required super.upfrontPaymentOptions,
    required super.facilitationRate,
    required super.applicationFee,
  });

  factory LoanProductModel.fromJson(Map<String, dynamic> json) {
    return LoanProductModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      loanType: AppMapper.safeString(json['loanType']),
      loanPeriod: AppMapper.safeInt(json['loanPeriod']),
      upfrontPaymentOptions: _parseUpfrontPaymentOptions(json['upfrontPaymentOptions']),
      facilitationRate: AppMapper.safeString(json['facilitationRate']),
      applicationFee: AppMapper.safeString(json['applicationFee']),
    );
  }

  static List<UpfrontPayment> _parseUpfrontPaymentOptions(dynamic optionsJson) {
    if (optionsJson is List) {
      return optionsJson
          .map((option) => UpfrontPaymentModel.fromJson(option as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'loanType': loanType,
      'loanPeriod': loanPeriod,
      'upfrontPaymentOptions': upfrontPaymentOptions
          .map((option) => (option as UpfrontPaymentModel).toJson())
          .toList(),
      'facilitationRate': facilitationRate,
      'applicationFee': applicationFee,
    };
  }
}

/// Model for upfront payment option
class UpfrontPaymentModel extends UpfrontPayment {
  const UpfrontPaymentModel({
    required super.percentage,
    required super.interestRate,
  });

  factory UpfrontPaymentModel.fromJson(Map<String, dynamic> json) {
    return UpfrontPaymentModel(
      percentage: AppMapper.safeString(json['percentage']),
      interestRate: AppMapper.safeDouble(json['interestRate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'percentage': percentage,
      'interestRate': interestRate,
    };
  }
}
