import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class LoanPaymentInformationCard extends StatelessWidget {
  final Map<String, dynamic> loanInfo;

  const LoanPaymentInformationCard({
    super.key,
    required this.loanInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate,
                color: Theme.of(context).primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                'Loan Payment Information',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildInfoRow('Monthly Payment', '\$${loanInfo['monthlyPayment'] ?? 'N/A'}'),
          _buildInfoRow('Total Amount', '\$${loanInfo['totalAmount'] ?? 'N/A'}'),
          _buildInfoRow('Interest Rate', '${loanInfo['interestRate'] ?? 'N/A'}%'),
          _buildInfoRow('Processing Fee', '\$${loanInfo['processingFee'] ?? 'N/A'}'),
          if (loanInfo['loanTerm'] != null)
            _buildInfoRow('Loan Term', '${loanInfo['loanTerm']} months'),
          if (loanInfo['downPayment'] != null)
            _buildInfoRow('Down Payment', '\$${loanInfo['downPayment']}'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
