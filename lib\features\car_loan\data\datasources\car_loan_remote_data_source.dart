import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/car_loan/data/models/car_bank_model.dart';
import 'package:cbrs/features/car_loan/data/models/car_body_type_model.dart';
import 'package:cbrs/features/car_loan/data/models/car_model.dart';
import 'package:cbrs/features/car_loan/data/models/loan_payment_info_model.dart';
import 'package:cbrs/features/car_loan/data/models/car_loan_application_model.dart';
import 'package:cbrs/features/car_loan/domain/entities/application_fee_transaction.dart';
import 'package:flutter/material.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/car_loan/data/models/application_fee_transaction_model.dart';

abstract class CarLoanRemoteDataSource {
  Future<List<CarBankModel>> getCarBanks({
    required String productId,
    int page = 1,
    int limit = 10,
  });

  Future<List<CarBodyTypeModel>> getCarBodyTypes({
    int page = 1,
    int limit = 10,
  });

  Future<List<CarModel>> getCars({
    int page = 1,
    int limit = 10,
    String? bodyType,
    String? productId,
  });

  Future<LoanPaymentInfoModel> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
  });

  Future<CarLoanApplicationModel> applyCarLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    bool isAutoRepay = false,
  });

  Future<CarLoanApplicationFeeTransaction> generateApplicationTransaction({
    required String loanApplicationId,
  });

  Future<String> getLoanTerms({required String bankId});

  Future<CarModel> getCarById({
    required String carId,
  });
}

class CarLoanRemoteDataSourceImpl implements CarLoanRemoteDataSource {
  CarLoanRemoteDataSourceImpl({
    required ApiService apiService,
    required AuthLocalDataSource authLocalDataSource,
  })  : _apiService = apiService,
        _authLocalDataSource = authLocalDataSource;
  final ApiService _apiService;
  final AuthLocalDataSource _authLocalDataSource;

  Future<T> _handleApiResponse<T>(
    Future<T> Function(Map<String, dynamic>) parser,
    Future<Result<Map<String, dynamic>>> resultFuture,
  ) async {
    try {
      final result = await resultFuture;

      return result.fold(
        (data) async {
          if (data['success'] == false) {
            throw ApiException(
              message: data['message'] as String? ?? 'Operation failed',
              statusCode: 400,
            );
          }
          return parser(data);
        },
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'An unexpected error occurred: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<List<CarBankModel>> getCarBanks({
    required String productId,
    int page = 1,
    int limit = 10,
  }) async {
    return _handleApiResponse<List<CarBankModel>>(
      (data) async {
        if (data['data'] == null || data['data']['banks'] == null) {
          throw const ApiException(
            message: 'Invalid response format',
            statusCode: 500,
          );
        }

        final banksData = data['data']['banks'] as List;
        return banksData
            .map((bank) => CarBankModel.fromJson(bank as Map<String, dynamic>))
            .toList();
      },
      _apiService.get(
        ApiEndpoints.carLoanBanks,
        queryParameters: {
          'page': page,
          'limit': limit,
          'loanType': 'car',
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<List<CarBodyTypeModel>> getCarBodyTypes({
    int page = 1,
    int limit = 10,
  }) async {
    return _handleApiResponse<List<CarBodyTypeModel>>(
      (responseData) async {
        if (responseData['data'] != null &&
            responseData['data']['data'] != null &&
            responseData['data']['data']['docs'] != null) {
          final typesData = responseData['data']['data']['docs'] as List;
          return typesData
              .map(
                (type) =>
                    CarBodyTypeModel.fromJson(type as Map<String, dynamic>),
              )
              .toList();
        }

        throw const ApiException(
          message: 'Invalid response format',
          statusCode: 500,
        );
      },
      _apiService.get(
        ApiEndpoints.carBodiesPaginate,
        queryParameters: {
          'page': page,
          'limit': limit,
        },
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<List<CarModel>> getCars({
    int page = 1,
    int limit = 10,
    String? bodyType,
    String? productId,
  }) async {
    debugPrint('bodyType: $bodyType');
    debugPrint('productId: $productId');

    return _handleApiResponse<List<CarModel>>(
      (response) async {
        if (response['status'] == 200) {
          final data = response['data']['data'];
          if (data == null || data['docs'] == null) {
            throw const ApiException(
              message: 'Invalid response format',
              statusCode: 500,
            );
          }

          final carsData = data['docs'] as List;
          return carsData
              .map((car) => CarModel.fromJson(car as Map<String, dynamic>))
              .toList();
        }

        debugPrint('response: $response');
        throw ApiException(
          message: (response['message'] as String?) ?? 'Failed to fetch cars',
          statusCode: 500,
        );
      },
      _apiService.get(
        ApiEndpoints.carsPaginate,
        queryParameters: {
          'page': page,
          'limit': limit,
          if (bodyType != null) 'bodyType': bodyType,
          if (productId != null) 'productId': productId,
        },
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<LoanPaymentInfoModel> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
  }) async {
    final queryParams = {
      'productId': productId,
      'bankId': bankId,
      'upfrontPaymentPercentage': upfrontPaymentPercentage,
      'loanType': 'car',
    };

    return _handleApiResponse<LoanPaymentInfoModel>(
      (data) async {
        if (data['success'] == true) {
          return LoanPaymentInfoModel.fromJson(
            data['data'] as Map<String, dynamic>,
          );
        }
        throw ApiException(
          message: (data['message'] as String?) ??
              'Failed to calculate loan payment',
          statusCode: 400,
        );
      },
      _apiService.get(
        ApiEndpoints.carLoansPaymentInfo,
        queryParameters: queryParams,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<CarLoanApplicationModel> applyCarLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    bool isAutoRepay = false,
  }) async {
    return _handleApiResponse<CarLoanApplicationModel>(
      (response) async {
        if (response['success'] == true) {
          final data = response['data'];
          if (data == null) {
            throw const ApiException(
              message: 'Invalid response format',
              statusCode: 500,
            );
          }

          return CarLoanApplicationModel.fromJson(data as Map<String, dynamic>);
        }

        throw ApiException(
          message: (response['message'] as String?) ??
              'Failed to apply for car loan',
          statusCode: 400,
        );
      },
      _apiService.post(
        ApiEndpoints.carLoansApply,
        data: {
          'loanId': loanId,
          'productId': productId,
          'upfrontPaymentPercentage': upfrontPaymentPercentage,
          'isAutoRepay': isAutoRepay,
          'loanType': 'car',
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

// yablechelchal
// yabrekerkal
  @override
  Future<CarLoanApplicationFeeTransaction> generateApplicationTransaction({
    required String loanApplicationId,
  }) async {
    debugPrint('Generating application transaction for ID: $loanApplicationId');

    final result = await _apiService.post(
      ApiEndpoints.generateApplicationTransaction(loanApplicationId),
      parser: (data) => CarLoanApplicationFeeTransactionModel.fromJson(
        data as Map<String, dynamic>,
      ),
    );

    debugPrint("'ressssoutl");
    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );

/*

    return _handleApiResponse<CarLoanApplicationFeeTransaction>(
      (response) async {
        debugPrint(
          'Response for generate application transaction data: $response',
        );
        if (response['success'] == true) {
          if (response['data'] == null) {
            throw Exception('Invalid response format: missing data field');
          }

          final responseData = response['data'] as Map<String, dynamic>;
          debugPrint('Processing response data: $responseData');

          try {
            return CarLoanApplicationFeeTransactionModel.fromJson(responseData);
          } catch (e, stackTrace) {
            debugPrint('Error parsing response: $e');
            debugPrint('Stack trace: $stackTrace');
            rethrow;
          }
        }

        throw ApiException(
          message: (response['message'] as String?) ??
              'Failed to generate transaction',
          statusCode: 400,
        );
      },
      _apiService.post(
        ApiEndpoints.generateApplicationTransaction(loanApplicationId),
        parser: (data) => data as Map<String, dynamic>,
      ),
    );


*/
  }

  @override
  Future<String> getLoanTerms({required String bankId}) async {
    return _handleApiResponse<String>(
      (response) async {
        if (response['success'] == true) {
          return response['data']['content'] as String;
        }
        throw const ApiException(
          message: 'Failed to fetch loan terms',
          statusCode: 400,
        );
      },
      _apiService.get(
        ApiEndpoints.getLoanTerms(bankId),
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<CarModel> getCarById({
    required String carId,
  }) async {
    return _handleApiResponse<CarModel>(
      (response) async {
        if (response['status'] == 200) {
          final data = response['data'];
          if (data == null) {
            throw const ApiException(
              message: 'Invalid response format',
              statusCode: 500,
            );
          }

          return CarModel.fromJson(data as Map<String, dynamic>);
        }

        throw ApiException(
          message:
              (response['message'] as String?) ?? 'Failed to fetch car details',
          statusCode: 400,
        );
      },
      _apiService.get(
        ApiEndpoints.getCarById,
        queryParameters: {
          'id': carId,
        },
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }
}
