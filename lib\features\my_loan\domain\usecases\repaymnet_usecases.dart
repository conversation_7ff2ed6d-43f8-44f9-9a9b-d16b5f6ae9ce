import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/my_loan.dart';
import 'package:cbrs/features/my_loan/domain/entity/payment_history.dart';
import 'package:cbrs/features/my_loan/domain/entity/success_payment.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:equatable/equatable.dart';

class FetchLoanRepaymentUsecase extends UsecaseWithParams<void, FetchLoanParams> {
  final LoanRepaymentRepository repository;

  FetchLoanRepaymentUsecase(this.repository);

  @override
  ResultFuture<LoanRepaymentEntity> call(FetchLoanParams params) {
    return repository.fetchLoanRepayments(status: params.status, page: params.page);
  }
}

class FetchLoanParams extends Equatable {
  final String status;
  final int page;

  const FetchLoanParams({required this.status, required this.page});

  @override
  List<Object?> get props => [status];
}



class FetchLoanRepaymentInfoUsecase extends UsecaseWithParams<void, FetchLoanInfoParams> {
  final LoanRepaymentRepository repository;

  FetchLoanRepaymentInfoUsecase(this.repository);

  @override
  ResultFuture<LoanRepaymentInfoEntity> call(FetchLoanInfoParams params) {
    return repository.fetchLoanInfoDetail(loanId: params.loanId, months: params.months);
  }
}

class FetchLoanInfoParams extends Equatable {
  final String loanId;
  final String months;


  const FetchLoanInfoParams({required this.loanId, required this.months});

  @override
  List<Object?> get props => [loanId];
}





class fetchPaymentHistroyDetail extends UsecaseWithParams<void, FetchLoanInfoParams> {
  final LoanRepaymentRepository repository;

  fetchPaymentHistroyDetail(this.repository);

  @override
  ResultFuture<LoanPaymentHistoryEntity> call(FetchLoanInfoParams params) {
    return repository.fetchPaymentHistory(loanPaymentId: params.loanId, );
  }
}


