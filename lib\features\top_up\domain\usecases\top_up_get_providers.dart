import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_confirm_response_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_providers_entity.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_success_response_entities.dart';
import 'package:cbrs/features/top_up/domain/repositories/top_up_repositories.dart';
import 'package:equatable/equatable.dart';

class TopUpGetProviders
    extends UsecaseWithoutParams<TopUpProvidersEntity> {
  const TopUpGetProviders(this._repositores);
  final TopUpRepositories _repositores;

  @override
  ResultFuture<TopUpProvidersEntity> call() {
    return _repositores.getTopUpProviders();
  }
}
