import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/data/data_source/my_loan_remote_data_source.dart';
import 'package:cbrs/features/my_loan/data/model/loan_info_model.dart';
import 'package:cbrs/features/my_loan/data/model/loan_model.dart';
import 'package:cbrs/features/my_loan/data/model/monthly_repayment_model.dart';
import 'package:cbrs/features/my_loan/data/model/monthly_repayment_transaction.dart';
import 'package:cbrs/features/my_loan/data/model/upfront_loan_model.dart';
import 'package:cbrs/features/my_loan/data/model/upfront_transaction_model.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/my_loan.dart';
import 'package:cbrs/features/my_loan/domain/entity/payment_history.dart';
import 'package:cbrs/features/my_loan/domain/entity/success_payment.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_loan_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:dartz/dartz.dart';

class LoanRepaymentRepositoryImpl implements LoanRepaymentRepository {
  const LoanRepaymentRepositoryImpl({
    required RepaymentRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  final RepaymentRemoteDataSource _remoteDataSource;

  @override
  ResultFuture<WelcomeRepaymentLoanModel> fetchLoanRepayments({
    required String status,
    required int page,
  }) async {
    try {
      final returnData = await _remoteDataSource.fetchRepaymentLoans(
        status: status,
        page: page,
      );
      return Right(returnData);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<WelcomeRepaymentLoanInfoModel> fetchLoanInfoDetail({
    required String loanId,
    required String months,
  }) async {
    // TODO: implement fetchLoanInfo

    try {
      final returnData = await _remoteDataSource.fetchLoanInfoDetail(
        loanId: loanId,
        months: months,
      );
      return Right(returnData);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  // @override
  // ResultFuture<SuccessLoanRepaymentEntity> payMonthlyRepayment({
  //   required String loanId,
  //   required String months,
  // }) async {
  //   // TODO: implement payUpfrontOrRepayment
  //   try {
  //     final returnData = await _remoteDataSource.payMonthlyRepayment(
  //         loanId: loanId,  months: months,);
  //     return Right(returnData);
  //   } on ServerException catch (e) {
  //     return Left(ServerFailure.fromException(e));
  //   }
  // }

  @override
  ResultFuture<LoanPaymentHistoryEntity> fetchPaymentHistory({
    required String loanPaymentId,
  }) async {
    // TODO: implement fetchPaymentHistory
    // TODO: implement payUpfrontOrRepayment
    try {
      final returnData = await _remoteDataSource.fetchPaymentHistory(
        loanPaymentId: loanPaymentId,
      );
      return Right(returnData);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<UpfrontTransactionModel> generateUpfrontPayment({
    required String loanId,
  }) async {
    try {
      final returnData = await _remoteDataSource.generateUpfrontPayment(
        loanId: loanId,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<UpfrontLoanModel> confirmUprontPaymentWithPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    try {
      final returnData = await _remoteDataSource.confirmUprontPaymentWithPin(
        billRefNo: billRefNo,
        pin: pin,
        transactionType: transactionType,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<bool> confirmWithOtp({
    required String transactionType,
    required String billRefNo,
    required int otpCode,
  }) async {
    try {
      final returnData = await _remoteDataSource.confirmWithOtp(
        billRefNo: billRefNo,
        otpCode: otpCode,
        transactionType: transactionType,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<bool> resendOtp({
    required String otpFor,
    required String billRefNo,
  }) async {
    try {
      final returnData = await _remoteDataSource.resendOtp(
        billRefNo: billRefNo,
        otpFor: otpFor,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<MonthlyRepaymentTransactionModel>
      confirmMonthlyRepaymentWithPin({
    required String loanId,
    required String months,
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    try {
      final returnData = await _remoteDataSource.confirmMonthlyRepaymentWithPin(
        billRefNo: billRefNo,
        pin: pin,
        transactionType: transactionType,
        loanId: loanId,
        months: months,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<MonthlyRepaymentModel> generateMonthlyRepayment({
    required String loanId,
    required String months,
  }) async {
    try {
      final returnData = await _remoteDataSource.generateMonthlyRepayment(
        loanId: loanId,
        months: months,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }
}
