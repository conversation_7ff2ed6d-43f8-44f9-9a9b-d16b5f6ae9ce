import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class PhoneNumberField extends StatelessWidget {
  final TextEditingController controller;
  final bool isTouched;
  final Function(bool) onFocusChanged;
  final Function(String) onChanged;
  final ThemeData theme;

  const PhoneNumberField({
    super.key,
    required this.controller,
    required this.isTouched,
    required this.onFocusChanged,
    required this.onChanged,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: 'Phone Number',
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: ' *',
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.red,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Focus(
          onFocusChange: onFocusChanged,
          child: CustomTextFormField(
            controller: controller,
            textInputType: TextInputType.phone,
            maxLength: 9,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(9),
            ],
            style: GoogleFonts.plusJakartaSans(
              color: theme.colorScheme.onSurface,
            ),
            fillColor: theme.colorScheme.onTertiary,
            borderRadius: 8,
            onChange: onChanged,
            decoration: InputDecoration(
              counterText: '',
              prefixIcon: _buildCountryCodePrefix(theme),
              hintText: '912345678',
              hintStyle: GoogleFonts.plusJakartaSans(
                color: theme.colorScheme.onSurface.withOpacity(0.3),
                fontSize: 15,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: theme.colorScheme.onTertiary,
              contentPadding: const EdgeInsets.symmetric(
                vertical: 15,
                horizontal: 20,
              ),
            ),
            validator: _validatePhone,
            overrideValidator: true,
          ),
        ),
      ],
    );
  }

  Widget _buildCountryCodePrefix(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: CountryFlag.fromCountryCode(
              'ET',
              height: 20,
              width: 24,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '+251',
            style: GoogleFonts.plusJakartaSans(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            height: 24,
            width: 1,
            color: Colors.grey[300],
          ),
        ],
      ),
    );
  }

  String? _validatePhone(String? value) {
    if (!isTouched) return null;

    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    if (value.length != 9) {
      return 'Number must be exactly 9 digits';
    }
    if (isTouched && (!value.startsWith('9') && !value.startsWith('7'))) {
      return 'Number must start with 9 or 7';
    }
    return null;
  }
}
