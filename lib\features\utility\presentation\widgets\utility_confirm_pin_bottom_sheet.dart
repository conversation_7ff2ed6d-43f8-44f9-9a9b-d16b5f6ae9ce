import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';

class UtilityConfirmPinBottomSheet extends StatefulWidget {
  final Function(String pin) onPinSubmitted;
  final Function() onBiometricAuth;
  final bool isLoading;
  final bool hasBiometricSupport;
  final BiometricType? preferredBiometric;

  const UtilityConfirmPinBottomSheet({
    Key? key,
    required this.onPinSubmitted,
    required this.onBiometricAuth,
    required this.isLoading,
    required this.hasBiometricSupport,
    this.preferredBiometric,
  }) : super(key: key);

  @override
  State<UtilityConfirmPinBottomSheet> createState() =>
      _UtilityConfirmPinBottomSheetState();
}

class _UtilityConfirmPinBottomSheetState
    extends State<UtilityConfirmPinBottomSheet>
    with SingleTickerProviderStateMixin {
  final _pinController = TextEditingController();
  String _enteredPin = '';
  bool _obscurePin = true;
  late AnimationController _animationController;
  bool _isLocalLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _animationController.forward();
  }

  void _onKeyPressed(String value) {
    if (_enteredPin.length >= 6) return;

    setState(() {
      _enteredPin += value;
      _pinController.text = _enteredPin;
    });

    if (_enteredPin.length == 6) {
      widget.onPinSubmitted(_enteredPin);
    }
  }

  void _onBackspace() {
    if (_enteredPin.isEmpty) return;

    setState(() {
      _enteredPin = _enteredPin.substring(0, _enteredPin.length - 1);
      _pinController.text = _enteredPin;
    });
  }

  Widget _buildKeypadButton({
    required String label,
    required VoidCallback? onPressed,
    bool isGo = false,
  }) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 12.w),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            width: 124.w,
            height: 64.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isGo ? const Color(0xFF065234) : Colors.grey[50],
            ),
            child: Center(
              child: widget.isLoading && isGo
                  ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
                  : Text(
                label,
                style: GoogleFonts.outfit(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                  color: isGo ? Colors.white : Colors.black,
                ),
              ),
            ),
          ),
        ),
      ).animate().scale(duration: 200.ms),
    );
  }

  Widget _buildNumericKeypad() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          [1, 2, 3],
          [4, 5, 6],
          [7, 8, 9],
          ['⌫', 0, '→'],
        ].map((row) {
          return Padding(
            padding: EdgeInsets.only(bottom: 13.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: row.map((item) {
                if (item == '⌫') {
                  return _buildKeypadButton(
                    label: item.toString(),
                    onPressed: _onBackspace,
                  );
                } else if (item == '→') {
                  return _buildKeypadButton(
                    label: _enteredPin.length == 6 ? '' : item.toString(),
                    onPressed: _enteredPin.length == 6
                        ? null
                        : () => _onKeyPressed(item.toString()),
                    isGo: true,
                  );
                } else {
                  return _buildKeypadButton(
                    label: item.toString(),
                    onPressed: () => _onKeyPressed(item.toString()),
                  );
                }
              }).toList(),
            ),
          );
        }).toList(),
      ),
    ).animate().fadeIn(duration: 800.ms, delay: 400.ms);
  }

  Widget _buildPinDisplay() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(6, (index) {
        bool isFilled = index < _enteredPin.length;
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 6.w),
          width: 48.w,
          height: 48.w,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              isFilled ? (_obscurePin ? '*' : _enteredPin[index]) : '',
              style: GoogleFonts.outfit(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      }),
    )
        .animate(controller: _animationController)
        .fadeIn(duration: 600.ms)
        .slideY(begin: 0.2, end: 0);
  }

  Widget _buildBiometricButton() {
    if (!widget.hasBiometricSupport || widget.preferredBiometric == null) {
      return const SizedBox.shrink();
    }

    return Container(
      width: 60.w,
      height: 60.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[50],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            widget.preferredBiometric == BiometricType.face
                ? MediaRes.faceIdIcon
                : MediaRes.fingerPrintIcon,
            width: 40.w,
            height: 40.h,
            color: widget.isLoading
                ? const Color(0xFF065234).withOpacity(0.5)
                : const Color(0xFF065234),
          ),
          if (widget.isLoading)
            const CircularProgressIndicator(
              color: Color(0xFF065234),
              strokeWidth: 2,
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 32.h,
        bottom: MediaQuery.of(context).viewInsets.bottom + 32.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(32.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Enter PIN to Verify Transfer',
            style: GoogleFonts.outfit(
              fontSize: 24.sp,
              fontWeight: FontWeight.w700,
            ),
          )
              .animate(controller: _animationController)
              .fadeIn(duration: 600.ms)
              .slideY(begin: 0.2, end: 0),
          SizedBox(height: 8.h),
          Text(
            'Please enter your PIN to verify this transfer.',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          )
              .animate(controller: _animationController)
              .fadeIn(duration: 600.ms, delay: 200.ms)
              .slideY(begin: 0.2, end: 0),
          SizedBox(height: 32.h),
          _buildPinDisplay(),
          TextButton(
            onPressed: () => setState(() => _obscurePin = !_obscurePin),
            child: Text(
              _obscurePin ? 'Show PIN' : 'Hide PIN',
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: const Color(0xFF065234),
                fontWeight: FontWeight.w500,
              ),
            ),
          )
              .animate(controller: _animationController)
              .fadeIn(duration: 600.ms, delay: 400.ms)
              .slideY(begin: 0.2, end: 0),
          SizedBox(height: 24.h),
          GestureDetector(
            onTap: widget.isLoading ? null : _handleBiometricAuth,
            child: _buildBiometricButton(),
          ),
          SizedBox(height: 24.h),
          _buildNumericKeypad(),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _handleBiometricAuth() async {
    if (widget.isLoading || _isLocalLoading) return;

    try {
      setState(() => _isLocalLoading = true);

      final deviceController = Get.find<DeviceCheckController>();
      final storedPin = await deviceController.getStoredPin();

      if (storedPin == null) {
        setState(() => _isLocalLoading = false);
        return;
      }

      final authLocalDataSource = context.read<AuthLocalDataSource>();
      final success = await authLocalDataSource.authenticateWithBiometrics();

      if (success && mounted) {
        setState(() {
          _enteredPin = storedPin;
          _pinController.text = storedPin;
        });
        widget.onPinSubmitted(_enteredPin);
      }
    } catch (e) {
      debugPrint('Biometric auth error: $e');
    } finally {
      if (mounted) {
        setState(() => _isLocalLoading = false);
      }
    }
  }
}
