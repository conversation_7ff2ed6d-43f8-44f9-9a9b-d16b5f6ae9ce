import 'package:cbrs/features/utility/domain/entities/utility_transaction.dart';
import 'package:dartz/dartz.dart';
import 'package:cbrs/core/error/failures.dart';
import 'package:cbrs/features/utility/domain/entities/payment_request.dart';

abstract class UtilityPaymentRepository {
  // Future<
  //     Either<
  //         Failure,
  //         // dynamic
  //        UtilityTransaction>> processPayment(
  //     // UtilityPaymentRequest
  //     dynamic request);


  ///1. fetch utitlity
  ///2. create order
  ///3. confirm payment
  ///
}
