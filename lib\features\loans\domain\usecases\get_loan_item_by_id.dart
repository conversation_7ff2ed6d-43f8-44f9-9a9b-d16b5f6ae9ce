import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

/// Use case for fetching a single loan item by ID
class GetLoanItemById
    extends UsecaseWithParams<LoanItem, GetLoanItemByIdParams> {
  const GetLoanItemById(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<LoanItem> call(GetLoanItemByIdParams params) async {
    return repository.getLoanItemById(
      itemId: params.itemId,
      loanType: params.loanType,
    );
  }
}

/// Parameters for getting a loan item by ID
class GetLoanItemByIdParams extends Equatable {
  const GetLoanItemByIdParams({
    required this.itemId,
    required this.loanType,
  });

  final String itemId;
  final LoanItemType loanType;

  @override
  List<Object?> get props => [itemId, loanType];
}
