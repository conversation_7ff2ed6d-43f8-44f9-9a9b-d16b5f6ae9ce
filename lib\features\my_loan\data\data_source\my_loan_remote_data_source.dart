import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/my_loan/data/model/loan_info_model.dart';
import 'package:cbrs/features/my_loan/data/model/loan_model.dart';
import 'package:cbrs/features/my_loan/data/model/monthly_repayment_model.dart';
import 'package:cbrs/features/my_loan/data/model/monthly_repayment_transaction.dart';
import 'package:cbrs/features/my_loan/data/model/payment_history_model.dart';
import 'package:cbrs/features/my_loan/data/model/success_payment_model.dart';
import 'package:cbrs/features/my_loan/data/model/upfront_loan_model.dart';
import 'package:cbrs/features/my_loan/data/model/upfront_transaction_model.dart';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

abstract class RepaymentRemoteDataSource {
  Future<WelcomeRepaymentLoanModel> fetchRepaymentLoans({
    required String status,
    required int page,
  });

  Future<WelcomeRepaymentLoanInfoModel> fetchLoanInfoDetail({
    required String loanId,
    required String months,
  });

  Future<WelcomeSuccessLoanRepayment> payMonthlyRepayment({
    required String loanId,
    required String months,
  });

  Future<UpfrontTransactionModel> generateUpfrontPayment({
    required String loanId,
  });

  Future<MonthlyRepaymentModel> generateMonthlyRepayment({
    required String loanId,
    required String months,
  });

  Future<MonthlyRepaymentTransactionModel> confirmMonthlyRepaymentWithPin({
    required String loanId,
    required String months,
    required String transactionType,
    required String billRefNo,
    required String pin,
  });

  Future<UpfrontLoanModel> confirmUprontPaymentWithPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  });

  Future<bool> confirmWithOtp({
    required String transactionType,
    required String billRefNo,
    required int otpCode,
  });
  Future<bool> resendOtp({
    required String otpFor,
    required String billRefNo,
  });

  Future<WelcomePaymentHistoryRepayment> fetchPaymentHistory({
    required String loanPaymentId,
  });
}

class RepaymentRemoteDataSourceImpl implements RepaymentRemoteDataSource {
  RepaymentRemoteDataSourceImpl({
    required ApiService apiService,
    required Box<dynamic> authBox,
  })  : _apiService = apiService,
        _authBox = authBox;
  final ApiService _apiService;

  final Box<dynamic> _authBox;
  String? _cachedToken;

  @override
  Future<WelcomeRepaymentLoanModel> fetchRepaymentLoans({
    required String status,
    required int page,
  }) async {
    try {
      debugPrint(
        'Remote fetch MY LOANS🎗️ () :: loan status $status current page $page',
      );
      final queryParams = <String, dynamic>{
        'status': status.toUpperCase(),
        'page': page,
      };

      final result = await _apiService.get(
        ApiEndpoints.fetchLoans,
        queryParameters: queryParams,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData = WelcomeRepaymentLoanModel.fromJson(responseData);

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );

      /*

      final userId = await getUserId();
      var response = await _safeApiCall<DataMap>(() => _dio.get<DataMap>(
          ApiEndpoints.fetchLoans(userId),
          queryParameters: queryParams));
      debugPrint(
          ' yes 🎀🎊 \nRemote - fetch Loans() resposne has a response for page user id $userId ${page} $response  \n\n 🎊');
      if (response.data != null) {
        var returnData = WelcomeRepaymentLoanModel.fromJson(response.data!);

        return returnData;
      } else {
        throw ApiException(
          message: 'Empty response from server',
          statusCode: response.statusCode ?? 500,
        );
      }


      */
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Error fetching loans',
        statusCode: 500,
      );
    }
  }

  ///loanID -- loan payment ID not loan Id
  @override
  Future<WelcomeRepaymentLoanInfoModel> fetchLoanInfoDetail({
    required String loanId,
    required String months,
  }) async {
    try {
      debugPrint(
        'Remote fetch MY LOANS INFO DETAILS🎗️ () :: loan ID $loanId',
      );

      final queryParams = <String, dynamic>{'months': months};

      final result = await _apiService.get(
        ApiEndpoints.getLoanInfoDetails(loanId),
        queryParameters: queryParams,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData =
              WelcomeRepaymentLoanInfoModel.fromJson(responseData);

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );

/*
      final response = await _safeApiCall<DataMap>(() => _dio.get<DataMap>(
          ApiEndpoints.getLoanInfoDetails(loanId),
          queryParameters: queryParams,),);
      debugPrint(
          ' yes 🎀 \nRemote - fetch getLoanInfoDetails() resposne $response \n\n 🎊',);
      if (response.data != null && response.statusCode == 200) {
        final returnData = WelcomeRepaymentLoanInfoModel.fromJson(response.data!);

        return returnData;
        
      } else {
        throw ApiException(
          message: 'Empty response from server',
          statusCode: response.statusCode ?? 500,
        );
      }
      */
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Error fetching loans',
        statusCode: 500,
      );
    }
  }

  /// 👑this method pays upfront or repayment
  @override
  Future<WelcomeSuccessLoanRepayment> payMonthlyRepayment({
    required String loanId,
    required String months,
  }) async {
    try {
      debugPrint(
        'Remote pay updfornt or repayment with : $loanId  current months $months',
      );
      final queryParams = <String, dynamic>{'months': months};

      final result = await _apiService.post(
        ApiEndpoints.getRepayment(loanId),
        queryParameters: queryParams,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData = WelcomeSuccessLoanRepayment.fromJson(responseData);

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
/*
      final response = await _safeApiCall<DataMap>(() => _dio.post<DataMap>(
            isRepayment
                ? ApiEndpoints.getRepayment(loanId)
                : ApiEndpoints.getPayUpfront(loanId),
            queryParameters: queryParams,
            options: DioConfig.getDefaultOptions(),
          ),);
      debugPrint(
          ' yes 🎀 \nRemote - pay upfront or repayment with resposne $response \n\n 🎊',);

      if (response.data != null && response.statusCode == 200) {
        final returnData = WelcomeSuccessLoanRepayment.fromJson(response.data!);

        return returnData;
      } else {
        throw Exception(response.data?['message'] ?? 'Error in paying loans');
      }
      */
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      debugPrint('Catch error in repayment $e');
      throw Exception(e);
    }
  }

  @override
  Future<UpfrontTransactionModel> generateUpfrontPayment({
    required String loanId,
  }) async {
    try {
      debugPrint(
        'Remote pay updfornt $loanId isRepayment ',
      );

      final result = await _apiService.post(
        ApiEndpoints.getPayUpfront(loanId),
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData = UpfrontTransactionModel.fromJson(
            responseData['data'] as Map<String, dynamic>,
          );

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      debugPrint('Catch error in repayment $e');
      throw const ApiException(
        message: 'Failed to pay upfront payment',
        statusCode: 500,
      );
    }
  }

  @override
  Future<WelcomePaymentHistoryRepayment> fetchPaymentHistory({
    required String loanPaymentId,
  }) async {
    try {
      debugPrint('Remote Fetch Payment History : $loanPaymentId');

      final result = await _apiService.get(
        ApiEndpoints.getPaymentHistory(loanPaymentId),
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData =
              WelcomePaymentHistoryRepayment.fromJson(responseData);

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
/*
      final response = await _safeApiCall<DataMap>(() => _dio.get<DataMap>(
            ApiEndpoints.getPaymentHistory(loanPaymentId),
          ),);
      debugPrint(
          ' yes 🎀 \nRemote - pay updfornt or repayment with resposne response \n\n 🎊',);
      if (response.data != null) {
        final returnData =
            WelcomePaymentHistoryRepayment.fromJson(response.data!);

        return returnData;
      } else {
        throw ApiException(
          message: 'Empty response from server',
          statusCode: response.statusCode ?? 500,
        );
      }
      */
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Error fetching loans',
        statusCode: 500,
      );
    }
  }

  @override
  Future<UpfrontLoanModel> confirmUprontPaymentWithPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    try {
      debugPrint('Remote Fetch Payment History : confirmUprontPaymentWithPin');

      final postData = {
        'transactionType': transactionType,
        'billRefNo': billRefNo,
        'pin': pin,
      };

      debugPrint("${postData.toString()}");

      final result = await _apiService.post(
        ApiEndpoints.confirmLoanPayment,
        data: postData,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData = UpfrontLoanModel.fromJson(responseData);

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    }
  }

  @override
  Future<bool> confirmWithOtp({
    required String transactionType,
    required String billRefNo,
    required int otpCode,
  }) async {
    try {
      debugPrint('Remote Fetch Payment History : confirmUprontPaymentWithPin');

      final postData = {
        'transactionType': transactionType,
        'billRefNo': billRefNo,
        'otpCode': otpCode,
      };

      final result = await _apiService.post(
        ApiEndpoints.confirmLoanPayment, // todo
        data: postData,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          return true;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    }
  }

  @override
  Future<bool> resendOtp({
    required String otpFor,
    required String billRefNo,
  }) async {
    try {
      debugPrint('Remote Fetch Payment History : confirmUprontPaymentWithPin');

      final postData = {
        'otpFor': otpFor,
        'billRefNo': billRefNo,
      };

      final result = await _apiService.post(
        ApiEndpoints.confirmLoanPayment, // todo
        data: postData,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          return true;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    }
  }

  @override
  Future<MonthlyRepaymentTransactionModel> confirmMonthlyRepaymentWithPin({
    required String loanId,
    required String months,
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    try {
      debugPrint(
        'Remote confirmMonthlyRepaymentWithPin : $loanId  current months $months',
      );
      final postData = {
        'transactionType': transactionType,
        'billRefNo': billRefNo,
        'pin': pin,
        'months': months,
        'loanPaymentId': loanId
      };

      final result = await _apiService.post(
        ApiEndpoints.confirmLoanPayment,
        data: postData,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData =
              MonthlyRepaymentTransactionModel.fromJson(responseData);

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    }
  }

  @override
  Future<MonthlyRepaymentModel> generateMonthlyRepayment({
    required String loanId,
    required String months,
  }) async {
    try {
      debugPrint(
        'Remote pay updfornt or repayment with : $loanId  current months $months',
      );
      final queryParams = <String, dynamic>{'months': months};

      final result = await _apiService.post(
        ApiEndpoints.getRepayment(loanId),
        queryParameters: queryParams,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;
          final returnData = MonthlyRepaymentModel.fromJson(responseData);

          return returnData;
        },
        (error) => throw ApiException(
          message: 'Failed to fetch transactions: ${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    }
  }
}
