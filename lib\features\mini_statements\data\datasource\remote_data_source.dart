import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';

abstract class MiniStatementRemoteDataSource {
  Future<String> generateMiniStatements({
    required String startDate,
    required String endDate,
    required String currency,
  });
}

class MiniStatementRemoteDataSourceImpl
    implements MiniStatementRemoteDataSource {
  MiniStatementRemoteDataSourceImpl({required ApiService apiService})
      : _apiService = apiService;

  final ApiService _apiService;

  @override
  Future<String> generateMiniStatements({
    required String startDate,
    required String endDate,
    required String currency,
  }) async {
    try {
      final queryParams = {
        'startDate': startDate,
        'endDate': endDate,
        'currency': currency,
      };

      final result = await _apiService.get(
        ApiEndpoints.miniStatements,
        queryParameters: queryParams,
        parser: (data) => data,
      );

      return result.fold(
        (data) {
          final responseData = data as Map<String, dynamic>;

          return responseData['miniStatementURL'] as String;
        },
        (error) => throw ApiException(
          message: '${error.message}',
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Error fetching loans',
        statusCode: 500,
      );
    }
  }
}
