import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:cbrs/core/common/widgets/custom_row_transaction.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/utility/domain/entities/utility_transaction.dart';

class UtilityTransactionModal extends StatelessWidget {
  final UtilityTransaction transaction;
  final VoidCallback? onClose;

  const UtilityTransactionModal({
    Key? key,
    required this.transaction,
    this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with drag indicator
          Container(
            padding: EdgeInsets.symmetric(vertical: 12.h),
            child: Center(
              child: Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
            ),
          ),

          // Transaction Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Transaction Details',
                      style: GoogleFonts.outfit(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                    ),
                    IconButton(
                      onPressed: onClose,
                      icon: Icon(
                        Icons.close,
                        size: 24.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        CurrencyFormatter.formatBalance(
                          amount: transaction.total,
                          currency: 'ETB',
                          showSymbol: true,
                        ),
                        style: GoogleFonts.outfit(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Transaction Details
          Padding(
            padding: EdgeInsets.all(24.w),
            child: Column(
              children: [
                CustomRowTransaction(
                  label: 'Transaction Type',
                  value: transaction.transactionType,
                ),
                CustomRowTransaction(
                  label: 'Recipient',
                  value: transaction.recipientName,
                ),
                CustomRowTransaction(
                  label: 'Date',
                  value:
                      DateFormat('MMM dd, yyyy HH:mm').format(transaction.date),
                ),

                SizedBox(height: 16.h),
                CustomPaint(
                  painter: DottedLinePainter(
                    color: Colors.grey[300]!,
                  ),
                  size: Size(double.infinity, 1.h),
                ),
                SizedBox(height: 16.h),

                // Amount Breakdown
                CustomRowTransaction(
                  label: 'Amount',
                  value: CurrencyFormatter.formatBalance(
                    amount: transaction.amount,
                    currency: 'ETB',
                    showSymbol: true,
                  ),
                ),
                CustomRowTransaction(
                  label: 'Service Fee',
                  value: CurrencyFormatter.formatBalance(
                    amount: transaction.serviceFee,
                    currency: 'ETB',
                    showSymbol: true,
                  ),
                ),
                CustomRowTransaction(
                  label: 'VAT',
                  value: CurrencyFormatter.formatBalance(
                    amount: transaction.vat,
                    currency: 'ETB',
                    showSymbol: true,
                  ),
                ),

                SizedBox(height: 16.h),
                CustomPaint(
                  painter: DottedLinePainter(
                    color: Colors.grey[300]!,
                  ),
                  size: Size(double.infinity, 1.h),
                ),
                SizedBox(height: 16.h),

                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Amount',
                      style: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      CurrencyFormatter.formatBalance(
                        amount: transaction.total,
                        currency: 'ETB',
                        showSymbol: true,
                      ),
                      style: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
