import 'package:hive/hive.dart';

part 'recent_transaction_hive.g.dart';

@HiveType(typeId: 0)
class RecentTransactionHive extends HiveObject {
  @HiveField(0)
  final String bankId;

  @HiveField(1)
  final String recipientName;

  @HiveField(2)
  final String accountNumber;

  @HiveField(3)
  final DateTime createdAt;

  RecentTransactionHive({
    required this.bankId,
    required this.recipientName,
    required this.accountNumber,
    required this.createdAt,
  });

  String get uniqueKey => '$bankId|$recipientName|$accountNumber';
}
