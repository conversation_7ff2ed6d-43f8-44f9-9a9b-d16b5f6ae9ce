import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/mini_apps/presentation/views/Payment_permission_screen.dart';
import 'package:cbrs/features/mini_apps/presentation/views/mini_apps_webview.dart';
import 'package:cbrs/core/constants/mini_apps_urls.dart';
import 'package:cbrs/features/mini_apps/domain/mini_app_permission.dart';

class MiniAppsScreen extends StatelessWidget {
  const MiniAppsScreen({super.key});

  void _showPaymentPermissionModal(BuildContext context, AppModel app) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => PaymentPermissionModal(
        appName: app.name,
        amount: '1,500.00 ETB',
        walletBalance: 'ETB 20,000.00',
        onNext: () {
          Navigator.pop(context);
          // Add navigation logic here for the next screen
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'Mini Apps',
          style: GoogleFonts.outfit(fontSize: 18.sp),
        ),
        titleSpacing: 22.w,
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 20.h),
              Text(
                'Mini Apps',
                style: GoogleFonts.outfit(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 20.h),
              _buildAppGrid(context, miniApps),
              SizedBox(height: 30.h),
              Text(
                '3Click Shop Apps',
                style: GoogleFonts.outfit(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 20.h),
              _buildAppGrid(context, shopApps),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppGrid(BuildContext context, List<AppModel> apps) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 122.w / 128.h,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
      ),
      itemCount: apps.length,
      itemBuilder: (context, index) {
        return _buildAppItem(context, apps[index]);
      },
    );
  }

  Widget _buildAppItem(BuildContext context, AppModel app) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MiniAppsWebView(
              url: app.url,
              appName: app.name,
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0x14000000),
              blurRadius: 24,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 56.w,
              height: 56.h,
              padding: EdgeInsets.all(8.w),
              child: Image.asset(
                app.iconPath,
                width: 44.w,
                height: 44.h,
                fit: BoxFit.contain,
              ),
            ),
            SizedBox(height: 8.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text(
                app.name,
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1A1A1A),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AppModel {
  final String name;
  final String iconPath;
  final String url;
  final MiniAppPermission permissions;

  AppModel({
    required this.name,
    required this.iconPath,
    required this.url,
    required this.permissions,
  });
}

final List<AppModel> miniApps = [
  AppModel(
    name: 'GuzoGo',
    iconPath: 'assets/icons/guzo.png',
    url: MiniAppsUrls.guzoGoUrl,
    permissions: const MiniAppPermission(requiresPayment: true),
  ),
  AppModel(
    name: 'TeleTv',
    iconPath: 'assets/icons/teletv.png',
    url: MiniAppsUrls.teletvUrl,
    permissions: const MiniAppPermission(requiresPayment: true),
  ),
  AppModel(
    name: 'Adika',
    iconPath: 'assets/icons/adika.png',
    url: MiniAppsUrls.adikaUrl,
    permissions: const MiniAppPermission(
      requiresLocation: true,
      requiresPayment: true,
    ),
  ),
  AppModel(
    name: 'Z-Mall',
    iconPath: 'assets/icons/zmall.png',
    url: MiniAppsUrls.zmallUrl,
    permissions: const MiniAppPermission(requiresPayment: true),
  ),
  AppModel(
    name: 'Muyalogy',
    iconPath: 'assets/icons/muyalogy.png',
    url: '',
    permissions: const MiniAppPermission(),
  ),
  AppModel(
    name: 'WebSprix',
    iconPath: 'assets/icons/websprix.png',
    url: '',
    permissions: const MiniAppPermission(),
  ),
  AppModel(
    name: 'Hulu Beje',
    iconPath: 'assets/icons/hulubeje.png',
    url: '',
    permissions: const MiniAppPermission(),
  ),
  AppModel(
    name: 'Ride',
    iconPath: 'assets/icons/ride.png',
    url: '',
    permissions: const MiniAppPermission(),
  ),
  AppModel(
    name: 'Kuraz Tech',
    iconPath: 'assets/icons/kuraz.png',
    url: '',
    permissions: const MiniAppPermission(),
  ),
  AppModel(
    name: 'ichereta',
    iconPath: 'assets/icons/ichereta.png',
    url: '',
    permissions: const MiniAppPermission(),
  ),
];

final List<AppModel> shopApps = [
  AppModel(
    name: 'Queens Supermarket',
    iconPath: 'assets/icons/queens.png',
    url: MiniAppsUrls.queensSupermarketUrl,
    permissions: const MiniAppPermission(),
  ),
  AppModel(
    name: 'Gelagle Market',
    iconPath: 'assets/icons/gelagle.png',
    url: MiniAppsUrls.gelagleMarketUrl,
    permissions: const MiniAppPermission(),
  ),
];
