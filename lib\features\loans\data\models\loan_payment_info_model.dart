import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_info.dart';

/// Model for unified loan payment info
class LoanPaymentInfoModel extends LoanPaymentInfo {
  const LoanPaymentInfoModel({
    required super.loanId,
    required super.productDetails,
    required super.paymentDetails,
    required super.loanDetails,
  });

  factory LoanPaymentInfoModel.fromJson(Map<String, dynamic> json) {
    return LoanPaymentInfoModel(
      loanId: AppMapper.safeString(json['loanId']),
      productDetails: ProductDetailsModel.fromJson(
        json['productDetails'] as Map<String, dynamic>,
      ),
      paymentDetails: PaymentDetailsModel.fromJson(
        json['paymentDetails'] as Map<String, dynamic>,
      ),
      loanDetails: LoanDetailsModel.fromJson(
        json['loanDetails'] as Map<String, dynamic>,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loanId': loanId,
      'productDetails': (productDetails as ProductDetailsModel).toJson(),
      'paymentDetails': (paymentDetails as PaymentDetailsModel).toJson(),
      'loanDetails': (loanDetails as LoanDetailsModel).toJson(),
    };
  }
}

/// Model for payment details
class PaymentDetailsModel extends PaymentDetails {
  const PaymentDetailsModel({
    required super.upfrontPaymentAmount,
    required super.upfrontPaymentPercentage,
    required super.upfrontFacilitationFee,
    required super.loanAmount,
    required super.monthlyPayment,
    required super.monthlyFacilitationFee,
    required super.totalMonthlyPayment,
    required super.totalPayment,
    required super.totalInterest,
    required super.interestRate,
  });

  factory PaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return PaymentDetailsModel(
      upfrontPaymentAmount: AppMapper.safeDouble(json['upfrontPaymentAmount']),
      upfrontPaymentPercentage: AppMapper.safeString(json['upfrontPaymentPercentage']),
      upfrontFacilitationFee: AppMapper.safeDouble(json['upfrontFacilitationFee']),
      loanAmount: AppMapper.safeDouble(json['loanAmount']),
      monthlyPayment: AppMapper.safeDouble(json['monthlyPayment']),
      monthlyFacilitationFee: AppMapper.safeDouble(json['monthlyFacilitationFee']),
      totalMonthlyPayment: AppMapper.safeDouble(json['totalMonthlyPayment']),
      totalPayment: AppMapper.safeString(json['totalPayment']),
      totalInterest: AppMapper.safeString(json['totalInterest']),
      interestRate: AppMapper.safeDouble(json['interestRate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'upfrontPaymentAmount': upfrontPaymentAmount,
      'upfrontPaymentPercentage': upfrontPaymentPercentage,
      'upfrontFacilitationFee': upfrontFacilitationFee,
      'loanAmount': loanAmount,
      'monthlyPayment': monthlyPayment,
      'monthlyFacilitationFee': monthlyFacilitationFee,
      'totalMonthlyPayment': totalMonthlyPayment,
      'totalPayment': totalPayment,
      'totalInterest': totalInterest,
      'interestRate': interestRate,
    };
  }
}

/// Model for loan details
class LoanDetailsModel extends LoanDetails {
  const LoanDetailsModel({
    required super.loanPeriod,
    required super.applicationFee,
    required super.facilitationRate,
    required super.penaltyStructure,
    required super.gracePeriodInDays,
    required super.amortizationType,
    required super.dailyPenaltyFee,
  });

  factory LoanDetailsModel.fromJson(Map<String, dynamic> json) {
    return LoanDetailsModel(
      loanPeriod: AppMapper.safeInt(json['loanPeriod']),
      applicationFee: json['applicationFee'] as Map<String, dynamic>? ?? {},
      facilitationRate: json['facilitationRate'] as Map<String, dynamic>? ?? {},
      penaltyStructure: json['penaltyStructure'] as Map<String, dynamic>? ?? {},
      gracePeriodInDays: AppMapper.safeInt(json['gracePeriodInDays']),
      amortizationType: AppMapper.safeString(json['amortizationType']),
      dailyPenaltyFee: AppMapper.safeDouble(json['dailyPenaltyFee']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loanPeriod': loanPeriod,
      'applicationFee': applicationFee,
      'facilitationRate': facilitationRate,
      'penaltyStructure': penaltyStructure,
      'gracePeriodInDays': gracePeriodInDays,
      'amortizationType': amortizationType,
      'dailyPenaltyFee': dailyPenaltyFee,
    };
  }
}

/// Model for product details
class ProductDetailsModel extends ProductDetails {
  const ProductDetailsModel({
    required super.productId,
    required super.productName,
    required super.productDescription,
  });

  factory ProductDetailsModel.fromJson(Map<String, dynamic> json) {
    return ProductDetailsModel(
      productId: AppMapper.safeString(json['productId']),
      productName: AppMapper.safeString(json['productName']),
      productDescription: AppMapper.safeString(json['productDescription']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'productDescription': productDescription,
    };
  }
}
