import 'package:equatable/equatable.dart';

/// Unified application fee transaction entity for all loan types
class LoanApplicationFeeTransaction extends Equatable {
  final String elstRef;
  final String billRefNo;
  final String beneficiaryId;
  final String beneficiaryName;
  final String senderName;
  final String senderPhone;
  final String senderEmail;
  final String bankName;
  final String bankCode;
  final String senderId;
  final String transactionOwner;
  final String authorizationType;
  final String status;
  final double vat;
  final double serviceCharge;
  final String originalCurrency;
  final double billAmount;
  final DateTime createdAt;
  final DateTime lastModifiedAt;
  final String loanId;
  final String loanType;
  final String transactionType;
  final TransactionProductDetails productDetails;
  final DateTime paidDate;
  final String paymentMethod;
  final double paidAmount;
  final PaymentDetails paymentDetails;
  final String paymentReference;

  const LoanApplicationFeeTransaction({
    required this.elstRef,
    required this.billRefNo,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.senderName,
    required this.senderPhone,
    required this.senderEmail,
    required this.bankName,
    required this.bankCode,
    required this.senderId,
    required this.transactionOwner,
    required this.authorizationType,
    required this.status,
    required this.vat,
    required this.serviceCharge,
    required this.originalCurrency,
    required this.billAmount,
    required this.createdAt,
    required this.lastModifiedAt,
    required this.loanId,
    required this.loanType,
    required this.transactionType,
    required this.productDetails,
    required this.paidDate,
    required this.paymentMethod,
    required this.paidAmount,
    required this.paymentDetails,
    required this.paymentReference,
  });

  @override
  List<Object?> get props => [
        elstRef,
        billRefNo,
        beneficiaryId,
        beneficiaryName,
        senderName,
        senderPhone,
        senderEmail,
        bankName,
        bankCode,
        senderId,
        transactionOwner,
        authorizationType,
        status,
        vat,
        serviceCharge,
        originalCurrency,
        billAmount,
        createdAt,
        lastModifiedAt,
        loanId,
        loanType,
        transactionType,
        productDetails,
        paidDate,
        paymentMethod,
        paidAmount,
        paymentDetails,
        paymentReference,
      ];
}

/// Unified transaction details for all loan types
class TransactionDetails extends Equatable {
  final String elstRef;
  final String billRefNo;
  final double billAmount;
  final String date;
  final String? senderName;

  const TransactionDetails({
    required this.elstRef,
    required this.billRefNo,
    required this.billAmount,
    required this.date,
    this.senderName,
  });

  Map<String, dynamic> toJson() {
    return {
      'ELSTRef': elstRef,
      'billRefNo': billRefNo,
      'billAmount': billAmount,
      'date': date,
      if (senderName != null) 'senderName': senderName,
    };
  }

  @override
  List<Object?> get props => [elstRef, billRefNo, billAmount, date, senderName];
}

/// Unified item details for both cars and houses
class LoanItemDetails extends Equatable {
  final String model;
  final String price;
  final String? make; // For cars
  final String? type; // For houses

  const LoanItemDetails({
    required this.model,
    required this.price,
    this.make,
    this.type,
  });

  /// Factory for car details
  factory LoanItemDetails.car({
    required String model,
    required String price,
    required String make,
  }) {
    return LoanItemDetails(
      model: model,
      price: price,
      make: make,
    );
  }

  /// Factory for house details
  factory LoanItemDetails.house({
    required String model,
    required String price,
    required String type,
  }) {
    return LoanItemDetails(
      model: model,
      price: price,
      type: type,
    );
  }

  @override
  List<Object?> get props => [model, price, make, type];
}

/// Payment details for application fee transactions
class PaymentDetails extends Equatable {
  final String walletId;
  final String currency;

  const PaymentDetails({
    required this.walletId,
    required this.currency,
  });

  @override
  List<Object> get props => [walletId, currency];
}

/// Product details for application fee transactions
class TransactionProductDetails extends Equatable {
  final String id;
  final String name;
  final String description;
  final LocationDetails location;
  final String condition;
  final List<AmenityDetails> amenities;
  final String featureImage;
  final double productPrice;
  final String productName;

  const TransactionProductDetails({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.condition,
    required this.amenities,
    required this.featureImage,
    required this.productPrice,
    required this.productName,
  });

  @override
  List<Object> get props => [
        id,
        name,
        description,
        location,
        condition,
        amenities,
        featureImage,
        productPrice,
        productName,
      ];
}

/// Location details for products
class LocationDetails extends Equatable {
  final String fieldName;
  final String lng;
  final String lat;

  const LocationDetails({
    required this.fieldName,
    required this.lng,
    required this.lat,
  });

  @override
  List<Object> get props => [fieldName, lng, lat];
}

/// Amenity details for products
class AmenityDetails extends Equatable {
  final AmenityInfo amenity;
  final String detail;

  const AmenityDetails({
    required this.amenity,
    required this.detail,
  });

  @override
  List<Object> get props => [amenity, detail];
}

/// Amenity information
class AmenityInfo extends Equatable {
  final String name;
  final String icon;

  const AmenityInfo({
    required this.name,
    required this.icon,
  });

  @override
  List<Object> get props => [name, icon];
}
