import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application_fee_transaction.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

/// Usecase for generating application fee transaction for loans
class GenerateApplicationTransaction extends UsecaseWithParams<
    LoanApplicationFeeTransaction, GenerateApplicationTransactionParams> {
  const GenerateApplicationTransaction(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<LoanApplicationFeeTransaction> call(
    GenerateApplicationTransactionParams params,
  ) async {
    return repository.generateApplicationTransaction(
      loanApplicationId: params.loanApplicationId,
      loanType: params.loanType,
    );
  }
}

/// Parameters for generating application fee transaction
class GenerateApplicationTransactionParams extends Equatable {
  const GenerateApplicationTransactionParams({
    required this.loanApplicationId,
    required this.loanType,
  });

  final String loanApplicationId;
  final LoanItemType loanType;

  @override
  List<Object?> get props => [loanApplicationId, loanType];
}
