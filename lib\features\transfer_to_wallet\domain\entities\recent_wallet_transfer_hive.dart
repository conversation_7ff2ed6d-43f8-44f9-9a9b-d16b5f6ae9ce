import 'package:hive/hive.dart';

part 'recent_wallet_transfer_hive.g.dart';

@HiveType(typeId: 4)
class RecentWalletTransferHive extends HiveObject {
  RecentWalletTransferHive({
    required this.recipientId,
    required this.recipientName,
    required this.recipientPhone,
    required this.recipientEmail,
    required this.avatar,
    required this.createdAt,
  });
  @HiveField(0)
  final String recipientId;

  @HiveField(1)
  final String recipientName;

  @HiveField(2)
  final String recipientPhone;
  @HiveField(3)
  final String recipientEmail;

  @HiveField(4)
  final String avatar;

  @HiveField(5)
  final DateTime createdAt;

  String get uniqueKey => recipientId;
}
