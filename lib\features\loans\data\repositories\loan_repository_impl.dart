import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/data/datasources/loan_remote_data_source.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application_fee_transaction.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/features/loans/domain/entities/loan_confirm_otp_response_entity.dart';
import 'package:cbrs/features/loans/domain/entities/loan_confirm_pin_response_entity.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_confirmation.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_info.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:dartz/dartz.dart';

class LoanRepositoryImpl implements LoanRepository {
  const LoanRepositoryImpl({
    required this.remoteDataSource,
  });

  final LoanRemoteDataSource remoteDataSource;

  @override
  Future<Either<Failure, PaginatedLoanItemsResult>> getPaginatedLoanItems({
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
    String? categoryId,
    String? productId,
    String? searchQuery,
  }) async {
    try {
      final result = await remoteDataSource.getPaginatedLoanItems(
        loanType: loanType,
        page: page,
        limit: limit,
        categoryId: categoryId,
        productId: productId,
        searchQuery: searchQuery,
      );
      return Right(result);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, LoanItem>> getLoanItemById({
    required String itemId,
    required LoanItemType loanType,
  }) async {
    try {
      final loanItem = await remoteDataSource.getLoanItemById(
        itemId: itemId,
        loanType: loanType,
      );
      return Right(loanItem);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, List<LoanBank>>> getLoanBanks({
    required String productId,
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final banks = await remoteDataSource.getLoanBanks(
        productId: productId,
        loanType: loanType,
        page: page,
        limit: limit,
      );
      return Right(banks);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, LoanPaymentInfo>> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
  }) async {
    try {
      final paymentInfo = await remoteDataSource.getLoanPaymentInfo(
        productId: productId,
        bankId: bankId,
        upfrontPaymentPercentage: upfrontPaymentPercentage,
        loanType: loanType,
      );
      return Right(paymentInfo);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> calculateLoanPayment({
    required String bankId,
    required String loanProductId,
    required String upfrontPaymentPercentage,
    required String productId,
    required double interestRate,
    required LoanItemType loanType,
  }) async {
    try {
      final paymentInfo = await remoteDataSource.calculateLoanPayment(
        bankId: bankId,
        loanProductId: loanProductId,
        upfrontPaymentPercentage: upfrontPaymentPercentage,
        productId: productId,
        interestRate: interestRate,
        loanType: loanType,
      );
      return Right(paymentInfo);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, LoanApplication>> applyForLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
    bool isAutoRepay = false,
  }) async {
    try {
      final application = await remoteDataSource.applyForLoan(
        loanId: loanId,
        productId: productId,
        upfrontPaymentPercentage: upfrontPaymentPercentage,
        loanType: loanType,
        isAutoRepay: isAutoRepay,
      );
      return Right(application);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, LoanApplicationFeeTransaction>>
      generateApplicationTransaction({
    required String loanApplicationId,
    required LoanItemType loanType,
  }) async {
    try {
      final transaction = await remoteDataSource.generateApplicationTransaction(
        loanApplicationId: loanApplicationId,
        loanType: loanType,
      );
      return Right(transaction);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> getLoanTerms({
    required String bankId,
    required LoanItemType loanType,
  }) async {
    try {
      final terms = await remoteDataSource.getLoanTerms(
        bankId: bankId,
        loanType: loanType,
      );
      return Right(terms);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred: $e'));
    }
  }

  // Methods from LoanRepositoriesImpl
  @override
  ResultFuture<LoanConfirmOtpResponseEntity> confirmWithOtp({
    required String billRefNo,
    required String otpCode,
    required String transactionType,
  }) async {
    try {
      final result = await remoteDataSource.confirmWithOtp(
        billRefNo: billRefNo,
        otpCode: otpCode,
        transactionType: transactionType,
      );

      return Right(result);
    } on ApiException catch (error) {
      return Left(ServerFailure(message: error.message));
    } catch (error) {
      return Left(ServerFailure(message: error.toString()));
    }
  }

  @override
  ResultFuture<LoanConfirmPinResponseEntity> confirmWithPin({
    required String billRefNo,
    required String pin,
    required String transactionType,
  }) async {
    try {
      final result = await remoteDataSource.confirmWithPin(
        billRefNo: billRefNo,
        pin: pin,
        transactionType: transactionType,
      );

      return Right(result);
    } on ApiException catch (error) {
      return Left(ServerFailure(message: error.message));
    } catch (error) {
      return Left(ServerFailure(message: error.toString()));
    }
  }

  @override
  ResultFuture<String> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    try {
      final result = await remoteDataSource.resendOtp(
        otpFor: otpFor,
        billRefNo: billRefNo,
      );

      return Right(result);
    } on ApiException catch (error) {
      return Left(ServerFailure(message: error.message));
    } catch (error) {
      return Left(ServerFailure(message: error.toString()));
    }
  }

  @override
  ResultFuture<LoanPaymentConfirmation> confirmLoanPayment({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    try {
      final result = await remoteDataSource.confirmLoanPayment(
        transactionType: transactionType,
        billRefNo: billRefNo,
        pin: pin,
      );

      return Right(result);
    } on ApiException catch (error) {
      return Left(ServerFailure(message: error.message));
    } catch (error) {
      return Left(ServerFailure(message: error.toString()));
    }
  }

  @override
  ResultFuture<List<LoanCategory>> getLoanCategories({
    int page = 1,
    int limit = 10,
    String? loanType,
  }) async {
    try {
      final result = await remoteDataSource.getLoanCategories(
        page: page,
        limit: limit,
        loanType: loanType,
      );

      return Right(result);
    } on ApiException catch (error) {
      return Left(ServerFailure(message: error.message));
    } catch (error) {
      return Left(ServerFailure(message: error.toString()));
    }
  }

  @override
  ResultFuture<LoanCategory> getLoanCategoryById({
    required String categoryId,
  }) async {
    try {
      final result = await remoteDataSource.getLoanCategoryById(
        categoryId: categoryId,
      );

      return Right(result);
    } on ApiException catch (error) {
      return Left(ServerFailure(message: error.message));
    } catch (error) {
      return Left(ServerFailure(message: error.toString()));
    }
  }
}
