import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';

class ConfirmTransferRequestModel {
  final String pin;
  final String billRefNo;
  final TransactionType transactionType;
  final String? otp;
  final String authType;

  ConfirmTransferRequestModel({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
    this.otp,
  }) : authType = otp != null ? 'PIN_AND_OTP' : 'PIN';

  Map<String, dynamic> toJson() {
    return {
      'PIN': pin,
      'billRefNo': billRefNo,
      'transactionType': transactionType.value,
      'authType': authType,
      if (otp != null) 'otpCode': otp,
    };
  }
} 