import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_event.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_state.dart';
import 'package:cbrs/features/my_loan/domain/usecases/confirm_monthly_repayment_with_pin.dart';
import 'package:cbrs/features/my_loan/domain/usecases/confirm_upfront_with_pin_usecase.dart';
import 'package:cbrs/features/my_loan/domain/usecases/generate_upfront_payment_usecase.dart';
import 'package:cbrs/features/my_loan/domain/usecases/my_loan_resend_otp.dart';
import 'package:cbrs/features/my_loan/domain/usecases/my_loan_verify_otp.dart';
import 'package:cbrs/features/my_loan/domain/usecases/pay_monthly_repayment_usecase.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UpfrontPaymentBloc extends Bloc<RepaymentLoanEvent, RepaymentLoanState> {
  UpfrontPaymentBloc({
    required GenerateUpfrontPaymentUsecase generateUpfrontPaymentUsecase,
    required ConfirmUpfrontWithPinUsecase confirmUpfrontWithPinUsecase,
    required MyLoanResendOtpUsecase myLoanResendOtpUsecase,
    required MyLoanVerifyOtpUseCase myLoanVerifyOtpUseCase,
    required GenerateMonthlyRepaymentUseCase generateMonthlyRepaymentUseCase,
    required ConfirmMonthlyRepaymentWithPinUsecase
        confirmMonthlyRepaymentWithPinUsecase,
  })  : _generateUpfrontPaymentUsecase = generateUpfrontPaymentUsecase,
        _confirmUpfrontWithPinUsecase = confirmUpfrontWithPinUsecase,
        _myLoanResendOtpUsecase = myLoanResendOtpUsecase,
        _myLoanVerifyOtpUseCase = myLoanVerifyOtpUseCase,
        _confirmMonthlyRepaymentWithPinUsecase =
            confirmMonthlyRepaymentWithPinUsecase,
        _generateMonthlyRepaymentUseCase = generateMonthlyRepaymentUseCase,

        super(LoanInitialState()) {
    on<PayUpfrontPaymentEvent>(_handlePayUpfrontPayment);
    on<ConfirmTransferEvent>(_handleConfirmUpfrontWithPin);
    on<GenerateMonthlyRepaymentEvent>(_handleGenerateMonthlyRepayment);
    on<ConfirmMonthlyRepaymentEvent>(_handleConfirmMonthlyRepaymentWithPin);




    on<ResendOtpEvent>(_handleResendOtp);
    on<VerifyOtpEvent>(_handleConfirmOtp);
  }
  final GenerateUpfrontPaymentUsecase _generateUpfrontPaymentUsecase;
  final GenerateMonthlyRepaymentUseCase _generateMonthlyRepaymentUseCase;
  final ConfirmMonthlyRepaymentWithPinUsecase
      _confirmMonthlyRepaymentWithPinUsecase;
  final ConfirmUpfrontWithPinUsecase _confirmUpfrontWithPinUsecase;
  final MyLoanResendOtpUsecase _myLoanResendOtpUsecase;
  final MyLoanVerifyOtpUseCase _myLoanVerifyOtpUseCase;

  Future<void> _handlePayUpfrontPayment(
    PayUpfrontPaymentEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('Fetching loans...');

    try {
      // Emit loading state
      emit(RepaymentLoadingState());

      final result = await _generateUpfrontPaymentUsecase(
        event.loanId,
      );

      result.fold(
        // On failure,
        (failure) => emit(RepaymentErrorState(failure.message)),

        // On success,
        (success) => emit(GeneratedUpfronPayment(success)),
      );
    } catch (e) {
      debugPrint('Error encountered: $e');

      emit(RepaymentErrorState(e.toString()));
    }
  }

  Future<void> _handleConfirmUpfrontWithPin(
    ConfirmTransferEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('Fetching loans...');

    try {
      // Emit loading state
      emit(RepaymentLoadingState());

      final result = await _confirmUpfrontWithPinUsecase(
        ConfirmUpfrontTransferParams(
          pin: event.pin,
          billRefNo: event.billRefNo,
          transactionType: event.transactionType,
        ),
      );

      result.fold(
        // On failure,
        (failure) => emit(RepaymentErrorState(failure.message)),

        // On success,
        (success) => emit(ConfirmedUpfrontPaymentState(success)),
      );
    } catch (e) {
      debugPrint('Error encountered: $e');

      emit(RepaymentErrorState(e.toString()));
    }
  }

  Future<void> _handleGenerateMonthlyRepayment(
    GenerateMonthlyRepaymentEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('Fetching loans...');

    try {
      // Emit loading state
      emit(RepaymentLoadingState());

      final result = await _generateMonthlyRepaymentUseCase(
       MonthlyRepaymentParams(loanId: event.loanId, months: event.months)
      );

      result.fold(
        // On failure,
        (failure) => emit(RepaymentErrorState(failure.message)),

        // On success,
        (success) => emit(GenerateMonthlyRepaymentState(success)),
      );
    } catch (e) {
      debugPrint('Error encountered: $e');

      emit(RepaymentErrorState(e.toString()));
    }
  }

  Future<void> _handleConfirmMonthlyRepaymentWithPin(
    ConfirmMonthlyRepaymentEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('Fetching loans...');

    try {
      // Emit loading state
      emit(RepaymentLoadingState());

      final result = await _confirmMonthlyRepaymentWithPinUsecase(
        ConfirmMonthlyRepaymentrParams(
          pin: event.pin,
          billRefNo: event.billRefNo,
          transactionType: event.transactionType,
          loanId: event.loanPaymentId,
          months: event.months
        ),
      );

      result.fold(
        // On failure,
        (failure) => emit(RepaymentErrorState(failure.message)),

        // On success,
        (success) => emit(ConfirmedMonthlyRePaymentState(success)),
      );
    } catch (e) {
      debugPrint('Error encountered: $e');

      emit(RepaymentErrorState(e.toString()));
    }
  }

  Future<void> _handleResendOtp(
    ResendOtpEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('Fetching loans...');

    try {
      // Emit loading state
      emit(RepaymentLoadingState());

      final result = await _myLoanResendOtpUsecase(
        ResendUpfrontOtpParams(
          billRefNo: event.billRefNo,
          otpFor: event.otpFor,
        ),
      );

      result.fold(
        // On failure,
        (failure) => emit(RepaymentErrorState(failure.message)),

        // On success,
        (success) => emit(ActionConfirmRuleEngines(success)),
      );
    } catch (e) {
      debugPrint('Error encountered: $e');

      emit(RepaymentErrorState(e.toString()));
    }
  }

  Future<void> _handleConfirmOtp(
    VerifyOtpEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('Fetching loans...');

    try {
      // Emit loading state
      emit(RepaymentLoadingState());

      final result = await _myLoanVerifyOtpUseCase(
        VerifyUpfrontOtpParams(
          billRefNo: event.billRefNo,
          otpFor: event.otpFor,
          otpCode: event.otpCode,
        ),
      );

      result.fold(
        // On failure,
        (failure) => emit(RepaymentErrorState(failure.message)),

        // On success,
        (success) => emit(ActionConfirmRuleEngines(success)),
      );
    } catch (e) {
      debugPrint('Error encountered: $e');

      emit(RepaymentErrorState(e.toString()));
    }
  }
}
