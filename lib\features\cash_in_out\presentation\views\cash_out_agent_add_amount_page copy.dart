import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_number_keyboard.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_state.dart';
import 'package:cbrs/features/cash_in_out/presentation/widgets/custom_wallet_balance_etb.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:cbrs/features/user/domain/entities/user.dart';
import 'package:cbrs/features/user/presentation/bloc/user_bloc.dart';
import 'package:cbrs/features/user/presentation/bloc/user_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_event.dart';
import 'package:cbrs/features/cash_in_out/application/bloc/cash_in_cash_out_bloc.dart';

class CashOutAgentAddAmountPagecopy extends StatefulWidget {
  final Map<String, dynamic> agentData;
  final bool showAgentContainer;

  const CashOutAgentAddAmountPagecopy({
    super.key,
    required this.agentData,
    required this.showAgentContainer,
  });

  @override
  State<CashOutAgentAddAmountPagecopy> createState() =>
      _CashOutAgentAddAmountPagecopyState();
}

class _CashOutAgentAddAmountPagecopyState extends State<CashOutAgentAddAmountPagecopy> {
  final TextEditingController _amountController = TextEditingController();
  bool _isButtonEnabled = false;
  bool _exceedsBalance = false;
  String _senderName = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_updateButtonState);
    // Set initial amount if provided
    if (widget.agentData['amount'] != null) {
      final amount = widget.agentData['amount'].toString();
      _amountController.text = '${double.parse(amount).toStringAsFixed(2)} ETB';
    }
  }

  void _updateButtonState() {
    if (!mounted) return;
    setState(() {
      if (_amountController.text.isEmpty) {
        _isButtonEnabled = false;
        _exceedsBalance = false;
        return;
      }

      try {
        final cleanAmount =
            _amountController.text.replaceAll(RegExp(r'[^\d.]'), '');
        final double inputAmount = double.parse(cleanAmount);

        // Get current balance from UserBloc
        final userState = context.read<UserBloc>().state;
        final currentBalance = userState is UserLoaded
            ? userState.user.wallets
                .firstWhere(
                  (w) => w.currency == 'ETB',
                  orElse: () =>
                      Wallet(currency: 'ETB', balance: 0, walletCode: ''),
                )
                .balance
            : 0.0;

        _exceedsBalance = inputAmount > currentBalance;
        _isButtonEnabled = inputAmount > 0 && !_exceedsBalance;
      } catch (e) {
        _isButtonEnabled = false;
        _exceedsBalance = false;
      }
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  // Add method to handle keypad input
  void _onKeyPressed(String value) {
    if (value == '⌫' || value == 'clear') {
      if (_amountController.text.isNotEmpty) {
        // Get current amount without currency symbol and commas
        final currentAmount =
            _amountController.text.replaceAll(RegExp(r'[^\d.]'), '');

        if (value == 'clear') {
          _amountController.clear();
          return;
        }

        if (currentAmount.isNotEmpty) {
          final newAmount =
              currentAmount.substring(0, currentAmount.length - 1);

          if (newAmount.isEmpty) {
            _amountController.clear();
            return;
          }

          try {
            String formattedAmount;
            if (newAmount.contains('.')) {
              final parts = newAmount.split('.');
              final wholePart = _formatWithCommas(parts[0]);
              formattedAmount = '$wholePart.${parts[1]}';
            } else {
              formattedAmount = _formatWithCommas(newAmount);
            }

            _amountController.text = '$formattedAmount ETB';
            _amountController.selection = TextSelection.fromPosition(
              TextPosition(offset: _amountController.text.length - 4),
            );
          } catch (e) {
            _amountController.clear();
          }
        }
      }
    } else {
      final currentAmount =
          _amountController.text.replaceAll(RegExp(r'[^\d.]'), '');

      // Handle decimal point
      if (value == '.') {
        if (!currentAmount.contains('.')) {
          if (currentAmount.isEmpty) {
            _amountController.text = '0. ETB';
          } else {
            _amountController.text = '${_formatWithCommas(currentAmount)}. ETB';
          }
        }
        return;
      }

      // Handle numeric input
      if (currentAmount.isEmpty && value == '0') return;

      String newAmount = currentAmount + value;
      try {
        String formattedAmount;
        if (newAmount.contains('.')) {
          final parts = newAmount.split('.');
          final wholePart = _formatWithCommas(parts[0]);
          formattedAmount = '$wholePart.${parts[1]}';
        } else {
          formattedAmount = _formatWithCommas(newAmount);
        }

        _amountController.text = '$formattedAmount ETB';
        _amountController.selection = TextSelection.fromPosition(
          TextPosition(offset: _amountController.text.length - 4),
        );
      } catch (e) {
        return;
      }
    }
  }

  String _formatWithCommas(String number) {
    if (number.isEmpty) return '';
    final parts = number.split('.');
    parts[0] = parts[0].replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
    return parts.join('.');
  }

  double _getWalletBalance(BuildContext context) {
    final userState = context.read<UserBloc>().state;
    if (userState is UserLoaded) {
      final etbWallet = userState.user.wallets.firstWhere(
        (w) => w.currency == 'ETB',
        orElse: () => Wallet(currency: 'ETB', balance: 0, walletCode: ''),
      );
      return etbWallet.balance;
    }
    return 0.0;
  }

  @override
  Widget build(BuildContext context) {
    final userState = context.read<UserBloc>().state;
    if (userState is UserLoaded) {
      _senderName = "${userState.user.firstName} ${userState.user.lastName}";
    }
    return
     BlocListener<CashInCashOutBloc, CashInCashOutState>(
      listener: (context, state) {
        if (state is CashInCashOutLoading) {
          setState(() {
            _isLoading = true;
          });
        } else {
          setState(() {
            _isLoading = false;
          });
        }

        if (state is TransferRulesCheckSuccess) {
          final cleanAmount =
              _amountController.text.replaceAll(' ETB', '').trim();

          context.pushNamed(
            AppRouteName.cashOutConfirmation,
            extra: {
              'amount': double.parse(cleanAmount),
              'serviceCharge': state.serviceCharge,
              'vat': state.vat,
              'total': state.total,
              'authorizationType': state.authorizationType,
              'agentName': widget.agentData['agentName'],
              'agentId': widget.agentData['agentId'],
              'agentCode': widget.agentData['agentCode'],
              'senderName': _senderName,
            },
          );
        } else if (state is CashInCashOutFailure) {
          CustomToastification(
            context,
            message: state.message,
            isError: true,
          );
        }
      },


      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(
            'Cash-Out',
            style: GoogleFonts.outfit(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Cash-Out',
                            style: GoogleFonts.outfit(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Enter the amount you wish to cash out, then submit to complete the transaction.',
                            style: GoogleFonts.outfit(
                              fontSize: 14.sp,
                              color: const Color(0xFFAAAAAA),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),

                      // Agent Container (if shown)
                      if (widget.showAgentContainer) ...[
                        RecipientCard(
                          alwaysShowETBColor: true,
                          name: widget.agentData['agentName']?.toString() ?? '',
                          accountNumber:
                              'Agent ID: ${widget.agentData['agentCode'] ?? ''}',
                          isBirrTransfer: true,
                        ),
                        SizedBox(height: 16.h),
                      ],

                      // Wallet Balance and Amount Input
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            TextField(
                              controller: _amountController,
                              readOnly: true,
                              textAlign: TextAlign.center,
                              style: GoogleFonts.outfit(
                                fontSize: 32.sp,
                                fontWeight: FontWeight.w600,
                                color: _exceedsBalance
                                    ? Colors.red
                                    : LightModeTheme().primaryColorBirr,
                              ),
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: '0.00 ETB',
                                hintStyle: GoogleFonts.outfit(
                                  fontSize: 32.sp,
                                  color: const Color(0xFFAAAAAA),
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ),
                            SizedBox(height: 14.h),
                            Center(
                              child: _buildWalletBalance(),
                            ),
                            SizedBox(height: 14.h),
                            if (_exceedsBalance)
                              Text(
                                'Amount exceeds wallet balance',
                                style: GoogleFonts.outfit(
                                  fontSize: 13.sp,
                                  color: Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Keypad and Button
                      CustomNumberKeyboard(
                        onKeyPressed: _onKeyPressed,
                        useBackspace: true,
                        fontSize: 24.sp,
                      ),
                      SizedBox(height: 12.h),
                      CustomButton(
                        text: 'Continue',
                        showLoadingIndicator: _isLoading,
                        onPressed: (_isButtonEnabled && !_isLoading)
                            ? () {
                                final cleanAmount = _amountController.text
                                    .replaceAll(' ETB', '')
                                    .trim();
                                context.read<CashInCashOutBloc>().add(
                                      CheckTransferRulesRequested(
                                        amount: double.parse(cleanAmount),
                                      ),
                                    );
                              }
                            : null,
                        options: CustomButtonOptions(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          color: _isButtonEnabled
                              ? LightModeTheme().primaryColorBirr
                              : Colors.grey,
                          disabledColor: Colors.grey,
                          textStyle: GoogleFonts.outfit(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                          borderRadius: BorderRadius.circular(32.r),
                          loadingColor: Colors.white,
                        ),
                      ),
                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWalletBalance() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        final balance = state is UserLoaded
            ? state.user.wallets
                .firstWhere(
                  (w) => w.currency == 'ETB',
                  orElse: () =>
                      Wallet(currency: 'ETB', balance: 0, walletCode: ''),
                )
                .balance
            : 0.0;

        return CustomWalletBalanceEtb(
          walletBalance: balance,
          isBirrBalance: true,
        );
      },
    );
  }
}
