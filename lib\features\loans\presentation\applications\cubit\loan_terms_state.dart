import 'package:equatable/equatable.dart';

/// Base state for loan terms
abstract class LoanTermsState extends Equatable {
  const LoanTermsState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class LoanTermsInitial extends LoanTermsState {
  const LoanTermsInitial();
}

/// Loading state when fetching terms
class LoanTermsLoading extends LoanTermsState {
  const LoanTermsLoading();
}

/// Success state with terms content
class LoanTermsLoaded extends LoanTermsState {
  const LoanTermsLoaded({
    required this.content,
    required this.bankId,
    required this.loanType,
  });

  final String content;
  final String bankId;
  final String loanType;

  @override
  List<Object?> get props => [content, bankId, loanType];
}

/// Error state when terms fetching fails
class LoanTermsError extends LoanTermsState {
  const LoanTermsError({
    required this.message,
    this.bankId,
    this.loanType,
  });

  final String message;
  final String? bankId;
  final String? loanType;

  @override
  List<Object?> get props => [message, bankId, loanType];
}
