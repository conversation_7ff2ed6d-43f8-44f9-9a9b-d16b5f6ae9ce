import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application_fee_transaction.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/features/loans/domain/entities/loan_confirm_otp_response_entity.dart';
import 'package:cbrs/features/loans/domain/entities/loan_confirm_pin_response_entity.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_confirmation.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_info.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:dartz/dartz.dart';

/// Repository interface for loan operations
abstract class LoanRepository {
  /// Get paginated loan items (cars, houses, etc.)
  Future<Either<Failure, PaginatedLoanItemsResult>> getPaginatedLoanItems({
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
    String? categoryId,
    String? productId,
    String? searchQuery,
  });

  /// Get a single loan item by ID
  Future<Either<Failure, LoanItem>> getLoanItemById({
    required String itemId,
    required LoanItemType loanType,
  });

  /// Get loan banks for a specific loan type
  Future<Either<Failure, List<LoanBank>>> getLoanBanks({
    required String productId,
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
  });

  /// Get loan payment information
  Future<Either<Failure, LoanPaymentInfo>> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
  });

  /// Calculate loan payment details
  Future<Either<Failure, Map<String, dynamic>>> calculateLoanPayment({
    required String bankId,
    required String loanProductId,
    required String upfrontPaymentPercentage,
    required String productId,
    required double interestRate,
    required LoanItemType loanType,
  });

  /// Apply for a loan
  Future<Either<Failure, LoanApplication>> applyForLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
    bool isAutoRepay = false,
  });

  /// Generate application fee transaction
  Future<Either<Failure, LoanApplicationFeeTransaction>>
      generateApplicationTransaction({
    required String loanApplicationId,
    required LoanItemType loanType,
  });

  /// Get loan terms
  Future<Either<Failure, String>> getLoanTerms({
    required String bankId,
    required LoanItemType loanType,
  });

  ResultFuture<LoanConfirmPinResponseEntity> confirmWithPin({
    required String billRefNo,
    required String pin,
    required String transactionType,
  });

  ResultFuture<LoanConfirmOtpResponseEntity> confirmWithOtp({
    required String billRefNo,
    required String otpCode,
    required String transactionType,
  });

  ResultFuture<String> resendOtp({
    required String billRefNo,
    required String otpFor,
  });

  /// Confirm loan payment with PIN
  ResultFuture<LoanPaymentConfirmation> confirmLoanPayment({
    required String transactionType,
    required String billRefNo,
    required String pin,
  });

  // Loan Categories
  ResultFuture<List<LoanCategory>> getLoanCategories({
    int page = 1,
    int limit = 10,
    String? loanType, // 'car', 'mortgage', etc.
  });

  ResultFuture<LoanCategory> getLoanCategoryById({
    required String categoryId,
  });
}
