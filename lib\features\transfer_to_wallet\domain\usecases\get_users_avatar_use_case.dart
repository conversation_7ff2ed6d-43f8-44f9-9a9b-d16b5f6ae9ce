import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/repositories/wallet_transfer_repository.dart';
import 'package:equatable/equatable.dart';

class GetUsersAvatarUseCase
    extends UsecaseWithParams<Map<String, String>, GetUsersAvatarParams> {
  const GetUsersAvatarUseCase(this._repository);
  final WalletTransferRepository _repository;

  @override
  ResultFuture<Map<String, String>> call(
    GetUsersAvatarParams params,
  ) async {
    return _repository.getUsersAvatar(
      userIds: params.userIds,
    );
  }
}

class GetUsersAvatarParams extends Equatable {
  const GetUsersAvatarParams({
    required this.userIds,
  });
  final List<String> userIds;

  @override
  List<Object?> get props => [userIds];
}
