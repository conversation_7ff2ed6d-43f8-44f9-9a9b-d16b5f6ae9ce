import 'package:cbrs/core/common/widgets/success/custom_transaction_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class MiniAppSuccessScreen extends StatelessWidget {
  final String amount;
  final String merchantName;
  final String transactionId;
  final String referenceNumber;
  final String miniAppName;
  final double serviceFee;
  final double vat;

  const MiniAppSuccessScreen({
    super.key,
    required this.amount,
    required this.merchantName,
    required this.transactionId,
    required this.referenceNumber,
    required this.miniAppName,
    required this.serviceFee,
    required this.vat,
  });

  String _formatAmount(double amount) {
    return NumberFormat.currency(
      symbol: 'ETB ',
      decimalDigits: 2,
      customPattern: '#,##0.00 ¤',
    ).format(amount);
  }

  Widget _buildTransactionDetails(BuildContext context) {
    return Column(
      children: [
        _buildTransactionDetail('Merchant Name:', merchantName),
        _buildTransactionDetail('Transaction ID:', transactionId),
        _buildTransactionDetail('Mini App:', miniAppName),
        _buildTransactionDetail('Reference No:', referenceNumber),
        _buildTransactionDetail(
          'Date:',
          DateFormat('MMMM dd, yyyy').format(DateTime.now()),
        ),
        _buildTransactionDetail(
          'Service Fee:',
          _formatAmount(serviceFee),
        ),
        _buildTransactionDetail(
          'VAT:',
          _formatAmount(vat),
        ),
      ],
    );
  }

  Widget _buildTransactionDetail(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 4,
            child: Text(
              label,
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: const Color(0xFF757575),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Text(
              value,
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomTransactionSuccessScreen(
      pageDescription: 'Mini App Payment Successful',
      totalAmount: double.parse(amount),
      isBirrTransfer: true,
      hasPageTitle: true,
      child: _buildTransactionDetails(context),
      onQrTap: () {
        // Implement QR functionality
      },
      onShareTap: () {
        // Implement share functionality
      },
      onGetRecieptTap: () {
        // Implement receipt functionality
      },
    );
  }
}
