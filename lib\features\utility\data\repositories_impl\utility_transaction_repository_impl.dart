// import '../../domain/entities/utility_transaction.dart';
// import '../../domain/repositories/utility_transaction_repository.dart';
// import '../datasources/utility_payment_data_source.dart';

// class UtilityTransactionRepositoryImpl implements UtilityTransactionRepository {
//   final UtilityPaymentDataSource dataSource;

//   UtilityTransactionRepositoryImpl({required this.dataSource});

//   @override
//   Future<UtilityTransaction> getUtilityTransaction(String transactionId) async {
//     try {
//       final Map<String, dynamic> request = {
//         'orderPayload': {
//           'transaction_id': transactionId,
//         },
//         'x-api-key': '74a93bc*****************************************',
//       };

//       throw Exception('Failed to get transaction: ');

//       // final response = await dataSource.processUtilityPayment(request);
//       // return UtilityTransaction.fromJson(
//       //     response['data'] as Map<String, dynamic>);
//     } catch (e) {
//       throw Exception('Failed to get transaction: ${e.toString()}');
//     }
//   }

//   @override
//   Future<List<UtilityTransaction>> getUtilityTransactions() async {
//     // This method might be implemented later if needed
//     throw UnimplementedError('Get all transactions not implemented yet');
//   }
// }
