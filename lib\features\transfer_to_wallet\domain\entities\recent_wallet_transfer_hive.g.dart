// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recent_wallet_transfer_hive.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RecentWalletTransferHiveAdapter
    extends TypeAdapter<RecentWalletTransferHive> {
  @override
  final int typeId = 4;

  @override
  RecentWalletTransferHive read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RecentWalletTransferHive(
      recipientId: fields[0] as String,
      recipientName: fields[1] as String,
      recipientPhone: fields[2] as String,
      recipientEmail: fields[3] as String,
      avatar: fields[4] as String,
      createdAt: fields[5] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, RecentWalletTransferHive obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.recipientId)
      ..writeByte(1)
      ..write(obj.recipientName)
      ..writeByte(2)
      ..write(obj.recipientPhone)
      ..writeByte(3)
      ..write(obj.recipientEmail)
      ..writeByte(4)
      ..write(obj.avatar)
      ..writeByte(5)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecentWalletTransferHiveAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
