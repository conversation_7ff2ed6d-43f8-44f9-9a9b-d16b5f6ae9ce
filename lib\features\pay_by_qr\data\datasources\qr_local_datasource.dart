import 'dart:convert';
import 'package:cbrs/core/error/exceptions.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/features/pay_by_qr/data/models/qr_generate_response_model.dart';

abstract class QrLocalDataSource {
  /// Gets the cached QR code response
  ///
  /// Returns [QrGenerateResponseModel] if cache exists
  /// Throws [CacheException] if no cached data is present
  Future<QrGenerateResponseModel?> getCachedQrCode();

  /// Caches the QR code response
  Future<void> cacheQrCode(QrGenerateResponseModel qrCode);

  /// Clears the cached QR code
  Future<void> clearCache();
}

class QrLocalDataSourceImpl implements QrLocalDataSource {
  final HiveBoxManager hiveManager;
  final String cacheKey = 'CACHED_QR_CODE';
  final String timestampKey = 'QR_CODE_CACHE_TIMESTAMP';
  final Duration cacheValidity =
      const Duration(minutes: 5); // Cache valid for 5 minutes

  QrLocalDataSourceImpl({
    required this.hiveManager,
  });

  @override
  Future<QrGenerateResponseModel?> getCachedQrCode() async {
    try {
      // Get the cached data and timestamp
      final cachedData = hiveManager.authBox.get(cacheKey);
      final cachedTimestamp =
          hiveManager.authBox.get(timestampKey) as DateTime?;

      if (cachedData != null && cachedTimestamp != null) {
        // Check if cache is still valid
        final now = DateTime.now();
        if (now.difference(cachedTimestamp) <= cacheValidity) {
          // Cache is still valid
          final Map<String, dynamic> qrData =
              json.decode(cachedData as String) as Map<String, dynamic>;
          return QrGenerateResponseModel.fromJson(qrData);
        } else {
          // Cache expired, clear it
          await clearCache();
        }
      }
      return null;
    } catch (e) {
      print('🔥 Error reading QR code cache: $e');
      // If there's any error reading the cache, clear it and return null
      await clearCache();
      return null;
    }
  }

  @override
  Future<void> cacheQrCode(QrGenerateResponseModel qrCode) async {
    try {
      // Convert QR code to JSON string
      final qrJson = json.encode({
        'qr': qrCode.qr,
        'name': qrCode.name,
        'code': qrCode.code,
      });

      // Save data and timestamp
      await hiveManager.authBox.put(cacheKey, qrJson);
      await hiveManager.authBox.put(timestampKey, DateTime.now());
    } catch (e) {
      print('🔥 Error caching QR code: $e');
      throw CacheException(message: 'Failed to cache QR code: ${e.toString()}');
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      await hiveManager.authBox.delete(cacheKey);
      await hiveManager.authBox.delete(timestampKey);
    } catch (e) {
      print('🔥 Error clearing QR code cache: $e');
      throw CacheException(
          message: 'Failed to clear QR code cache: ${e.toString()}');
    }
  }
}
