import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:cbrs/features/pay_by_qr/domain/entities/qr_parse_response.dart';
import 'package:cbrs/features/pay_by_qr/domain/repositories/qr_repository.dart';
import 'package:equatable/equatable.dart';

class ParseQrCodeUsecase
    extends UsecaseWithParams<QrParseResponse, ParseQrCodeParams> {
  const ParseQrCodeUsecase(this._repository);
  final QrRepository _repository;

  @override
  ResultFuture<QrParseResponse> call(ParseQrCodeParams params) async {
    return _repository.parseQrCode(
      qrString: params.qrString,
    );
  }
}

class ParseQrCodeParams extends Equatable {
  const ParseQrCodeParams({
    required this.qrString,
  });
  final String qrString;

  @override
  List<Object?> get props => [qrString];
}
