import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/send_money/domain/repositories/bank_transfer_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

class WalletBalanceBloc extends Bloc<HomeEvent, HomeState> {
  WalletBalanceBloc({
    required BankTransferRepository bankTransferRepository,
  })  : _bankTransferRepository = bankTransferRepository,
        super(HomeInitial()) {
    on<FetchWalletEvent>(_handleFetchWalletEvent);
    on<WalletSwitchingEvent>(_handleSwitchWalletEvent);
  }

  final BankTransferRepository _bankTransferRepository;
  bool _hasFetchedWallet =
      false; // i dont want to show loader when user hide or show balance

  Future<void> _handleFetchWalletEvent(
    FetchWalletEvent event,
    Emitter<HomeState> emit,
  ) async {
    debugPrint('📌 deppub 1111');
    final walletType = event.isUsdWallet ? 'USD' : 'ETB';
    if (event.forceTheme) await _setThemeAndWallet(walletType);
    debugPrint('📌 deppub 2222');

    if (!_hasFetchedWallet) {
      debugPrint('📌 deppub 33333');

      emit(WalletLoadingState());
      await _emitWalletDetails(emit, event.isUsdWallet);
      _hasFetchedWallet = true;
    } else {
      debugPrint('📌 deppub 444444 state $state');

      final currentState = state;
      if (currentState is WalletLoadedState) {
        emit(currentState);
        debugPrint('📌 deppub 44444ss4 state $state');

        // await _emitWalletDetails(emit, event.isUsdWallet);
      }

      await _emitWalletDetails(emit, event.isUsdWallet);
    }
  }

  Future<void> _handleSwitchWalletEvent(
    WalletSwitchingEvent event,
    Emitter<HomeState> emit,
  ) async {
    await _setThemeAndWallet(event.walletType);

// I emit wallet loaded state to keep track of theme, unless theme applieed after wallet fetched
    emit(
      WalletLoadedState(
        isUsdWallet: event.walletType == 'USD',
        etbBalance: event.etbBalance,
        usdBalance: event.usdBalance,
      ),
    );

    emit(WalletLoadingState());
    await _emitWalletDetails(emit, event.walletType == 'USD');
    _hasFetchedWallet = true;
  }

  Future<void> _setThemeAndWallet(String walletType) async {
    GlobalVariable.currentlySelectedWallet = walletType;
    final themeController = Get.find<GetAppThemeController>();
    await themeController.forceSetTheme(walletType);
  }

  Future<void> _emitWalletDetails(
    Emitter<HomeState> emit,
    bool isUsdWallet,
  ) async {
    final result = await _bankTransferRepository.getWalletDetails();

    result.fold(
      (failure) {
        debugPrint('Failed to fetch wallet: ${failure.message}');
        emit(HomeError(failure.message));
      },
      (walletDetails) {
        emit(
          WalletLoadedState(
            isUsdWallet: isUsdWallet,
            etbBalance: walletDetails.etbBalance,
            usdBalance: walletDetails.usdBalance,
          ),
        );
        debugPrint("finnnnll 📌 📌📌📌📌📌 ");
      },
    );
  }
}
