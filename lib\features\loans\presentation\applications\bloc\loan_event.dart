import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:equatable/equatable.dart';

/// Base class for all loan events
abstract class LoanEvent extends Equatable {
  const LoanEvent();

  @override
  List<Object?> get props => [];
}

/// Event to fetch paginated loan items
class FetchLoanItemsEvent extends LoanEvent {
  const FetchLoanItemsEvent({
    required this.params,
    this.isRefresh = false,
  });

  final GetPaginatedLoanItemsParams params;
  final bool isRefresh;

  @override
  List<Object?> get props => [params, isRefresh];
}

/// Event to load more loan items (pagination)
class LoadMoreLoanItemsEvent extends LoanEvent {
  const LoadMoreLoanItemsEvent({
    required this.params,
  });

  final GetPaginatedLoanItemsParams params;

  @override
  List<Object?> get props => [params];
}

/// Event to refresh loan items
class RefreshLoanItemsEvent extends LoanEvent {
  const RefreshLoanItemsEvent({
    required this.params,
  });

  final GetPaginatedLoanItemsParams params;

  @override
  List<Object?> get props => [params];
}

class FilterLoanItemsByCategoryEvent extends LoanEvent {
  const FilterLoanItemsByCategoryEvent({
    required this.categoryId,
    required this.loanType,
    this.page = 1,
    this.limit = 10,
  });

  final String categoryId;
  final LoanItemType loanType;
  final int page;
  final int limit;

  @override
  List<Object?> get props => [categoryId, loanType, page, limit];
}

/// Event to search loan items
class SearchLoanItemsEvent extends LoanEvent {
  const SearchLoanItemsEvent({
    required this.query,
    required this.loanType,
    this.categoryId,
    this.page = 1,
    this.limit = 10,
  });

  final String query;
  final LoanItemType loanType;
  final String? categoryId;
  final int page;
  final int limit;

  @override
  List<Object?> get props => [query, loanType, categoryId, page, limit];
}

/// Event to clear loan items
class ClearLoanItemsEvent extends LoanEvent {
  const ClearLoanItemsEvent();
}

/// Event to reset loan state
class ResetLoanStateEvent extends LoanEvent {
  const ResetLoanStateEvent();
}

// ============================================================================
// LOAN APPLICATION EVENTS (OTP/PIN Confirmation)
// ============================================================================

/// Event to confirm loan application with PIN
class LoanConfirmWithPinEvent extends LoanEvent {
  const LoanConfirmWithPinEvent({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
  });

  final String pin;
  final String billRefNo;
  final String transactionType;

  @override
  List<Object?> get props => [
        pin,
        billRefNo,
        transactionType,
      ];
}

/// Event to confirm loan application with OTP
class LoanConfirmWithOtpEvent extends LoanEvent {
  const LoanConfirmWithOtpEvent({
    required this.otpCode,
    required this.billRefNo,
    required this.transactionType,
  });

  final String otpCode;
  final String billRefNo;
  final String transactionType;

  @override
  List<Object?> get props => [billRefNo, transactionType, otpCode];
}

/// Event to resend OTP for loan application
class LoanResendOtpEvent extends LoanEvent {
  const LoanResendOtpEvent({
    required this.otpFor,
    required this.billRefNo,
  });

  final String otpFor;
  final String billRefNo;

  @override
  List<Object?> get props => [billRefNo, otpFor];
}

/// Event to apply for a loan
class ApplyLoanEvent extends LoanEvent {
  const ApplyLoanEvent({
    required this.loanId,
    required this.productId,
    required this.upfrontPaymentPercentage,
    required this.loanType,
    this.isAutoRepay = false,
  });

  final String loanId;
  final String productId;
  final String upfrontPaymentPercentage;
  final LoanItemType loanType;
  final bool isAutoRepay;

  @override
  List<Object?> get props => [
        loanId,
        productId,
        upfrontPaymentPercentage,
        loanType,
        isAutoRepay,
      ];
}

/// Event to generate application fee transaction
class GenerateApplicationTransactionEvent extends LoanEvent {
  const GenerateApplicationTransactionEvent({
    required this.loanApplicationId,
    required this.loanType,
  });

  final String loanApplicationId;
  final LoanItemType loanType;

  @override
  List<Object?> get props => [loanApplicationId, loanType];
}

/// Event to confirm loan payment with PIN
class ConfirmLoanPaymentEvent extends LoanEvent {
  const ConfirmLoanPaymentEvent({
    required this.transactionType,
    required this.billRefNo,
    required this.pin,
  });

  final String transactionType;
  final String billRefNo;
  final String pin;

  @override
  List<Object?> get props => [transactionType, billRefNo, pin];
}
