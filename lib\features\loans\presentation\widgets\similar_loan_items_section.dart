import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/utils/price_formatter.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_event.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class SimilarLoanItemsSection extends StatefulWidget {
  const SimilarLoanItemsSection({
    required this.loanItem,
    required this.onItemTap,
    super.key,
  });

  final LoanItem loanItem;
  final ValueChanged<LoanItem> onItemTap;

  @override
  State<SimilarLoanItemsSection> createState() =>
      _SimilarLoanItemsSectionState();
}

class _SimilarLoanItemsSectionState extends State<SimilarLoanItemsSection> {
  @override
  void initState() {
    super.initState();
    _loadSimilarItems();
  }

  void _loadSimilarItems() {
    // Load similar items based on category
    context.read<LoanItemsBloc>().add(
          FilterLoanItemsByCategoryEvent(
            categoryId: widget.loanItem.category.id,
            loanType: widget.loanItem.type,
            page: 1,
            limit: 5,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;
        final cardWidth = (maxWidth * 0.65).clamp(180.0, 220.0);
        final cardHeight = (cardWidth * 1.4).clamp(240.0, 280.0);
        final titleSize = (maxWidth * 0.05).clamp(16.0, 22.0);
        final cardPadding = (maxWidth * 0.02).clamp(8.0, 16.0);
        final borderRadius = (maxWidth * 0.04).clamp(12.0, 20.0);

        return BlocBuilder<LoanItemsBloc, LoanState>(
          builder: (context, state) {
            if (state is LoanLoading) {
              return _buildLoadingSection(
                cardWidth: cardWidth,
                cardHeight: cardHeight,
                titleSize: titleSize,
                cardPadding: cardPadding,
                borderRadius: borderRadius,
              );
            }

            if (state is LoanError || state is LoanEmpty) {
              return const SizedBox.shrink();
            }

            if (state is LoanLoaded) {
              final similarItems = state.items
                  .where((item) => item.id != widget.loanItem.id)
                  .take(3)
                  .toList();

              if (similarItems.isEmpty) {
                return const SizedBox.shrink();
              }

              return _buildSimilarItemsSection(
                similarItems: similarItems,
                cardWidth: cardWidth,
                cardHeight: cardHeight,
                titleSize: titleSize,
                cardPadding: cardPadding,
                borderRadius: borderRadius,
              );
            }

            return const SizedBox.shrink();
          },
        );
      },
    );
  }

  Widget _buildLoadingSection({
    required double cardWidth,
    required double cardHeight,
    required double titleSize,
    required double cardPadding,
    required double borderRadius,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(cardPadding),
          child: Text(
            _getSectionTitle(),
            style: GoogleFonts.outfit(
              fontSize: titleSize,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ),
        SizedBox(
          height: cardHeight,
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: cardPadding),
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            itemBuilder: (context, index) => _buildShimmerCard(
              width: cardWidth,
              height: cardHeight,
              borderRadius: borderRadius,
              padding: cardPadding,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSimilarItemsSection({
    required List<LoanItem> similarItems,
    required double cardWidth,
    required double cardHeight,
    required double titleSize,
    required double cardPadding,
    required double borderRadius,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(cardPadding),
          child: Text(
            _getSectionTitle(),
            style: GoogleFonts.outfit(
              fontSize: titleSize,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ),
        SizedBox(
          height: cardHeight,
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: cardPadding),
            scrollDirection: Axis.horizontal,
            itemCount: similarItems.length,
            itemBuilder: (context, index) => _buildSimilarItemCard(
              similarItems[index],
              width: cardWidth,
              height: cardHeight,
              borderRadius: borderRadius,
              padding: cardPadding,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerCard({
    required double width,
    required double height,
    required double borderRadius,
    required double padding,
  }) {
    final imageHeight = height * 0.6;

    return Container(
      width: width,
      margin: EdgeInsets.only(right: padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: imageHeight,
              margin: EdgeInsets.all(padding / 2),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(borderRadius / 1.5),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(padding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    height: 16.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Container(
                    width: 80.w,
                    height: 12.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimilarItemCard(
    LoanItem item, {
    required double width,
    required double height,
    required double borderRadius,
    required double padding,
  }) {
    final imageHeight = height * 0.6;
    final fontSize = (width * 0.08).clamp(14.0, 18.0);
    final priceFontSize = (width * 0.09).clamp(15.0, 20.0);
    final tagFontSize = (width * 0.06).clamp(10.0, 14.0);

    return GestureDetector(
      onTap: () => widget.onItemTap(item),
      child: Container(
        width: width,
        margin: EdgeInsets.only(right: padding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(padding / 2),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(borderRadius / 1.5),
                child: CachedNetworkImage(
                  imageUrl: item.imageUrl,
                  height: imageHeight,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(color: Colors.white),
                  ),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(padding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: GoogleFonts.outfit(
                      fontSize: fontSize,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        PriceFormatter.formatPrice(item.price.toString()),
                        style: GoogleFonts.outfit(
                          fontSize: priceFontSize,
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      if (item.category != null)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: padding / 1.5,
                            vertical: padding / 3,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius:
                                BorderRadius.circular(borderRadius / 1.5),
                          ),
                          child: Text(
                            item.category!.name,
                            style: GoogleFonts.outfit(
                              fontSize: tagFontSize,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSectionTitle() {
    switch (widget.loanItem.type) {
      case LoanItemType.car:
        return 'Similar Cars';
      case LoanItemType.house:
        return 'Similar Properties';
      default:
        return 'Similar Items';
    }
  }
}
