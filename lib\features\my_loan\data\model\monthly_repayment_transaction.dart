
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_transaction.dart';

class MonthlyRepaymentTransactionModel extends MonthlyRepaymentTransactionEntity {
  const MonthlyRepaymentTransactionModel({
    required super.id,
    required super.loanId,
    required super.loanPaymentId,
    required super.installmentNumber,
    required super.principalAmount,
    required super.interestAmount,
    required super.totalAmount,
    required super.paidAmount,
    required super.remainingAmount,
    required super.dueDate,
    required super.status,
    required super.penaltyConfig,
    required super.referenceNumber,
    super.sessionId,
    super.paymentMethod,
    super.redirectURL,
    required super.facilitationFee,
    required super.isInPenalty,
    required super.penaltyAmount,
    required super.penaltyStartDate,
    required super.lastPaidDate,
    super.lastPenaltyCalculationDate,
    required super.totalPenaltyPaid,
    super.invoiceURL,
    required super.createdAt,
    required super.updatedAt,
  });

  factory MonthlyRepaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    return MonthlyRepaymentTransactionModel(
      id: AppMapper.safeString(['id']),
      loanId: AppMapper.safeString(['loanId']),
      loanPaymentId: AppMapper.safeString(['loanPaymentId']),
      installmentNumber: AppMapper.safeInt(['installmentNumber']),
      principalAmount: AppMapper.safeString(['principalAmount']),
      interestAmount: AppMapper.safeString(['interestAmount']),
      totalAmount: AppMapper.safeString(['totalAmount']),
      paidAmount: AppMapper.safeString(['paidAmount']),
      remainingAmount: AppMapper.safeString(['remainingAmount']),
      dueDate: AppMapper.safeString(['dueDate']),
      status: AppMapper.safeString(['status']),
      penaltyConfig: PenaltyConfigModel.fromJson(AppMapper.safeMap(json['penaltyConfig'])),
      
      referenceNumber: AppMapper.safeString(['referenceNumber']),
      sessionId: AppMapper.safeString(['sessionId']),
      paymentMethod: AppMapper.safeString(['paymentMethod']),
      redirectURL: AppMapper.safeString(['redirectURL']),
      facilitationFee: AppMapper.safeString(['facilitationFee']),
      isInPenalty: AppMapper.safeBool(['isInPenalty']),
      penaltyAmount: AppMapper.safeString(['penaltyAmount']),
      penaltyStartDate: AppMapper.safeString(['penaltyStartDate']),
      lastPaidDate: AppMapper.safeString(['lastPaidDate']),
      lastPenaltyCalculationDate: AppMapper.safeString(['lastPenaltyCalculationDate']),
      totalPenaltyPaid: AppMapper.safeString(['totalPenaltyPaid']),
    
      invoiceURL: AppMapper.safeString(['invoiceURL']),
      createdAt: AppMapper.safeString(json['createdAt']),
      updatedAt: AppMapper.safeString(json['updatedAt']),
    );
  }
}


class PenaltyConfigModel extends PenaltyConfigEntity {
  const PenaltyConfigModel({
    required super.originalBalance,
    required super.gracePeriodInDays,
  });

  factory PenaltyConfigModel.fromJson(Map<String, dynamic> json) {
    return PenaltyConfigModel(
      originalBalance: AppMapper.safeDouble(json['originalBalance']),
      gracePeriodInDays: AppMapper.safeInt(['gracePeriodInDays']),
    );
  }
}



