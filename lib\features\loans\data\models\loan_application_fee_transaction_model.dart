import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application_fee_transaction.dart';

/// Model for unified loan application fee transaction
class LoanApplicationFeeTransactionModel extends LoanApplicationFeeTransaction {
  const LoanApplicationFeeTransactionModel({
    required super.elstRef,
    required super.billRefNo,
    required super.beneficiaryId,
    required super.beneficiaryName,
    required super.senderName,
    required super.senderPhone,
    required super.senderEmail,
    required super.bankName,
    required super.bankCode,
    required super.senderId,
    required super.transactionOwner,
    required super.authorizationType,
    required super.status,
    required super.vat,
    required super.serviceCharge,
    required super.originalCurrency,
    required super.billAmount,
    required super.createdAt,
    required super.lastModifiedAt,
    required super.loanId,
    required super.loanType,
    required super.transactionType,
    required super.productDetails,
    required super.paidDate,
    required super.paymentMethod,
    required super.paidAmount,
    required super.paymentDetails,
    required super.paymentReference,
  });

  factory LoanApplicationFeeTransactionModel.fromJson(
      Map<String, dynamic> json) {
    return LoanApplicationFeeTransactionModel(
      elstRef: AppMapper.safeString(json['ELSTRef']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      senderEmail: AppMapper.safeString(json['senderEmail']),
      bankName: AppMapper.safeString(json['bankName']),
      bankCode: AppMapper.safeString(json['bankCode']),
      senderId: AppMapper.safeString(json['senderId']),
      transactionOwner: AppMapper.safeString(json['TransactionOwner']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      status: AppMapper.safeString(json['status']),
      vat: AppMapper.safeDouble(json['VAT']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      lastModifiedAt: json['lastModifiedAt'] != null
          ? DateTime.parse(json['lastModifiedAt'] as String)
          : DateTime.now(),
      loanId: AppMapper.safeString(json['loanId']),
      loanType: AppMapper.safeString(json['loanType']),
      transactionType: AppMapper.safeString(json['transactionType']),
      productDetails: TransactionProductDetailsModel.fromJson(
        json['productDetails'] as Map<String, dynamic>,
      ),
      paidDate: json['paidDate'] != null
          ? DateTime.parse(json['paidDate'] as String)
          : DateTime.now(),
      paymentMethod: AppMapper.safeString(json['paymentMethod'] ?? 'WALLET'),
      paidAmount:
          AppMapper.safeDouble(json['paidAmount'] ?? json['billAmount']),
      paymentDetails: json['paymentDetails'] != null
          ? PaymentDetailsModel.fromJson(
              json['paymentDetails'] as Map<String, dynamic>,
            )
          : PaymentDetailsModel(
              walletId: AppMapper.safeString(json['senderId']),
              currency: AppMapper.safeString(json['originalCurrency']),
            ),
      paymentReference:
          AppMapper.safeString(json['paymentReference'] ?? json['billRefNo']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ELSTRef': elstRef,
      'billRefNo': billRefNo,
      'beneficiaryId': beneficiaryId,
      'beneficiaryName': beneficiaryName,
      'senderName': senderName,
      'senderPhone': senderPhone,
      'senderEmail': senderEmail,
      'bankName': bankName,
      'bankCode': bankCode,
      'senderId': senderId,
      'TransactionOwner': transactionOwner,
      'authorization_type': authorizationType,
      'status': status,
      'VAT': vat,
      'serviceCharge': serviceCharge,
      'originalCurrency': originalCurrency,
      'billAmount': billAmount,
      'createdAt': createdAt.toIso8601String(),
      'lastModifiedAt': lastModifiedAt.toIso8601String(),
      'loanId': loanId,
      'loanType': loanType,
      'transactionType': transactionType,
      'productDetails':
          (productDetails as TransactionProductDetailsModel).toJson(),
      'paidDate': paidDate.toIso8601String(),
      'paymentMethod': paymentMethod,
      'paidAmount': paidAmount,
      'paymentDetails': (paymentDetails as PaymentDetailsModel).toJson(),
      'paymentReference': paymentReference,
    };
  }
}

/// Model for transaction details
class TransactionDetailsModel extends TransactionDetails {
  const TransactionDetailsModel({
    required super.elstRef,
    required super.billRefNo,
    required super.billAmount,
    required super.date,
    super.senderName,
  });

  factory TransactionDetailsModel.fromJson(Map<String, dynamic> json) {
    return TransactionDetailsModel(
      elstRef: AppMapper.safeString(json['elstRef'] ?? json['ELSTRef']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      date: AppMapper.safeString(json['date'] ?? json['paidDate']),
      senderName: json['senderName']?.toString(),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'ELSTRef': elstRef,
      'billRefNo': billRefNo,
      'billAmount': billAmount,
      'date': date,
      if (senderName != null) 'senderName': senderName,
    };
  }
}

/// Model for loan item details
class LoanItemDetailsModel extends LoanItemDetails {
  const LoanItemDetailsModel({
    required super.model,
    required super.price,
    super.make,
    super.type,
  });

  factory LoanItemDetailsModel.fromJson(Map<String, dynamic> json) {
    return LoanItemDetailsModel(
      model: AppMapper.safeString(json['model']),
      price: AppMapper.safeString(json['price']),
      make: json['make']?.toString(),
      type: json['type']?.toString(),
    );
  }

  /// Factory for car details
  factory LoanItemDetailsModel.car({
    required String model,
    required String price,
    required String make,
  }) {
    return LoanItemDetailsModel(
      model: model,
      price: price,
      make: make,
    );
  }

  /// Factory for house details
  factory LoanItemDetailsModel.house({
    required String model,
    required String price,
    required String type,
  }) {
    return LoanItemDetailsModel(
      model: model,
      price: price,
      type: type,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'model': model,
      'price': price,
      if (make != null) 'make': make,
      if (type != null) 'type': type,
    };
  }
}

/// Model for payment details
class PaymentDetailsModel extends PaymentDetails {
  const PaymentDetailsModel({
    required super.walletId,
    required super.currency,
  });

  factory PaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return PaymentDetailsModel(
      walletId: AppMapper.safeString(json['walletId']),
      currency: AppMapper.safeString(json['currency']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'walletId': walletId,
      'currency': currency,
    };
  }
}

/// Model for transaction product details
class TransactionProductDetailsModel extends TransactionProductDetails {
  const TransactionProductDetailsModel({
    required super.id,
    required super.name,
    required super.description,
    required super.location,
    required super.condition,
    required super.amenities,
    required super.featureImage,
    required super.productPrice,
    required super.productName,
  });

  factory TransactionProductDetailsModel.fromJson(Map<String, dynamic> json) {
    final productDetails =
        json['productDetails'] as Map<String, dynamic>? ?? {};

    return TransactionProductDetailsModel(
      id: AppMapper.safeString(productDetails['id']),
      name: AppMapper.safeString(productDetails['name']),
      description: AppMapper.safeString(productDetails['description']),
      location: productDetails['location'] != null
          ? LocationDetailsModel.fromJson(
              productDetails['location'] as Map<String, dynamic>,
            )
          : const LocationDetailsModel(
              fieldName: '',
              lng: '0',
              lat: '0',
            ),
      condition: AppMapper.safeString(productDetails['condition']),
      amenities: (productDetails['amenities'] as List<dynamic>?)
              ?.map(
                (amenity) => AmenityDetailsModel.fromJson(
                  amenity as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      featureImage: AppMapper.safeString(productDetails['featureImage']),
      productPrice:
          AppMapper.safeDouble(json['productPrice'] ?? productDetails['price']),
      productName:
          AppMapper.safeString(json['productName'] ?? productDetails['name']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productDetails': {
        'id': id,
        'name': name,
        'description': description,
        'location': (location as LocationDetailsModel).toJson(),
        'condition': condition,
        'amenities': amenities
            .map((amenity) => (amenity as AmenityDetailsModel).toJson())
            .toList(),
        'featureImage': featureImage,
      },
      'productPrice': productPrice,
      'productName': productName,
    };
  }
}

/// Model for location details
class LocationDetailsModel extends LocationDetails {
  const LocationDetailsModel({
    required super.fieldName,
    required super.lng,
    required super.lat,
  });

  factory LocationDetailsModel.fromJson(Map<String, dynamic> json) {
    return LocationDetailsModel(
      fieldName: AppMapper.safeString(json['fieldName'] ?? ''),
      lng: AppMapper.safeString(json['lng'] ?? '0'),
      lat: AppMapper.safeString(json['lat'] ?? '0'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fieldName': fieldName,
      'lng': lng,
      'lat': lat,
    };
  }
}

/// Model for amenity details
class AmenityDetailsModel extends AmenityDetails {
  const AmenityDetailsModel({
    required super.amenity,
    required super.detail,
  });

  factory AmenityDetailsModel.fromJson(Map<String, dynamic> json) {
    return AmenityDetailsModel(
      amenity: json['amenity'] != null
          ? AmenityInfoModel.fromJson(
              json['amenity'] as Map<String, dynamic>,
            )
          : const AmenityInfoModel(name: '', icon: ''),
      detail: AppMapper.safeString(json['detail'] ?? ''),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amenity': (amenity as AmenityInfoModel).toJson(),
      'detail': detail,
    };
  }
}

/// Model for amenity info
class AmenityInfoModel extends AmenityInfo {
  const AmenityInfoModel({
    required super.name,
    required super.icon,
  });

  factory AmenityInfoModel.fromJson(Map<String, dynamic> json) {
    return AmenityInfoModel(
      name: AppMapper.safeString(json['name'] ?? ''),
      icon: AppMapper.safeString(json['icon'] ?? ''),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': icon,
    };
  }
}
