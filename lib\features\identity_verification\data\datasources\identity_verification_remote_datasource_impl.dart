import 'dart:io';
import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/identity_verification/data/datasources/identity_verification_remote_datasource.dart';
import 'package:cbrs/features/identity_verification/data/models/document_upload_response_model.dart';
import 'package:cbrs/features/identity_verification/data/models/identity_document_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class IdentityVerificationRemoteDataSourceImpl
    implements IdentityVerificationRemoteDataSource {
  const IdentityVerificationRemoteDataSourceImpl({
    required ApiService apiService,
  }) : _apiService = apiService;

  final ApiService _apiService;

  @override
  Future<DocumentUploadResponseModel> uploadDocument({
    required File frontPhoto,
    required File backPhoto,
    required File selfiePhoto,
    String documentType = 'id_card',
  }) async {
    try {
      debugPrint('Uploading document: $documentType');
      debugPrint('Front photo path: ${frontPhoto.path}');
      debugPrint('Back photo path: ${backPhoto.path}');
      debugPrint('Selfie photo path: ${selfiePhoto.path}');

      // Create multipart files
      final frontMultipartFile = await MultipartFile.fromFile(
        frontPhoto.path,
        filename: 'front_${DateTime.now().millisecondsSinceEpoch}.jpg',
        contentType: DioMediaType('image', 'jpeg'),
      );

      final backMultipartFile = await MultipartFile.fromFile(
        backPhoto.path,
        filename: 'back_${DateTime.now().millisecondsSinceEpoch}.jpg',
        contentType: DioMediaType('image', 'jpeg'),
      );

      final selfieMultipartFile = await MultipartFile.fromFile(
        selfiePhoto.path,
        filename: 'selfie_${DateTime.now().millisecondsSinceEpoch}.jpg',
        contentType: DioMediaType('image', 'jpeg'),
      );

      // Prepare files map
      final files = <String, MultipartFile>{
        'photo': selfieMultipartFile,
        'idfrontpage': frontMultipartFile,
        'idbackpage': backMultipartFile,
      };

      // Prepare additional fields
      final fields = <String, dynamic>{
        'documentType': documentType,
      };

      final result = await _apiService.put(
        ApiEndpoints.uploadDocument,
        files: files,
        fields: fields,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        (data) {
          debugPrint('Document uploaded successfully response: $data');
          return DocumentUploadResponseModel.fromJson(data);
        },
        (error) {
          debugPrint('Document upload error: ${error.message}');
          throw ApiException(
            message: error.message,
            statusCode: error.statusCode ?? 500,
          );
        },
      );
    } catch (e) {
      debugPrint('Document upload exception: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to upload document: $e',
        statusCode: 500,
      );
    }
  }
}
