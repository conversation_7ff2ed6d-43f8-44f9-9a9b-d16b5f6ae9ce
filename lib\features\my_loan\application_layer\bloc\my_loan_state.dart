import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_transaction.dart';
import 'package:cbrs/features/my_loan/domain/entity/my_loan.dart';
import 'package:cbrs/features/my_loan/domain/entity/payment_history.dart';
import 'package:cbrs/features/my_loan/domain/entity/success_payment.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_loan_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';
import 'package:equatable/equatable.dart';

abstract class RepaymentLoanState extends Equatable {}

class LoanInitialState extends RepaymentLoanState {
  @override
  // TODO: implement props
  List<Object?> get props => [];
}

class RepaymentLoadingState extends RepaymentLoanState {
  @override
  // TODO: implement props
  List<Object?> get props => [];
}
//

class RepaymentLoadedState extends RepaymentLoanState {
  RepaymentLoadedState({
    required this.loans,
    required this.hasNextPage,
    required this.currentPage,
    required this.loanState,
  });
  final List<MyLoanRepaymentDataEntity> loans;
  final bool hasNextPage;
  final int currentPage;
  final String loanState;

  RepaymentLoadedState copyWith({
    List<MyLoanRepaymentDataEntity>? loans,
    bool? hasNextPage,
    int? currentPage,
    String? loanState,
  }) {
    return RepaymentLoadedState(
      loans: loans ?? this.loans,
      loanState: loanState ?? this.loanState,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  @override
  // TODO: implement props
  List<Object?> get props => [loans, hasNextPage, currentPage, loanState];
}

class RepaymentErrorState extends RepaymentLoanState {
  RepaymentErrorState(this.message);
  final String message;

  @override
  // TODO: implement props
  List<Object?> get props => [message];
}

class RepaymentInfoLoadedState extends RepaymentLoanState {
  RepaymentInfoLoadedState(this.loanDetails);
  final LoanRepaymentInfoEntity loanDetails;

  @override
  // TODO: implement props
  List<Object?> get props => [loanDetails];
}

class RepaymentPayState extends RepaymentLoanState {
  RepaymentPayState(this.successRepayment);
  final SuccessLoanRepaymentEntity successRepayment;

  @override
  // TODO: implement props
  List<Object?> get props => [successRepayment];
}

class GeneratedUpfronPayment extends RepaymentLoanState {
  GeneratedUpfronPayment(this.upfrontTransactionEntity);
  final UpfrontTransactionEntity upfrontTransactionEntity;

  @override
  // TODO: implement props
  List<Object?> get props => [upfrontTransactionEntity];
}

class RepaymentHistroyLoadingState extends RepaymentLoanState {
  @override
  // TODO: implement props
  List<Object?> get props => [];
}

class LoanHistoryRepaymentState extends RepaymentLoanState {
  LoanHistoryRepaymentState(this.loanHistory);
  final LoanPaymentHistoryEntity loanHistory;

  @override
  // TODO: implement props
  List<Object?> get props => [];
}

class ConfirmedUpfrontPaymentState extends RepaymentLoanState {
  ConfirmedUpfrontPaymentState(this.upfrontLoan);
  final UpfrontLoanEntity upfrontLoan;

  @override
  // TODO: implement props
  List<Object?> get props => [upfrontLoan];
}


class GenerateMonthlyRepaymentState extends RepaymentLoanState {
  GenerateMonthlyRepaymentState(this.monthlyRepayment);
  final MonthlyRepaymentEntity monthlyRepayment;

  @override
  // TODO: implement props
  List<Object?> get props => [monthlyRepayment];
}


class ConfirmedMonthlyRePaymentState extends RepaymentLoanState {
  ConfirmedMonthlyRePaymentState(this.monthlyRepayment);
  final MonthlyRepaymentTransactionEntity monthlyRepayment;

  @override
  // TODO: implement props
  List<Object?> get props => [monthlyRepayment];
}

class ActionConfirmRuleEngines extends RepaymentLoanState {
  final bool confirmed;
  ActionConfirmRuleEngines(this.confirmed);

    @override
  // TODO: implement props
  List<Object?> get props => [confirmed];

}
