import 'dart:convert';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/error/exceptions.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/pay_by_qr/data/models/qr_generate_response_model.dart';
import 'package:cbrs/features/pay_by_qr/data/models/qr_parse_response_model.dart';
import 'package:flutter/foundation.dart';

abstract class QrRemoteDataSource {
  /// Calls the API endpoint to parse a QR code
  ///
  /// Throws a [ServerException] for all error codes
  Future<QrParseResponseModel> parseQrCode(String qrString);

  /// Calls the API endpoint to generate a QR code
  ///
  /// Throws a [ServerException] for all error codes
  Future<QrGenerateResponseModel> generateQrCode();
}

class QrRemoteDataSourceImpl implements QrRemoteDataSource {
  final ApiService apiService;

  QrRemoteDataSourceImpl({
    required this.apiService,
  });

  @override
  Future<QrParseResponseModel> parseQrCode(String qrString) async {
    try {
      print('🔍 Sending QR string to parse: $qrString');

      final result = await apiService.post(
        ApiEndpoints.parseMerchantQr,
        data: {'qrString': qrString},
        parser: (data) {
          if (data is Map<String, dynamic> &&
              data['success'] == true &&
              data['data'] != null) {
            return QrParseResponseModel.fromJson(
                data['data'] as Map<String, dynamic>);
          }
          throw ServerException(
            message: data is Map<String, dynamic> && data['message'] != null
                ? data['message'].toString()
                : 'Invalid response format from server',
          );
        },
      );

      if (result.isSuccess && result.data != null) {
        final qrParseResponse = result.data!;
        print('🔍 Successfully parsed QR data: $qrParseResponse');
        return qrParseResponse;
      } else {
        print('🔥 QR Parse Error: ${result.error?.message}');
        throw ServerException(
          message: result.error?.message ?? 'Failed to parse QR code',
        );
      }
    } catch (e) {
      print('🔥 QR Parse Error: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to process QR code: ${e.toString()}',
      );
    }
  }

  @override
  Future<QrGenerateResponseModel> generateQrCode() async {
    try {
      print('🔍 Generating QR code');

      final result = await apiService.post(
        ApiEndpoints.generateQr,
        parser: (data) {
          if (data is Map<String, dynamic> &&
              data['success'] == true &&
              data['data'] != null) {
            return QrGenerateResponseModel.fromJson(
                data['data'] as Map<String, dynamic>);
          }
          throw ServerException(
            message: data is Map<String, dynamic> && data['message'] != null
                ? data['message'].toString()
                : 'Invalid response format from server',
          );
        },
      );

      if (result.isSuccess && result.data != null) {
        final qrGenerateResponse = result.data!;
        print('🔍 Successfully generated QR data: $qrGenerateResponse');
        return qrGenerateResponse;
      } else {
        print('🔥 QR Generate Error: ${result.error?.message}');
        throw ServerException(
          message: result.error?.message ?? 'Failed to generate QR code',
        );
      }
    } catch (e) {
      print('🔥 QR Generate Error: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to generate QR code: ${e.toString()}',
      );
    }
  }
}