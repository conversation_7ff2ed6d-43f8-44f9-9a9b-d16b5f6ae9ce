import 'dart:io';

import 'package:cbrs/features/mini_apps/domain/mini_app_permission.dart';
import 'package:permission_handler/permission_handler.dart';

class MiniAppPermissionHandler {
  static Future<bool> checkAndRequestPermissions(
      MiniAppPermission permissions) async {
    Map<Permission, PermissionStatus> statuses = {};

    if (permissions.requiresCamera) {
      statuses[Permission.camera] = await Permission.camera.request();
    }
    if (permissions.requiresLocation) {
      statuses[Permission.location] = await Permission.location.request();
    }
    if (permissions.requiresContacts) {
      statuses[Permission.contacts] = await Permission.contacts.request();
    }
    if (permissions.requiresStorage) {
      if (Platform.isAndroid) {
        statuses[Permission.storage] = await Permission.storage.request();
      } else {
        statuses[Permission.photos] = await Permission.photos.request();
      }
    }

    return !statuses.values.contains(PermissionStatus.denied) &&
        !statuses.values.contains(PermissionStatus.permanentlyDenied);
  }
}
