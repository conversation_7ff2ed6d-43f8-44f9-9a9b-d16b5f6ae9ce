import 'dart:io';

// import 'package:approov_service_flutter_httpclient/approov_service_flutter_httpclient.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/data/models/user_dto.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_event.dart';
import 'package:cbrs/features/profile/data/models/profile_dto.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_event.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/instance_manager.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UserProfileContainer extends StatefulWidget {
  const UserProfileContainer({required this.userDto, super.key});
  final LocalUser userDto;
  @override
  State<UserProfileContainer> createState() => _UserProfileContainerState();
}

class _UserProfileContainerState extends State<UserProfileContainer> {
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  Future<void> _showImageSourceBottomSheet() async {
    final theme = Theme.of(context);
    final parentContext = context;
    await showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.userDto.avatar.isNotEmpty ?? false)
                GestureDetector(
                  onTap: () {
                    debugPrint('Called');
                    Navigator.pop(context);
                    _deleteProfileImage();
                    // parentContext
                    //     .read<ProfileBloc>()
                    //     .add(DeleteProfilePictureEvent());
                  },
                  child: Align(
                    alignment: Alignment.topRight,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        const Text(
                          'Delete',
                          style: TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              Container(
                width: 50,
                height: 50,
                alignment: Alignment.center,
                // padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.person,
                  size: 40,
                  color: theme.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Upload Profile Picture',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Text(
                'Take a picture or choose from gallery',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.camera);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF5B9B8D).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.camera_alt,
                              color: theme.primaryColor,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Take a picture',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              'Camera',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                                color: theme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.gallery);
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.primaryColor.withOpacity(0.1),
                          //  .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.photo_library,
                              color: theme.primaryColor,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Upload from Gallery',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              'Browse',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                                color: theme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    // Initialize status with a default value
    var status = PermissionStatus.denied;
    var permissionMessage = 'Permission Denied';

    // Check permission based on source (camera or gallery)
    if (source == ImageSource.camera) {
      // Request Camera Permission
      status = await Permission.camera.request();
      permissionMessage = 'Camera permission is required to take photos';

      if (status.isDenied) {
        // Request permission again if it was initially denied
        status = await Permission.camera.request();
      }
    } else {
      // Gallery Permission (Photo Library)
      if (Platform.isAndroid) {
        // Android 13+ requires specific photo permission, not just storage
        if (await Permission.photos.status.isDenied) {
          status = await Permission.photos.request();
        } else {
          status = await Permission.photos.status;
        }

        // If photo permission is denied, fall back to storage permission for older versions
        if (status.isDenied) {
          status = await Permission.storage.request();
        }

        permissionMessage = 'Gallery permission is required to pick images';
      } else if (Platform.isIOS) {
        // iOS (photos library access)
        status = await Permission.photos.request();
        debugPrint('REQUESTING GALLERY PERMISSION ON IOS $status');

        // iOS-specific message
        permissionMessage = 'Photos permission is required to pick images';
      }
    }

    try {
      // Handle cases when permission is denied or permanently denied
      if (status.isDenied) {
        // Handle the denial here (e.g., show a message to the user)
        // print(permissionMessage);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(permissionMessage)),
        );
        return;
      } else if (status.isPermanentlyDenied) {
        // Permission has been permanently denied, prompt the user to go to settings
        print(
          'Permission has been permanently denied. Please go to app settings and enable it.',
        );

        // Open app settings to allow the user to manually enable the permission
        openAppSettings();
      } else {
        // Handle other cases (granted, etc.)
        print('Permission granted.');
      }

      if (status.isPermanentlyDenied) {
        if (!context.mounted) return;
        final shouldOpenSettings = await showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Permission Required'),
                content: Text(permissionMessage),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Open Settings'),
                  ),
                ],
              ),
            ) ??
            false;

        if (shouldOpenSettings as bool) {
          await openAppSettings();
        }
        return;
      }

      final image = await _picker.pickImage(
        source: source,
        imageQuality: 70,
      );

      if (image != null && context.mounted) {
        await _uploadImage(File(image.path));
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to pick image. Please try again.'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // choose gallery method

  File? image;
  Future _takeCamera() async {
//  Navigator.push(context, MaterialPageRoute(builder: (BuildContext context)=> HomeScreen()));

    try {
      final takeImage = await ImagePicker()
          .pickImage(source: ImageSource.camera, imageQuality: 100);
      if (takeImage == null) return;

      final temp = File(takeImage.path);
      final tempImage = await _cropImage(temp);

      setState(() {
        image = tempImage as File?;
      });
    } catch (err) {}
  }

  // choose gallery methods
  Future pickImage() async {
//  Navigator.push(context, MaterialPageRoute(builder: (BuildContext context)=> HomeScreen()));
    try {
      final image = await ImagePicker().pickImage(source: ImageSource.gallery);
      if (image == null) return;
      final temp = File(image.path);
      final tempImage = await _cropImage(temp);

      setState(() {
        this.image = tempImage as File;
      });
    } on PlatformException catch (err) {
      print('failed to pick images');
    }
  }

  // cropping images
  Future _cropImage(File imageFile) async {
    try {
      final croppedImg = await ImageCropper().cropImage(
        sourcePath: imageFile.path,
        compressQuality: 100,
        // uiSettings: [AndroidUiSettings(cropStyle: CropStyle.rectangle)],
      );
      if (croppedImg == null)
        return;
      else {
        return File(croppedImg.path);
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> _uploadImage(File imageFile) async {
    setState(() => _isLoading = true);

    try {
      final dio = Dio();

      (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
          (client) {
        // return ApproovHttpClient(ApiConstants.approovConfig);
        return client; // Use default client for now
      };

      final formData = FormData.fromMap({
        'profilepic': await MultipartFile.fromFile(imageFile.path),
      });

      final token =
          await getAuthToken(); // Implement this method to get the token
      debugPrint('Profile pic upload token $token >>> $formData');
      final response = await dio.post(
        '${ApiEndpoints.baseUrl}/members/uploadpicture',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      // debugPrint("repsosne ${response.data}");

      if (response.statusCode == 200) {
        setState(() {
          widget.userDto.copyWith(avatar: response.data['image'] as String);
        });

        // context.read<ProfileBloc>().add(onCacheProfile( widget.profile));

        debugPrint('Avatar22 $response');
        debugPrint('[prfile vatar ] ${widget.userDto.avatar}');

        final updatedProfile =
            widget.userDto.copyWith(avatar: response.data['image'] as String);

        debugPrint('updatedProfile $updatedProfile');
        // TODO - uncomment
        // context.read<ProfileBloc>().add(onCacheProfile(updatedProfile));
      }
    } catch (e) {
      debugPrint('Error uploading image: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to upload image')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteProfileImage() async {
    setState(() => _isLoading = true);

    try {
      final dio = Dio();

      // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
      //     (client) {
      //   return ApproovHttpClient(ApiConstants.approovConfig);
      //   return client; // Use default client for now
      // };
      final token = await getAuthToken();
      final response = await dio.post(
        '${ApiEndpoints.baseUrl}/members/deletepicture',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          sendTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
          validateStatus: (status) {
            debugPrint('Response status: $status');
            return status != null && status < 500;
          },
        ),
      );

      debugPrint('Response of delete  ${response.data} ');
      if (response.statusCode == 200) {
        setState(() {
          // widget.profile.copyWith(avatar: '');
        });

        CustomToastification(
          context,
          message: response.data['message'].toString(),
          isError: !(response.data['success'] as bool),
        );

        // context.read<ProfileBloc>().add(onCacheProfile( widget.profile));

        debugPrint('Avatar22 $response');

        final updatedProfile = widget.userDto.copyWith(avatar: '');

        debugPrint('updatedProfile $updatedProfile');
        // uncomment
        // context.read<ProfileBloc>().add(onCacheProfile(updatedProfile));
      }
    } catch (e) {
      debugPrint('Error in delete image: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to delete')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String convertToSentenceCase(String name) {
    final words = name.split(' ');
    final capitalizedWords = words.map((word) {
      if (word.isNotEmpty) {
        return word[0].toUpperCase() + word.substring(1);
      } else {
        return word;
      }
    }).toList();
    return capitalizedWords.join(' ');
  }

  Future<String?> getAuthToken() async {
    final authLocalDataSource = sl<AuthLocalDataSource>();
    return authLocalDataSource.getAuthToken();
  }

  @override
  Widget build(BuildContext context) {
    final name =
        '${widget.userDto.firstName} ${widget.userDto.middleName} ${widget.userDto.lastName}'
            .split(' ');

    final profileTitle = name.length > 1
        ? "${name[0][0]}${name.last[0]}".toUpperCase()
        : name[0][0];
    final isEmailVerified = widget.userDto.isEmailVerified;
    final isPhoneVerified = widget.userDto.isPhoneVerified;

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(vertical: 12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.04),
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).primaryColor,
                    width: 2,
                  ),
                ),
                child: _isLoading
                    ? Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: const CircleAvatar(radius: 50),
                      )
                    : ClipRRect(
                        borderRadius: BorderRadius.circular(50),
                        child: widget.userDto.avatar.isNotEmpty
                            ? CircleAvatar(
                                radius: 50,
                                backgroundImage:
                                    NetworkImage(widget.userDto.avatar),
                                onBackgroundImageError:
                                    (exception, stackTrace) {
                                  debugPrint(
                                    'Error loading avatar: $exception',
                                  );
                                },
                              )
                            : Container(
                                height: 100,
                                width: 100,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: Get.find<GetAppThemeController>()
                                          .isBirrTheme
                                          .value
                                      ? LightModeTheme().primaryGradient
                                      : LightModeTheme().usdGradient,

                                  // LinearGradient(
                                  //   colors: [
                                  //     Color(0xFF5B9B8D),
                                  //    Theme.of(context).primaryColor,
                                  //   ],
                                  // ),
                                ),
                                child: Center(
                                  child: Text(
                                    profileTitle,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ),
                      ),
              ),
              Positioned(
                right: 0,
                top: 0,
                child: GestureDetector(
                  onTap: _showImageSourceBottomSheet,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      // color: const Color(0xFF5B9B8D),
                      gradient:
                          Get.find<GetAppThemeController>().isBirrTheme.value
                              ? LightModeTheme().primaryGradient
                              : LightModeTheme().usdGradient,

                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            '${widget.userDto.fullName}',
            // convertToSentenceCase(widget.userDto.firstName),
            style: GoogleFonts.outfit(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                isEmailVerified
                    ? (widget.userDto.email?.toLowerCase() ?? '')
                    : (widget.userDto.phoneNumber ?? ''),
                style: GoogleFonts.outfit(
                  fontSize: 14,
                  color: Colors.grey,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              if (isEmailVerified)
                Icon(
                  Icons.verified,
                  color: Theme.of(context).primaryColor,
                  size: 15,
                )
              else
                isPhoneVerified
                    ? Icon(
                        Icons.verified,
                        color: Theme.of(context).primaryColor,
                        size: 15,
                      )
                    : const SizedBox.shrink(),
            ],
          ),
        ],
      ),
    );
  }
}
