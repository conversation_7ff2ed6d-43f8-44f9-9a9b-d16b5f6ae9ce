import 'package:equatable/equatable.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_confirmation.dart';

/// Base class for all loan states
abstract class LoanState extends Equatable {
  const LoanState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class LoanInitial extends LoanState {
  const LoanInitial();
}

/// Loading state for initial load
class LoanLoading extends LoanState {
  const LoanLoading();
}

/// Loading state for pagination (loading more items)
class LoanLoadingMore extends LoanState {
  const LoanLoadingMore({
    required this.currentItems,
    required this.currentPage,
    required this.hasReachedMax,
  });

  final List<LoanItem> currentItems;
  final int currentPage;
  final bool hasReachedMax;

  @override
  List<Object?> get props => [currentItems, currentPage, hasReachedMax];
}

/// Loading state for refresh
class LoanRefreshing extends LoanState {
  const LoanRefreshing({
    required this.currentItems,
  });

  final List<LoanItem> currentItems;

  @override
  List<Object?> get props => [currentItems];
}

/// Success state with loan items
class LoanLoaded extends LoanState {
  const LoanLoaded({
    required this.items,
    required this.currentPage,
    required this.hasReachedMax,
    required this.totalItems,
    this.isFiltered = false,
    this.currentCategoryId,
    this.searchQuery,
  });

  final List<LoanItem> items;
  final int currentPage;
  final bool hasReachedMax;
  final int totalItems;
  final bool isFiltered;
  final String? currentCategoryId;
  final String? searchQuery;

  /// Create a copy with updated values
  LoanLoaded copyWith({
    List<LoanItem>? items,
    int? currentPage,
    bool? hasReachedMax,
    int? totalItems,
    bool? isFiltered,
    String? currentCategoryId,
    String? searchQuery,
  }) {
    return LoanLoaded(
      items: items ?? this.items,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      totalItems: totalItems ?? this.totalItems,
      isFiltered: isFiltered ?? this.isFiltered,
      currentCategoryId: currentCategoryId ?? this.currentCategoryId,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  /// Check if we can load more items
  bool get canLoadMore => !hasReachedMax && items.isNotEmpty;

  /// Check if this is an empty result
  bool get isEmpty => items.isEmpty;

  /// Check if this is the first page
  bool get isFirstPage => currentPage == 1;

  @override
  List<Object?> get props => [
        items,
        currentPage,
        hasReachedMax,
        totalItems,
        isFiltered,
        currentCategoryId,
        searchQuery,
      ];
}

/// Error state
class LoanError extends LoanState {
  const LoanError({
    required this.message,
    this.currentItems = const [],
    this.isLoadMoreError = false,
  });

  final String message;
  final List<LoanItem> currentItems;
  final bool isLoadMoreError;

  @override
  List<Object?> get props => [message, currentItems, isLoadMoreError];
}

/// Empty state when no items are found
class LoanEmpty extends LoanState {
  const LoanEmpty({
    this.message = 'No loan items found',
    this.isFiltered = false,
  });

  final String message;
  final bool isFiltered;

  @override
  List<Object?> get props => [message, isFiltered];
}

// ============================================================================
// LOAN APPLICATION STATES (OTP/PIN Confirmation)
// ============================================================================

/// Initial state for loan application
class LoanApplicationInitial extends LoanState {
  const LoanApplicationInitial();
}

/// Loading state for loan application confirmation
class LoanApplicationLoading extends LoanState {
  const LoanApplicationLoading();
}

/// State when loan application is successful
class LoanApplicationSuccess extends LoanState {
  const LoanApplicationSuccess({
    required this.application,
  });

  final LoanApplication application;

  @override
  List<Object?> get props => [application];
}

/// State when generating application fee transaction
class GeneratingApplicationTransaction extends LoanState {
  const GeneratingApplicationTransaction();
}

/// State when application fee transaction is generated successfully
class ApplicationTransactionGenerated extends LoanState {
  const ApplicationTransactionGenerated({
    required this.transaction,
  });

  final dynamic transaction;

  @override
  List<Object?> get props => [transaction];
}

/// State when application fee transaction generation fails
class ApplicationTransactionError extends LoanState {
  const ApplicationTransactionError({
    required this.message,
  });

  final String message;

  @override
  List<Object?> get props => [message];
}

/// State when confirming loan payment
class ConfirmingLoanPayment extends LoanState {
  const ConfirmingLoanPayment();

  @override
  List<Object?> get props => [];
}

/// State when loan payment confirmation is successful
class LoanPaymentConfirmed extends LoanState {
  const LoanPaymentConfirmed({
    required this.confirmation,
  });

  final LoanPaymentConfirmation confirmation;

  @override
  List<Object?> get props => [confirmation];
}

/// State when loan payment confirmation fails
class LoanPaymentConfirmationError extends LoanState {
  const LoanPaymentConfirmationError({
    required this.message,
  });

  final String message;

  @override
  List<Object?> get props => [message];
}
