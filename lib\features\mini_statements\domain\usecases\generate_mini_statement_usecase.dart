// domain/usecases/generate_mini_statement.dart
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/mini_statements/domain/repository/mini_statement_repository.dart';
import 'package:equatable/equatable.dart';

class GenerateMiniStatementUsecase
    extends UsecaseWithParams<String, GenerateMiniStatementParams> {
  GenerateMiniStatementUsecase(this._repository);
  final MiniStatementRepository _repository;

  @override
  ResultFuture<String> call(GenerateMiniStatementParams params) {
    return _repository.generateMiniStatements(
      startDate: params.startDate,
      endDate: params.endDate,
      Currency: params.currency,
    );
  }
}

class GenerateMiniStatementParams extends Equatable {
  const GenerateMiniStatementParams({
    required this.startDate,
    required this.endDate,
    required this.currency,
  });
  final String startDate;
  final String endDate;
  final String currency;

  @override
  List<Object?> get props => [startDate, endDate, currency];
}
