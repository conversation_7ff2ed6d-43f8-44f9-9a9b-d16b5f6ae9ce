import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_providers_entity.dart';

class TopUpProvidersModel extends TopUpProvidersEntity {
  TopUpProvidersModel({required super.providers});

  factory TopUpProvidersModel.fromJson(Map<String, dynamic> json) {
    return TopUpProvidersModel(
      providers: AppMapper.safeList<dynamic>(json['data'])
          .map((item) => ProvidersModel.fromJson(AppMapper.safeMap(item)))
          .toList(),

   
    );
  }
}

class ProvidersModel extends ProvidersEntity {
  const ProvidersModel({
    required super.id,
    required super.name,
    required super.code,
    required super.merchantType,
    required super.logo,
  });

  factory ProvidersModel.fromJson(Map<String, dynamic> json) {
    return ProvidersModel(
      id: json['_id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      merchantType: json['merchantType'] as String,
      logo: json['logo'] as String,
    );
  }
}
