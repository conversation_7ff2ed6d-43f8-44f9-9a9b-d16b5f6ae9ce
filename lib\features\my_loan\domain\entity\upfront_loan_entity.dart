import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';
import 'package:equatable/equatable.dart';

class UpfrontLoanEntity extends Equatable {
  final String id;
  final String loanId;
  final String productId;
  final String memberId;
  final String status;
  final String upfrontPaymentPercentage;
  final String interestRate;
  final String upfrontPaymentAmount;
  final String loanAmount;
  final String monthlyPayment;
  // final TransactionEntity applicationFeeTransaction;
  final UpfrontTransactionEntity upfrontPaymentTransaction;
  final String startDate;
  final String endDate;
  final String totalPaid;
  final String totalPrincipalPaid;
  final String totalInterestPaid;
  final String totalPenaltiesPaid;
  final String remainingBalance;
  final String totalPayment;
  final String? lastPaymentDate;
  final String nextPaymentDate;
  final bool isOverdue;
  final int totalInstallments;
  final int paidInstallments;
  final int missedInstallments;
  final bool isAutoRepay;
  final String createdAt;
  final String? completedDate;
  final String updatedAt;
  final String approvedAt;
  final String approvedBy;
  final String? rejectedBy;
  final String? rejectedAt;
  final String? rejectionReason;
  final String lastModifiedAt;
  final String loanApplicationCode;

  const UpfrontLoanEntity({
    required this.id,
    required this.loanId,
    required this.productId,
    required this.memberId,
    required this.status,
    required this.upfrontPaymentPercentage,
    required this.interestRate,
    required this.upfrontPaymentAmount,
    required this.loanAmount,
    required this.monthlyPayment,
    // required this.applicationFeeTransaction,
    required this.upfrontPaymentTransaction,
    required this.startDate,
    required this.endDate,
    required this.totalPaid,
    required this.totalPrincipalPaid,
    required this.totalInterestPaid,
    required this.totalPenaltiesPaid,
    required this.remainingBalance,
    required this.totalPayment,
    required this.lastPaymentDate,
    required this.nextPaymentDate,
    required this.isOverdue,
    required this.totalInstallments,
    required this.paidInstallments,
    required this.missedInstallments,
    required this.isAutoRepay,
    required this.createdAt,
    required this.completedDate,
    required this.updatedAt,
    required this.approvedAt,
    required this.approvedBy,
    required this.rejectedBy,
    required this.rejectedAt,
    required this.rejectionReason,
    required this.lastModifiedAt,
    required this.loanApplicationCode,
  });

  @override
  List<Object?> get props => [
        id,
        loanId,
        productId,
        memberId,
        status,
        upfrontPaymentPercentage,
        interestRate,
        upfrontPaymentAmount,
        loanAmount,
        monthlyPayment,
        upfrontPaymentTransaction,
        startDate,
        endDate,
        totalPaid,
        totalPrincipalPaid,
        totalInterestPaid,
        totalPenaltiesPaid,
        remainingBalance,
        totalPayment,
        lastPaymentDate,
        nextPaymentDate,
        isOverdue,
        totalInstallments,
        paidInstallments,
        missedInstallments,
        isAutoRepay,
        createdAt,
        completedDate,
        updatedAt,
        approvedAt,
        approvedBy,
        rejectedBy,
        rejectedAt,
        rejectionReason,
        lastModifiedAt,
        loanApplicationCode,
      ];
}
/*
class PaidUpfrontTransaction extends Equatable {
  final String loanId;
  final String status;
  final String elstRef;
  final String ftNumber;
  final String bankCode;
  final String bankName;
  final String loanType;
  final String paidDate;
  final String senderId;
  final String billRefNo;
  final String createdAt;
  final double billAmount;
  final double paidAmount;
  final String senderName;
  final String senderPhone;
  final String paymentMethod;
  final String lastModifiedAt;
  final PaymentDetailsEntity paymentDetails;
  final String transactionType;
  final String transactionOwner;
  final String originalCurrency;
  final String paymentReference;
  final String? authorizationType;
  final String? beneficiaryId;
  final String? beneficiaryName;
  final String? percentage;
  final double? vat;
  final double? serviceCharge;
  final String? walletFTNumber;
  final String? lastModified;

  const PaidUpfrontTransaction({
    required this.loanId,
    required this.status,
    required this.elstRef,
    required this.ftNumber,
    required this.bankCode,
    required this.bankName,
    required this.loanType,
    required this.paidDate,
    required this.senderId,
    required this.billRefNo,
    required this.createdAt,
    required this.billAmount,
    required this.paidAmount,
    required this.senderName,
    required this.senderPhone,
    required this.paymentMethod,
    required this.lastModifiedAt,
    required this.paymentDetails,
    required this.transactionType,
    required this.transactionOwner,
    required this.originalCurrency,
    required this.paymentReference,
    this.authorizationType,
    this.beneficiaryId,
    this.beneficiaryName,
    this.percentage,
    this.vat,
    this.serviceCharge,
    this.walletFTNumber,
    this.lastModified,
  });

  @override
  List<Object?> get props => [
        loanId,
        status,
        elstRef,
        ftNumber,
        bankCode,
        bankName,
        loanType,
        paidDate,
        senderId,
        billRefNo,
        createdAt,
        billAmount,
        paidAmount,
        senderName,
        senderPhone,
        paymentMethod,
        lastModifiedAt,
        paymentDetails,
        transactionType,
        transactionOwner,
        originalCurrency,
        paymentReference,
        authorizationType,
        beneficiaryId,
        beneficiaryName,
        percentage,
        vat,
        serviceCharge,
        walletFTNumber,
        lastModified,
      ];
}

class PaymentDetailsEntity extends Equatable {
  final String currency;
  final String walletId;

  const PaymentDetailsEntity({
    required this.currency,
    required this.walletId,
  });

  @override
  List<Object?> get props => [currency, walletId];
}
*/