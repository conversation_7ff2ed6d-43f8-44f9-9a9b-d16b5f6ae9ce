import 'package:equatable/equatable.dart';

class LoanCategory extends Equatable {
  const LoanCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    this.isDeleted = false,
    this.createdAt,
    this.updatedAt,
  });
  final String id;
  final String name;
  final String description;
  final String icon;
  final bool isDeleted;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        icon,
        isDeleted,
        createdAt,
        updatedAt,
      ];
}
