part of 'loan_categories_cubit.dart';

abstract class LoanCategoriesState extends Equatable {
  const LoanCategoriesState();

  @override
  List<Object?> get props => [];
}

class LoanCategoriesInitial extends LoanCategoriesState {
  const LoanCategoriesInitial();
}

class LoanCategoriesLoading extends LoanCategoriesState {
  const LoanCategoriesLoading();
}

class LoanCategoriesLoaded extends LoanCategoriesState {
  const LoanCategoriesLoaded(this.categories);

  final List<LoanCategory> categories;

  @override
  List<Object?> get props => [categories];
}

class LoanCategoryLoaded extends LoanCategoriesState {
  const LoanCategoryLoaded(this.category);

  final LoanCategory category;

  @override
  List<Object?> get props => [category];
}

class LoanCategoriesError extends LoanCategoriesState {
  const LoanCategoriesError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}
