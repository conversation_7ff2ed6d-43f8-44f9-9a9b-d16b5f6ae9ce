import 'package:bloc/bloc.dart';
import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/features/loans/domain/usecases/get_all_loan_categories_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/get_loan_categories_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/get_loan_category_by_id_usecase.dart';

import 'package:equatable/equatable.dart';

part 'loan_categories_state.dart';

class LoanCategoriesCubit extends Cubit<LoanCategoriesState> {
  LoanCategoriesCubit({
    required GetAllLoanCategories getAllLoanCategories,
    required GetLoanCategories getLoanCategories,
    required GetLoanCategoryById getLoanCategoryById,
  })  : _getAllLoanCategories = getAllLoanCategories,
        _getLoanCategories = getLoanCategories,
        _getLoanCategoryById = getLoanCategoryById,
        super(const LoanCategoriesInitial());

  final GetAllLoanCategories _getAllLoanCategories;
  final GetLoanCategories _getLoanCategories;
  final GetLoanCategoryById _getLoanCategoryById;

  Future<void> fetchAllCategories() async {
    emit(const LoanCategoriesLoading());

    final result = await _getAllLoanCategories();

    result.fold(
      (failure) => emit(LoanCategoriesError(failure.message)),
      (categories) => emit(LoanCategoriesLoaded(categories)),
    );
  }

  Future<void> fetchCategories({
    int page = 1,
    int limit = 10,
    String? loanType,
  }) async {
    emit(const LoanCategoriesLoading());

    final result = await _getLoanCategories(
      GetLoanCategoriesParams(
        page: page,
        limit: limit,
        loanType: loanType,
      ),
    );

    result.fold(
      (failure) => emit(LoanCategoriesError(failure.message)),
      (categories) => emit(LoanCategoriesLoaded(categories)),
    );
  }

  Future<void> fetchCategoryById(String categoryId) async {
    emit(const LoanCategoriesLoading());

    final result = await _getLoanCategoryById(
      GetLoanCategoryByIdParams(categoryId: categoryId),
    );

    result.fold(
      (failure) => emit(LoanCategoriesError(failure.message)),
      (category) => emit(LoanCategoryLoaded(category)),
    );
  }

  void resetState() {
    emit(const LoanCategoriesInitial());
  }
}
