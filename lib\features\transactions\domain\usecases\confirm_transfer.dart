import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transactions/data/models/confirm_transfer_response_model.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:equatable/equatable.dart';

class ConfirmTransfer extends UsecaseWithParams<ConfirmTransferResponseModel,
    ConfirmTransferParams> {
  const ConfirmTransfer(this._repository);
  final TransactionRepository _repository;

  @override
  ResultFuture<ConfirmTransferResponseModel> call(
      ConfirmTransferParams params) async {
    return _repository.confirmTransfer(
      pin: params.pin,
      billRefNo: params.billRefNo,
      transactionType: params.transactionType.value,
      otp: params.otp,
    );
  }
}

class ConfirmTransferParams extends Equatable {
  const ConfirmTransferParams({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
    this.otp,
  });

  final String pin;
  final String billRefNo;
  final TransactionType transactionType;
  final String? otp;

  @override
  List<Object?> get props => [pin, billRefNo, transactionType, otp];
}
