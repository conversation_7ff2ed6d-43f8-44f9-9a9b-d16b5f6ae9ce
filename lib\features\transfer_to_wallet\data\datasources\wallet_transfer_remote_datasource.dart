import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/send_money/data/models/transfer_status_model.dart';
import 'package:cbrs/features/transactions/data/datasources/transaction_remote_datasource.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/contact_lookup_response_model.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/member_lookup_response_model.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/otp_resend_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/transfer_confirmation_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_info_model.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/wallet_info.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:retry/retry.dart';

abstract class WalletTransferRemoteDataSource {
  Future<List<ContactLookupResponseModel>> lookupContacts({
    required List<String> phoneNumbers,
  });

  Future<MemberLookupResponse> lookupMember({
    String? email,
    String? phoneNumber,
  });

  Future<WalletTransferResponse> transferToWallet({
    required double amount,
    required String currency,
    String? beneficiaryEmail,
    String? beneficiaryPhone,
    String? recipientID,
  });

  Future<void> validateTransferAmount({
    required double amount,
    required String currency,
  });

  Future<TransferStatusModel> checkTransferStatus({
    required String transactionId,
  });

  Future<WalletTransferResponse> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  });

  Future<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  });

  Future<WalletTransferResponse> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  });

  Future<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String productType,
  });

  Future<String> generateReceipt({
    required String billRefNo,
  });

  Future<Map<String, String>> getUsersAvatar({
    required List<String> userIds,
  });
}

class WalletTransferRemoteDataSourceImpl
    implements WalletTransferRemoteDataSource {
  WalletTransferRemoteDataSourceImpl({
    required ApiService apiService,
    required Box<dynamic> authBox,
    required AuthLocalDataSource authLocalDataSource,
  })  : _apiService = apiService,
        _authBox = authBox,
        _authLocalDataSource = authLocalDataSource;
  final ApiService _apiService;
  final Box<dynamic> _authBox;
  final AuthLocalDataSource _authLocalDataSource;

  final Map<String, DateTime> _lastTransactions = {};

  String _generateTransactionKey({
    required double amount,
    required String currency,
    String? beneficiaryEmail,
    String? beneficiaryPhone,
    String? recipientID,
  }) {
    return '${beneficiaryEmail ?? ''}_${beneficiaryPhone ?? ''}_${recipientID ?? ''}_${amount}_$currency';
  }

  @override
  Future<List<ContactLookupResponseModel>> lookupContacts({
    required List<String> phoneNumbers,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.contactLookup,
        data: {
          'phoneNumber': phoneNumbers,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      // final response = await _safeApiCall(
      //   () => _dio.post<Map<String, dynamic>>(
      //     ApiEndpoints.contactLookup,
      //     data: {
      //       'phoneNumber': phoneNumbers,
      //     },
      //   ),
      // );

      return result.fold((response) {
        final existingMembers = response['existingMembers'] as List;
        return existingMembers
            .map(
              (member) => ContactLookupResponseModel.fromJson(
                member as Map<String, dynamic>,
              ),
            )
            .toList();
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

    // if (response.data == null) {
    //   throw const ApiException(
    //     message: 'Empty response from server',
    //     statusCode: 500,
    //   );
    // }

    // final existingMembers = response.data!['existingMembers'] as List;
    // return existingMembers
    //     .map(
    //       (member) => ContactLookupResponseModel.fromJson(
    //         member as Map<String, dynamic>,
    //       ),
    //     )
    //     .toList();
  }

  @override
  Future<MemberLookupResponse> lookupMember({
    String? email,
    String? phoneNumber,
  }) async {
    debugPrint('\n=== Member Lookup Request ===');
    debugPrint('Email: $email');
    debugPrint('Phone: $phoneNumber');

    try {
      final result = await _apiService.post(
        ApiEndpoints.memberLookup,
        data: {
          if (email != null) 'email': email,
          if (phoneNumber != null) 'phoneNumber': phoneNumber,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return MemberLookupResponseModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

    // final response = await _safeApiCall(
    //   () => _dio.post<DataMap>(
    //     ApiEndpoints.memberLookup,
    //     data: {
    //       if (email != null) 'email': email,
    //       if (phoneNumber != null) 'phoneNumber': phoneNumber,
    //     },
    //   ),
    // );

    //   debugPrint('\n=== Member Lookup Response ===');
    //   debugPrint('Status Code: ${response.statusCode}');
    //   debugPrint('Response Data: ${response.data}');

    //   if (response.data == null) {
    //     throw const ApiException(
    //       message: 'Empty response from server',
    //       statusCode: 500,
    //     );
    //   }

    //   // Handle 404 Not Found case
    //   if (response.statusCode == 404) {
    //     throw NotFoundException(
    //       message: response.data?['message'] as String? ?? 'Member not found',
    //     );
    //   }

    //   // Ensure we're accessing the correct data structure
    //   final memberData = response.data!['data'] as Map<String, dynamic>?;
    //   if (memberData == null) {
    //     throw const ApiException(
    //       message: 'Invalid response format',
    //       statusCode: 500,
    //     );
    //   }

    //   return MemberLookupResponseModel.fromJson({
    //     'data': memberData,
    //     'success': response.data!['success'] ?? false,
    //     'statusCode': response.data!['statusCode'] ?? 500,
    //   });
    // } catch (e) {
    //   debugPrint('\n=== Member Lookup Error ===');
    //   debugPrint('Error Type: ${e.runtimeType}');
    //   debugPrint('Error Details: $e');

    //   if (e is NotFoundException) {
    //     rethrow;
    //   }

    //   if (e is ApiException) {
    //     rethrow;
    //   }

    //   throw ApiException(
    //     message: e.toString(),
    //     statusCode: 500,
    //   );
    // }
  }

  @override
  Future<WalletTransferResponse> transferToWallet({
    required double amount,
    required String currency,
    String? beneficiaryEmail,
    String? beneficiaryPhone,
    String? recipientID,
  }) async {
    final transactionKey = _generateTransactionKey(
      beneficiaryEmail: beneficiaryEmail,
      beneficiaryPhone: beneficiaryPhone,
      recipientID: recipientID,
      amount: amount,
      currency: currency,
    );

    debugPrint('request for wallet transfer amount $amount');
    debugPrint(
      'request for wallet transfer beneficiaryEmail $beneficiaryEmail',
    );
    debugPrint(
      'request for wallet transfer beneficiaryPhone $beneficiaryPhone',
    );
    debugPrint('request for wallet transfer recipientID $recipientID');
    debugPrint('request for wallet transfer currency $currency');

    try {
      final result = await _apiService.post(
        ApiEndpoints.walletTransfer,
        data: {
          if (beneficiaryEmail != null) 'beneficiaryEmail': beneficiaryEmail,
          if (beneficiaryPhone != null) 'beneficiaryPhone': beneficiaryPhone,
          if (recipientID != null) 'beneficiaryId': recipientID,
          'amount': amount,
          'currency': currency,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return WalletTransferResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

    //   final response = await retry(
    //     () => _safeApiCall(
    //       () => _dio.post<Map<String, dynamic>>(
    //         ApiEndpoints.walletTransfer,
    //         data: {
    //           if (beneficiaryEmail != null)
    //             'beneficiaryEmail': beneficiaryEmail,
    //           if (beneficiaryPhone != null)
    //             'beneficiaryPhone': beneficiaryPhone,
    //           if (recipientID != null) 'beneficiaryId': recipientID,
    //           'amount': amount,
    //           'currency': currency,
    //         },
    //       ),
    //     ),
    //     retryIf: (e) => e is DioException && e.type != DioExceptionType.cancel,
    //     maxAttempts: 3,
    //     delayFactor: const Duration(seconds: 2),
    //   );

    //   if (response.data == null) {
    //     throw const ApiException(
    //       message: 'Empty response from server',
    //       statusCode: 500,
    //     );
    //   }

    //   // Store successful transaction
    //   _lastTransactions[transactionKey] = DateTime.now();

    //   debugPrint('reponse from remote data source of wallet trasfer $response');

    //   return WalletTransferResponse.fromJson(response.data!);
    // } catch (e) {
    //   debugPrint('Stack Trace: ${StackTrace.current}');
    //   rethrow;
    // }
  }

  @override
  Future<void> validateTransferAmount({
    required double amount,
    required String currency,
  }) async {
    // await _safeApiCall(
    //   () => _dio.post<Map<String, dynamic>>(
    //     ApiEndpoints.validateTransferAmount,
    //     data: {
    //       'amount': amount,
    //       'currency': currency,
    //     },
    //   ),
    // );
  }

  @override
  Future<TransferStatusModel> checkTransferStatus({
    required String transactionId,
  }) async {
    try {
      final result = await _apiService.get(
        '${ApiEndpoints.transferStatus}/$transactionId',
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return TransferStatusModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

    // final response = await _safeApiCall(
    //   () => _dio.get<Map<String, dynamic>>(
    //     '${ApiEndpoints.transferStatus}/$transactionId',
    //   ),
    // );

    // if (response.data == null) {
    //   throw const ApiException(
    //     message: 'Empty response from server',
    //     statusCode: 500,
    //   );
    // }

    // return TransferStatusModel.fromJson(response.data!);
  }

  @override
  Future<WalletTransferResponse> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.confirmPayment,
        data: {
          'PIN': pin,
          'billRefNo': billRefNo,
          'transactionType': 'wallet_transfer',
          'authType': otp != null ? 'PIN_AND_OTP' : 'PIN',
          'otpCode': otp ?? '',
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return WalletTransferResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }
/*

      final response = await _dio.post(
        ApiEndpoints.confirmPayment,
        data: {
          'PIN': pin,
          'billRefNo': billRefNo,
          'transactionType': 'wallet_transfer',
          'authType': otp != null ? 'PIN_AND_OTP' : 'PIN',
          'otpCode': otp ?? '',
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
          validateStatus: (status) => true,
        ),
      );

      debugPrint('Resppspspp checl $response');
      if (response.statusCode == 401) {
        throw const ApiException(
          message: 'Session expired. Please login again.',
          statusCode: 401,
        );
      }

      if (response.statusCode == 400) {
        final errorMessage =
            (response.data as Map<String, dynamic>)['message']?.toString() ??
                'Invalid PIN or OTP';
        throw ServerException(message: errorMessage, statusCode: 400);
      }

      if (response.data == null) {
        throw const ServerException(
          message: 'Invalid response from server',
          statusCode: 500,
        );
      }

      // Convert TransferConfirmationResponse to WalletTransferResponse
      final confirmationResponse = TransferConfirmationResponse.fromJson(
        response.data as Map<String, dynamic>,
      );

      final responseData = response.data as Map<String, dynamic>;

      debugPrint(
        'Confirmation response bii ${confirmationResponse.data.billAmount}',
      );
      return WalletTransferResponse.fromJson({
        'statusCode': response.statusCode,
        'success': response.statusCode == 200,
        'message': confirmationResponse.message,
        'data': {
          'id': confirmationResponse.data.id,
          'senderId': confirmationResponse.data.senderId,
          'senderName': confirmationResponse.data.senderName,
          'beneficiaryId': confirmationResponse.data.beneficiaryId,
          'beneficiaryName': confirmationResponse.data.beneficiaryName,
          'beneficiaryEmail': confirmationResponse.data.beneficiaryEmail,
          'beneficiaryPhone': confirmationResponse.data.beneficiaryPhone,
          'amount': confirmationResponse.data.billAmount,
          'currency': confirmationResponse.data.originalCurrency,
          'billRefNo': confirmationResponse.data.billRefNo,
          'status': confirmationResponse.data.status,
          'walletFTNumber': confirmationResponse.data.walletFTNumber,
          'createdAt': confirmationResponse.data.createdAt,
          'authorization_type': confirmationResponse.data.authorizationType,
          'paidDate': confirmationResponse.data.paidDate,
          'serviceCharge': responseData['data']['serviceCharge'] ?? 0.0,
          'VAT': responseData['data']['VAT'] ?? 0.0,
          'billAmount': confirmationResponse.data.billAmount,
          'totalAmount': confirmationResponse.data.totalAmount,
          'changedCurrency': confirmationResponse.data.changedCurrency,
          'exchangeRate': confirmationResponse.data.exchangeRate,
          'paidAmount': confirmationResponse.data.paidAmount,
        },
      });
    } catch (e) {
      if (e is DioException) {
        throw ApiException(
          message: e.message ?? 'Failed to submit PIN',
          statusCode: e.response?.statusCode ?? 500,
        );
      }
      if (e is ApiException) rethrow;
      throw ApiException(
        message: 'Unexpected error: $e',
        statusCode: 500,
      );
    }
    */
  }

  @override
  Future<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.resendOtp,
        data: {
          'billRefNo': billRefNo,
          'otpFor': otpFor,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return OtpResendResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
      final token = await _authLocalDataSource.getAuthToken();

      final response = await _dio.post(
        ApiEndpoints.resendOtp,
        data: {
          'billRefNo': billRefNo,
          'otpFor': otpFor,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
          validateStatus: (status) => true,
        ),
      );

      if (response.statusCode == 401) {
        throw const ApiException(
          message: 'Session expired. Please login again.',
          statusCode: 401,
        );
      }

      if (response.statusCode == 400) {
        final errorMessage =
            (response.data as Map<String, dynamic>)['message']?.toString() ??
                'Failed to resend OTP';
        throw ServerException(message: errorMessage, statusCode: 400);
      }

      if (response.data == null) {
        throw const ServerException(
          message: 'Invalid response from server',
          statusCode: 500,
        );
      }

      return OtpResendResponse.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      if (e is DioException) {
        throw ApiException(
          message: e.message ?? 'Failed to resend OTP',
          statusCode: e.response?.statusCode ?? 500,
        );
      }
      if (e is ApiException) rethrow;
      throw ApiException(
        message: 'Unexpected error: $e',
        statusCode: 500,
      );
    }
    */
  }

  @override
  Future<WalletTransferResponse> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.verifyOtp,
        data: {
          'billRefNo': billRefNo,
          'otpFor': 'wallet_transfer',
          'otpCode': otpCode,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return WalletTransferResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
      final token = await _authLocalDataSource.getAuthToken();

      debugPrint(
        'bill ref nofff $billRefNo and otp $otpFor otpcode $otpCode ',
      );

      final response = await _dio.post(
        ApiEndpoints.verifyOtp,
        data: {
          'billRefNo': billRefNo,
          'otpFor': 'wallet_transfer',
          'otpCode': otpCode,
        },
      );

      if (response.statusCode == 401) {
        throw const ApiException(
          message: 'Session expired. Please login again.',
          statusCode: 401,
        );
      }

      return WalletTransferResponse.fromJson(
        response.data as Map<String, dynamic>,
      );
    } catch (e) {
      if (e is DioException) {
        throw ApiException(
          message: e.message ?? 'Failed to verify OTP',
          statusCode: e.response?.statusCode ?? 500,
        );
      }
      rethrow;
    }
    */
  }

  @override
  Future<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String productType,
  }) async {
    // debugPrint('response from remote data source of wallet transfer ');

    try {
      final result = await _apiService.post(
        ApiEndpoints.validateTransferAmount,
        data: {
          'amount': amount,
          'currency': currency,
          'productType': productType,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return CheckTransferRulesResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
    final response = await _safeApiCall(
      () => _dio.post<Map<String, dynamic>>(
        ApiEndpoints.validateTransferAmount,
        data: {
          'amount': amount,
          'currency': currency,
          'productType': productType,
        },
      ),
    );

    debugPrint('response from remote data source of wallet transfer $response');

    if (response.data == null) {
      throw const ApiException(
        message: 'Failed to check transfer rules',
        statusCode: 500,
      );
    }

    try {
      return CheckTransferRulesResponse.fromJson(response.data!);
    } catch (e) {
      throw ApiException(
        message: 'Failed to parse transfer rules response: $e',
        statusCode: 500,
      );
    }
    */
  }

  @override
  Future<String> generateReceipt({
    required String billRefNo,
  }) async {
    debugPrint('Recipt generating bill from remote 😳 $billRefNo');

    try {
      final result = await _apiService.get(
        ApiEndpoints.getInvoiceUrl(billRefNo),
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        final response = success as Map<String, dynamic>;
        final invoiceUrl = response['invoiceURL'] ?? '';

        return invoiceUrl as String;
        // response?['data']['invoiceURL'] ?? '';
        //  return response?['data']['invoiceURL'] ??'';
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
    try {
      final response = await _safeApiCall(
        () => _dio.get<Map<String, dynamic>>(
          ApiEndpoints.getInvoiceUrl(billRefNo),
        ),
      );

      debugPrint('Recipt generating response from remote 😳 $response');

      if (response.data == null) {
        throw const ApiException(
          message: 'Failed to generate receipt',
          statusCode: 500,
        );
      }

      debugPrint(
        'response from remote data source of wallet transfer $response',
      );

      final invoiceUrl = response.data?['invoiceURL'];

      if (invoiceUrl == null) {
        throw const ApiException(
          message: 'Invoice URL is missing',
          statusCode: 500,
        );
      }

      return invoiceUrl as String;
    } catch (e) {
      throw const ApiException(
        message: 'Failed to generate receipt',
        statusCode: 500,
      );
    }

    */
  }

  @override
  Future<Map<String, String>> getUsersAvatar({
    required List<String> userIds,
  }) async {
    // try {
    debugPrint('iiiiiii ${userIds.length}');
    if (userIds.isEmpty) return {};

    try {
      final result = await _apiService.post(
        ApiEndpoints.bulkMemberLookUp,
        data: {'ids': userIds},
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((response) {
        //     final avatarsData = response.data?['data'] as List<dynamic>;

        // debugPrint('avatar ${avatarsData.length} ');
        // debugPrint('avatar response from remote 😳 $response');

        // final avatarMap = <String, String>{};
        // for (final avatar in avatarsData) {
        //   debugPrint('issiisis $avatar');
        //   final av = AvatarModel.fromJson(avatar as Map<String, dynamic>);
        //   avatarMap[av.id] = av.avatar ?? '';
        // }

        // return avatarMap;
        // return TopUpProvidersModel.fromJson(success);
        return {};
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

    //   final response = await _safeApiCall(
    //     () => _dio.post<Map<String, dynamic>>(
    //       ApiEndpoints.bulkMemberLookUp,
    //       data: {'ids': userIds},
    //     ),
    //   );

    //   debugPrint('repsonse from the server $response');

    //   if (response.data == null) {
    //     throw const ApiException(
    //       message: 'No avatars data found',
    //       statusCode: 409,
    //     );
    //   }

    //   debugPrint("repsonse from jhh the server ${response.data?['data']}");

    //   final avatarsData = response.data?['data'] as List<dynamic>;

    //   debugPrint('avatar ${avatarsData.length} ');
    //   debugPrint('avatar response from remote 😳 $response');

    //   final avatarMap = <String, String>{};
    //   for (final avatar in avatarsData) {
    //     debugPrint('issiisis $avatar');
    //     final av = AvatarModel.fromJson(avatar as Map<String, dynamic>);
    //     avatarMap[av.id] = av.avatar ?? '';
    //   }

    //   return avatarMap;
    // } on ApiException catch (err) {
    //   rethrow;
    // } catch (e) {
    //   throw const ApiException(
    //     message: 'Something wrong, please try again later',
    //     statusCode: 500,
    //   );
    // }
  }

  /// ['url', 'url']
  /// // { userI: 'url', userId2: 'url' }
}
