import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class LoanItemInfoCards extends StatelessWidget {
  const LoanItemInfoCards({
    required this.loanItem,
    super.key,
  });

  final LoanItem loanItem;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Row(
        children: _buildInfoCards(),
      ),
    );
  }

  List<Widget> _buildInfoCards() {
    final cards = <Widget>[];

    switch (loanItem.type) {
      case LoanItemType.car:
        cards.addAll(_buildCarInfoCards());
        break;
      case LoanItemType.house:
        cards.addAll(_buildHouseInfoCards());
        break;
      case LoanItemType.general:
        cards.addAll(_buildGeneralInfoCards());
        break;
    }

    // Add spacing between cards
    final spacedCards = <Widget>[];
    for (int i = 0; i < cards.length; i++) {
      spacedCards.add(Expanded(child: cards[i]));
      if (i < cards.length - 1) {
        spacedCards.add(SizedBox(width: 8.w));
      }
    }

    return spacedCards;
  }

  List<Widget> _buildCarInfoCards() {
    return [
      _buildInfoCard(
        title: 'Body Type',
        value: loanItem.category.name.toUpperCase(),
        icon: loanItem.category.icon,
        isNetworkImage: true,
      ),
      _buildInfoCard(
        title: 'Transmission',
        value:
            loanItem.specifications['transmission']?.toString().toUpperCase() ??
                'N/A',
        icon: MediaRes.gearStick,
      ),
      _buildInfoCard(
        title: 'Max Speed',
        value: '${loanItem.specifications['horsePower'] ?? 'N/A'} KM/H',
        icon: MediaRes.speedometer,
      ),
    ];
  }

  List<Widget> _buildHouseInfoCards() {
    return [
      _buildInfoCard(
        title: 'Property Type',
        value: loanItem.category.name.toUpperCase(),
        icon: loanItem.category.icon,
        isNetworkImage: true,
      ),
      _buildInfoCard(
        title: 'Bedrooms',
        value: '${loanItem.specifications['bedrooms'] ?? 'N/A'}',
        icon: MediaRes.bedRoomIcon,
      ),
      _buildInfoCard(
        title: 'Bathrooms',
        value: '${loanItem.specifications['bathrooms'] ?? 'N/A'}',
        icon: MediaRes.bathRoomIcon,
      ),
    ];
  }

  List<Widget> _buildGeneralInfoCards() {
    // Extract the most relevant specifications for general items
    final specs = loanItem.specifications;
    final cards = <Widget>[];

    if (specs.containsKey('category')) {
      cards.add(_buildInfoCard(
        title: 'Category',
        value: specs['category'].toString(),
        icon: Icons.category,
        isIconData: true,
      ));
    }

    if (specs.containsKey('condition')) {
      cards.add(_buildInfoCard(
        title: 'Condition',
        value: specs['condition'].toString(),
        icon: Icons.star,
        isIconData: true,
      ));
    }

    if (specs.containsKey('year')) {
      cards.add(_buildInfoCard(
        title: 'Year',
        value: specs['year'].toString(),
        icon: Icons.calendar_today,
        isIconData: true,
      ));
    }

    // If no specific specs, show generic info
    if (cards.isEmpty) {
      cards.addAll([
        _buildInfoCard(
          title: 'Type',
          value: loanItem.category.name,
          icon: Icons.info,
          isIconData: true,
        ),
        _buildInfoCard(
          title: 'Status',
          value: 'Available',
          icon: Icons.check_circle,
          isIconData: true,
        ),
      ]);
    }

    return cards.take(3).toList(); // Limit to 3 cards
  }

  Widget _buildInfoCard({
    required String title,
    required String value,
    required dynamic icon,
    bool isNetworkImage = false,
    bool isIconData = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 16.h),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: const BorderSide(color: Color(0xFFEFEFEF)),
          borderRadius: BorderRadius.circular(14.r),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: GoogleFonts.outfit(
              fontSize: 12.sp,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 12.h),
          SizedBox(
            width: 40.w,
            height: 40.h,
            child: _buildIconWidget(icon, isNetworkImage, isIconData),
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: GoogleFonts.outfit(
              fontSize: 11.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildIconWidget(dynamic icon, bool isNetworkImage, bool isIconData) {
    if (isIconData && icon is IconData) {
      return Icon(
        icon,
        size: 24.w,
        color: Colors.grey[600],
      );
    } else if (isNetworkImage && icon is String) {
      return CachedNetworkImage(
        imageUrl: icon,
        fit: BoxFit.contain,
        placeholder: (context, url) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
        ),
        errorWidget: (context, url, error) => Icon(
          Icons.image_not_supported,
          size: 24.w,
          color: Colors.grey[400],
        ),
      );
    } else if (icon is String) {
      return Image.asset(
        icon,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) => Icon(
          Icons.image_not_supported,
          size: 24.w,
          color: Colors.grey[400],
        ),
      );
    } else {
      return Icon(
        Icons.help_outline,
        size: 24.w,
        color: Colors.grey[400],
      );
    }
  }
}
