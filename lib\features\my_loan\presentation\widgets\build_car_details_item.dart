import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';

class BuildCarDetailsItem extends StatelessWidget {
  const BuildCarDetailsItem({
    required this.title,
    required this.value,
    required this.showDivider,
    super.key,
  });
  final String title;
  final String value;
  final bool showDivider;

  @override
  Widget build(BuildContext context) {
    return _buildLoanInfoItem(context);
  }

  Widget _buildLoanInfoItem(BuildContext context) {
    return Column(
      children: [
        Container(
          // color: Colors.red,
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
          ),
          margin: EdgeInsets.symmetric(
            vertical: 8.w,
          ),
          // height: 34.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomBuildText(
                text: title,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                caseType: 'all',
              ),
              CustomBuildText(
                text: value,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ],
          ),
        ),
        SizedBox(height: 4,),
        if (showDivider)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: CustomPaint(
              painter: DottedLinePainter(
                dashWidth: 2,
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              size: const Size(double.infinity, 1),
            ),
          ),
        // Padding(
        //   padding: EdgeInsets.symmetric(horizontal: 16.w),
        //   child: Divider(
        //     color: const Color(0xFF2C2B34).withOpacity(0.3),
        //     thickness: 0.2,
        //   ),
        // ),
      ],
    );
  }
}
