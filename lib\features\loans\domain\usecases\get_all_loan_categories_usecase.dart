import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:dartz/dartz.dart';

class GetAllLoanCategories extends UsecaseWithoutParams<List<LoanCategory>> {
  GetAllLoanCategories(this._repository);

  final LoanRepository _repository;

  @override
  ResultFuture<List<LoanCategory>> call() async {
    try {
      final result = await _repository.getLoanCategories();
      return result;
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
