import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_banks_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_banks_state.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_payment_info_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_payment_info_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cbrs/core/services/routes/route_name.dart';

class LoanBankInfoPage extends StatefulWidget {
  final LoanItem loanItem;

  const LoanBankInfoPage({
    super.key,
    required this.loanItem,
  });

  @override
  State<LoanBankInfoPage> createState() => _LoanBankInfoPageState();
}

class _LoanBankInfoPageState extends State<LoanBankInfoPage> {
  String? _selectedUpfrontPayment;
  String? _selectedBankId;
  final _debounceDuration = const Duration(milliseconds: 500);
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    // Fetch banks when page loads - NO CACHE, always fresh data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LoanBanksCubit>().fetchLoanBanks(
            productId: widget.loanItem.id,
            loanType: widget.loanItem.type,
            forceRefresh: true, // Always force refresh - no cache
          );
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LoanBanksCubit, LoanBanksState>(
      listener: (context, state) {
        if (state is LoanBanksLoaded) {
          if (state.banks.isNotEmpty && _selectedBankId == null) {
            _handleBankSelection(state.banks.first.id);
          }
        }

        if (state is LoanBanksError) {
          _handleError(context, state.userFriendlyMessage);
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.grey[50],
          appBar: AppBar(
            title: Text(
              '${widget.loanItem.type == LoanItemType.car ? 'Car' : 'Mortgage'} Loan Application',
            ),
          ),
          body: RefreshIndicator(
            onRefresh: _refreshBanks,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20.h),
                  _buildLoanItemSummary(),
                  SizedBox(height: 16.h),
                  _buildSectionHeader(
                    'Select Bank',
                    'Choose your preferred bank for the ${widget.loanItem.type.name} loan',
                  ),
                  SizedBox(height: 16.h),
                  _buildBankSelection(state),
                  SizedBox(height: 16.h),
                  _buildUpfrontPaymentSection(state),
                  SizedBox(height: 16.h),
                  _buildLoanInformation(),
                  SizedBox(height: 100.h), // Space for bottom navigation
                ],
              ),
            ),
          ),
          bottomNavigationBar: _buildBottomNavigationBar(state),
        );
      },
    );
  }

  Widget _buildLoanItemSummary() {
    final itemPrice = widget.loanItem.price;
    final itemImage = widget.loanItem.galleryImages.isNotEmpty
        ? widget.loanItem.galleryImages[0].url
        : '';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 24,
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: SizedBox(
              width: 128.w,
              height: 107.h,
              child: itemImage.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: itemImage,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Icon(
                          widget.loanItem.type == LoanItemType.car
                              ? Icons.directions_car
                              : Icons.home,
                          size: 40.sp,
                          color: Colors.grey[400],
                        ),
                      ),
                    )
                  : Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(
                        widget.loanItem.type == LoanItemType.car
                            ? Icons.directions_car
                            : Icons.home,
                        size: 40.sp,
                        color: Colors.grey[400],
                      ),
                    ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            flex: 3,
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '\$${NumberFormat('#,###').format(itemPrice)}',
                    style: GoogleFonts.outfit(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                      height: 1.2,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    widget.loanItem.name,
                    style: GoogleFonts.outfit(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8.h),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Text(
                      widget.loanItem.category.name,
                      style: GoogleFonts.outfit(
                        color: Colors.white,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, String subtitle) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            subtitle,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Colors.black.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankSelection(LoanBanksState state) {
    if (state is LoanBanksLoading) {
      return _buildShimmerBanks();
    }

    if (state is LoanBanksError) {
      return _buildErrorWidget(state);
    }

    if (state is! LoanBanksLoaded || state.banks.isEmpty) {
      return _buildEmptyBanksWidget();
    }

    return _buildBankList(state.banks);
  }

  Widget _buildShimmerBanks() {
    return SizedBox(
      height: 132.h,
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          itemCount: 4,
          itemBuilder: (context, index) {
            return Container(
              width: 100.w,
              margin: EdgeInsets.only(right: 8.w),
              child: Column(
                children: [
                  // Bank logo shimmer with border effect
                  Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      border: Border.all(
                        color: Colors.grey[300]!,
                        width: 1.0,
                      ),
                    ),
                    child: Container(
                      width: 56.w,
                      height: 56.h,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  // Bank name shimmer
                  Container(
                    width: 70.w,
                    height: 14.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(7.r),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  // Secondary text shimmer
                  Container(
                    width: 50.w,
                    height: 10.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildErrorWidget(LoanBanksError state) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48.w,
            color: Colors.red.shade400,
          ),
          SizedBox(height: 12.h),
          Text(
            'Failed to load banks',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.red.shade700,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            state.userFriendlyMessage,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              color: Colors.red.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          if (state.canRetry) ...[
            SizedBox(height: 16.h),
            ElevatedButton.icon(
              onPressed: _refreshBanks,
              icon: const Icon(Icons.refresh),
              label: Text(
                'Try Again',
                style: GoogleFonts.outfit(fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyBanksWidget() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.account_balance,
            size: 48.w,
            color: Colors.grey.shade400,
          ),
          SizedBox(height: 12.h),
          Text(
            'No Banks Available',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'No banks are currently offering loans for this item. '
            'Please try again later.',
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBankList(List<LoanBank> banks) {
    final theme = Theme.of(context);

    return SizedBox(
      height: 132.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: banks.length,
        itemBuilder: (context, index) {
          final bank = banks[index];
          final isSelected = _selectedBankId == bank.id;

          return GestureDetector(
            onTap: () => _handleBankSelection(bank.id),
            child: Container(
              width: 100.w,
              margin: EdgeInsets.only(right: 8.w),
              child: Stack(
                alignment: Alignment.topCenter,
                children: [
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.only(top: isSelected ? 4.h : 2.h),
                        child: Container(
                          padding: EdgeInsets.all(4.w),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected
                                ? theme.primaryColor.withValues(alpha: 0.1)
                                : Colors.white,
                            border: Border.all(
                              color: isSelected
                                  ? theme.primaryColor
                                  : Colors.grey[300]!,
                              width: isSelected ? 1.5 : 1.0,
                            ),
                          ),
                          child: CircleAvatar(
                            radius: isSelected ? 32.r : 28.r,
                            backgroundColor: Colors.white,
                            child: Padding(
                              padding: EdgeInsets.all(4.w),
                              child: ClipOval(
                                child: bank.logo != null
                                    ? CachedNetworkImage(
                                        imageUrl: bank.logo!,
                                        width: isSelected ? 56.w : 48.w,
                                        height: isSelected ? 56.h : 48.h,
                                        fit: BoxFit.contain,
                                        placeholder: (context, url) =>
                                            Container(
                                          decoration: const BoxDecoration(
                                            color: Color(0xFFEEEEEE),
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Icon(
                                          Icons.account_balance,
                                          size: isSelected ? 22.sp : 18.sp,
                                          color: Colors.grey.shade400,
                                        ),
                                      )
                                    : Icon(
                                        Icons.account_balance,
                                        size: isSelected ? 22.sp : 18.sp,
                                        color: Colors.grey.shade400,
                                      ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        bank.name,
                        style: GoogleFonts.outfit(
                          fontSize: isSelected ? 14.sp : 12.sp,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                          color: isSelected ? theme.primaryColor : Colors.black,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  if (isSelected)
                    Positioned(
                      top: 0,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: theme.primaryColor,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'Selected',
                          style: GoogleFonts.outfit(
                            fontSize: 10.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _refreshBanks() async {
    await context.read<LoanBanksCubit>().fetchLoanBanks(
          productId: widget.loanItem.id,
          loanType: widget.loanItem.type,
          forceRefresh: true, // Always force refresh - no cache
        );
  }

  void _handleBankSelection(String bankId) {
    setState(() {
      _selectedBankId = bankId;
      _selectedUpfrontPayment = null;
    });

    // Use the cubit to select the bank
    final currentState = context.read<LoanBanksCubit>().state;
    if (currentState is LoanBanksLoaded) {
      LoanBank? selectedBank;
      try {
        selectedBank = currentState.banks.firstWhere(
          (bank) => bank.id == bankId,
        );
      } catch (e) {
        selectedBank =
            currentState.banks.isNotEmpty ? currentState.banks.first : null;
      }

      if (selectedBank != null) {
        context.read<LoanBanksCubit>().selectBank(selectedBank);

        // Auto-select first upfront payment option and trigger payment calculation
        if (selectedBank.loans.isNotEmpty &&
            selectedBank.loans.first.upfrontPaymentOptions.isNotEmpty) {
          final firstUpfrontPayment =
              selectedBank.loans.first.upfrontPaymentOptions.first;

          // Auto-select the first upfront payment option
          setState(() {
            _selectedUpfrontPayment = firstUpfrontPayment.percentage;
          });

          context
              .read<LoanBanksCubit>()
              .selectUpfrontPayment(firstUpfrontPayment);
          context.read<LoanPaymentInfoCubit>().calculatePaymentInfo(
                bank: selectedBank,
                loanProduct: selectedBank.loans.first,
                upfrontPayment: firstUpfrontPayment,
                productId: widget.loanItem.id,
                loanType: widget.loanItem.type,
              );
        }
      }
    }
  }

  void _handleError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: _refreshBanks,
        ),
      ),
    );
  }

  Widget _buildUpfrontPaymentSection(LoanBanksState state) {
    if (state is LoanBanksLoading) {
      return _buildUpfrontPaymentShimmer();
    }

    if (state is! LoanBanksLoaded ||
        state.banks.isEmpty ||
        _selectedBankId == null) {
      return const SizedBox.shrink();
    }

    LoanBank? selectedBank;
    try {
      selectedBank = state.banks.firstWhere(
        (bank) => bank.id == _selectedBankId,
      );
    } catch (e) {
      selectedBank = state.banks.isNotEmpty ? state.banks.first : null;
    }

    if (selectedBank == null) {
      return const SizedBox.shrink();
    }

    if (selectedBank.loans.isEmpty ||
        selectedBank.loans.first.upfrontPaymentOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final upfrontOptions = selectedBank.loans.first.upfrontPaymentOptions;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Loan Packages (Down Payment)',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 16.h),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: upfrontOptions.map((UpfrontPayment option) {
                final isSelected = _selectedUpfrontPayment == option.percentage;
                return Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: _buildUpfrontPaymentOption(option, isSelected),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpfrontPaymentOption(UpfrontPayment option, bool isSelected) {
    return GestureDetector(
      onTap: () => _handleUpfrontPaymentSelection(option.percentage),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 26.w,
          vertical: 8.h,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor
              : const Color(0xffEBF6F2),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : const Color(0xffEBF6F2),
          ),
        ),
        child: Text(
          '${option.percentage}%',
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _handleUpfrontPaymentSelection(String percentage) {
    if (_selectedBankId == null) return;

    setState(() {
      _selectedUpfrontPayment = percentage;
    });

    _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceDuration, () {
      if (!mounted) return;

      // Use the cubit to select upfront payment
      final currentState = context.read<LoanBanksCubit>().state;
      if (currentState is LoanBanksLoaded) {
        LoanBank? selectedBank;
        try {
          selectedBank = currentState.banks.firstWhere(
            (bank) => bank.id == _selectedBankId,
          );
        } catch (e) {
          selectedBank =
              currentState.banks.isNotEmpty ? currentState.banks.first : null;
        }

        if (selectedBank != null && selectedBank.loans.isNotEmpty) {
          UpfrontPayment? upfrontPayment;
          try {
            upfrontPayment =
                selectedBank.loans.first.upfrontPaymentOptions.firstWhere(
              (option) => option.percentage == percentage,
            );
          } catch (e) {
            upfrontPayment =
                selectedBank.loans.first.upfrontPaymentOptions.isNotEmpty
                    ? selectedBank.loans.first.upfrontPaymentOptions.first
                    : null;
          }

          if (upfrontPayment != null) {
            context.read<LoanBanksCubit>().selectUpfrontPayment(upfrontPayment);

            // Trigger payment calculation
            if (selectedBank.loans.isNotEmpty) {
              context.read<LoanPaymentInfoCubit>().calculatePaymentInfo(
                    bank: selectedBank,
                    loanProduct: selectedBank.loans.first,
                    upfrontPayment: upfrontPayment,
                    productId: widget.loanItem.id,
                    loanType: widget.loanItem.type,
                  );
            }
          }
        }
      }
    });
  }

  Widget _buildLoanInformation() {
    return BlocBuilder<LoanPaymentInfoCubit, LoanPaymentInfoState>(
      builder: (context, paymentState) {
        if (paymentState is LoanPaymentInfoLoading) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Text(
                  'Payment Details',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ),
              SizedBox(height: 8.h),
              _buildPaymentInfoShimmer(),
            ],
          );
        }

        if (paymentState is LoanPaymentInfoLoaded) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Text(
                  'Payment Details',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ),
              SizedBox(height: 8.h),
              _buildPaymentInfoSpecifications(paymentState.paymentInfo),
            ],
          );
        }

        if (paymentState is LoanPaymentInfoError) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red.shade700,
                      size: 20.w,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Payment Calculation Error',
                      style: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  paymentState.userFriendlyMessage,
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    color: Colors.red.shade600,
                  ),
                ),
                if (paymentState.canRetry) ...[
                  SizedBox(height: 12.h),
                  TextButton(
                    onPressed: () {
                      context.read<LoanPaymentInfoCubit>().retryCalculation();
                    },
                    child: Text(
                      'Retry',
                      style: GoogleFonts.outfit(
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildPaymentInfoSpecifications(Map<String, dynamic> paymentInfo) {
    final paymentDetails = [
      {
        'label': 'MONTHLY PAYMENT',
        'value': '\$${paymentInfo['monthlyPayment']}'
      },
      {'label': 'TOTAL AMOUNT', 'value': '\$${paymentInfo['totalAmount']}'},
      {'label': 'INTEREST RATE', 'value': '${paymentInfo['interestRate']}%'},
      {'label': 'PROCESSING FEE', 'value': '\$${paymentInfo['processingFee']}'},
    ];

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2C2B34).withValues(alpha: 0.04),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: _buildPaymentSpecificationItems(paymentDetails),
      ),
    );
  }

  List<Widget> _buildPaymentSpecificationItems(
      List<Map<String, String>> details) {
    final items = <Widget>[];

    for (var i = 0; i < details.length; i++) {
      final detail = details[i];
      items.add(
        _buildPaymentSpecItem(
          detail['label']!,
          detail['value']!,
          hasBorder: i < details.length - 1,
        ),
      );
    }

    return items;
  }

  Widget _buildPaymentSpecItem(String label, String value,
      {bool hasBorder = true}) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            Text(
              value,
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        if (hasBorder)
          Divider(
            color: const Color(0xFF2C2B34).withValues(alpha: 0.4),
          ),
        if (hasBorder) SizedBox(height: 10.h),
      ],
    );
  }

  Widget _buildPaymentInfoShimmer() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2C2B34).withValues(alpha: 0.04),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: [
            _buildPaymentShimmerRow(),
            SizedBox(height: 8.h),
            Divider(color: const Color(0xFF2C2B34).withValues(alpha: 0.4)),
            SizedBox(height: 10.h),
            _buildPaymentShimmerRow(),
            SizedBox(height: 8.h),
            Divider(color: const Color(0xFF2C2B34).withValues(alpha: 0.4)),
            SizedBox(height: 10.h),
            _buildPaymentShimmerRow(),
            SizedBox(height: 8.h),
            Divider(color: const Color(0xFF2C2B34).withValues(alpha: 0.4)),
            SizedBox(height: 10.h),
            _buildPaymentShimmerRow(isLast: true),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentShimmerRow({bool isLast = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: 100.w,
          height: 14.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(7.r),
          ),
        ),
        Container(
          width: 80.w,
          height: 14.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(7.r),
          ),
        ),
      ],
    );
  }

  Widget _buildUpfrontPaymentShimmer() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 200.w,
              height: 16.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
          SizedBox(height: 16.h),
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(3, (index) {
                  return Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 26.w,
                        vertical: 8.h,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Container(
                        width: 30.w,
                        height: 16.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoanItemSummaryShimmer() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 24,
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Row(
          children: [
            // Image shimmer
            Container(
              width: 128.w,
              height: 107.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              flex: 3,
              child: Padding(
                padding: EdgeInsets.all(8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Price shimmer
                    Container(
                      width: 120.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    // Title shimmer
                    Container(
                      width: double.infinity,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      width: 150.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    // Category shimmer
                    Container(
                      width: 80.w,
                      height: 24.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar(LoanBanksState state) {
    final theme = Theme.of(context);

    if (state is! LoanBanksLoaded ||
        state.banks.isEmpty ||
        _selectedBankId == null ||
        _selectedUpfrontPayment == null) {
      return const SizedBox.shrink();
    }

    LoanBank? selectedBank;
    try {
      selectedBank = state.banks.firstWhere(
        (bank) => bank.id == _selectedBankId,
      );
    } catch (e) {
      selectedBank = state.banks.isNotEmpty ? state.banks.first : null;
    }

    if (selectedBank == null) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      height: 84.h,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        margin: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 8.h,
        ),
        decoration: BoxDecoration(
          color: theme.primaryColor,
          borderRadius: BorderRadius.circular(32.r),
          boxShadow: [
            BoxShadow(
              color: theme.primaryColor.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 4),
              spreadRadius: 2,
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(36.w),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(36.w),
                child: CircleAvatar(
                  radius: 24.r,
                  backgroundColor: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.all(4.w),
                    child: ClipOval(
                      child: selectedBank.logo != null
                          ? CustomCachedImage(url: selectedBank.logo!)
                          : Icon(
                              Icons.account_balance,
                              color: Colors.grey.shade400,
                              size: 20.w,
                            ),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    selectedBank.name,
                    style: GoogleFonts.outfit(
                      fontWeight: FontWeight.w600,
                      fontSize: 16.sp,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    '$_selectedUpfrontPayment% Upfront Payment',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            CustomButton(
              text: 'Next',
              onPressed: () => _proceedToNextStep(selectedBank!),
              options: CustomButtonOptions(
                width: 100.w,
                height: 64.h,
                color: Colors.white.withValues(alpha: 0.15),
                textStyle: GoogleFonts.outfit(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 14.sp,
                ),
                borderRadius: BorderRadius.circular(28.r),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _proceedToNextStep(LoanBank selectedBank) {
    final routeName = widget.loanItem.type == LoanItemType.car
        ? '${AppRouteName.carLoanRoute}_${AppRouteName.documentForLoan}'
        : widget.loanItem.type == LoanItemType.house
            ? '${AppRouteName.mortgageRoute}_${AppRouteName.documentForLoan}'
            : AppRouteName.documentForLoan;

    context.pushNamed(
      routeName,
      extra: {
        'bank': selectedBank,
        'selectedUpfrontPayment': _selectedUpfrontPayment ?? '0%',
        'loanItem': widget.loanItem,
      },
    );
  }
}
