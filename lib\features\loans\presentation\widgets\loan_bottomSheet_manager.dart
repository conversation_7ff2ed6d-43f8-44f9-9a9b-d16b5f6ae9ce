// class LoanApplicationBottomsheet {}import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_otp_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_state.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter/material.dart';

class LoanApplicationBottomsheet {
  LoanApplicationBottomsheet({
    required this.context,
    required this.transactionType,
    required this.pinController,
    required this.onPinSubmitted,
    required this.onTransactionSuccess,
    required this.onTransactionComplete,
  });
  final BuildContext context;
  final String transactionType;
  final TextEditingController pinController;

  final Function(String) onPinSubmitted;
  final Function(String) onTransactionSuccess;
  final Function() onTransactionComplete;

  void showConfirmScreenBottomSheet({
    required Map<String, dynamic> data,
    required bool requiresOtp,
    required String billRefNo,
    required double billAmount,
    required double totalAmount,
    required String status,
    String originalCurrency = '', //

    String confirmButtonText = 'Confirm Transfer',
  }) {
    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, updateState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.9,
                ),
                child: CustomConfirmTransactionBottomSheet(
                  data: data,
                  transactionType: transactionType,
                  confirmButtonText: confirmButtonText,
                  status: status,
                  billAmount: billAmount,
                  totalAmount: totalAmount,
                  originalCurrency: originalCurrency,
                  onContinue: () {
                    debugPrint('money req uest');

                    if (Navigator.of(context).canPop()) {
                      Navigator.pop(context);
                    }
                    if (requiresOtp) {
                      showConfirmOtpScreenBottomSheet(billRefNo: billRefNo);
                    } else {
                      showConfirmPinScreenBottomSheet(billRefNo: billRefNo);
                    }
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }

  void showConfirmOtpScreenBottomSheet({required String billRefNo}) {
    final transactionBloc = context.read<LoanItemsBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.9,
            ),
            child: BlocProvider<LoanItemsBloc>.value(
              value: transactionBloc,
              child: CustomOtpBottomSheet(
                billRefNo: billRefNo,
                otpFor: transactionType,
                // transactionType: transactionType,
                focusNode: FocusNode(),
                onOtpVerified: (otpCode) {
                  Navigator.pop(context);
                  showConfirmPinScreenBottomSheet(billRefNo: billRefNo);
                },
                onResendSuccess: () {
                  // Optional: actions after OTP is resent
                },
              ),
            ),
          ),
        );
      },
    );
  }

  bool _isCheckingPin = false;

  void showConfirmPinScreenBottomSheet({required String billRefNo}) {
    final transactionBloc = context.read<LoanItemsBloc>();

    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return BlocProvider.value(
              value: transactionBloc,
              child: BlocConsumer<LoanItemsBloc, LoanState>(
                listener: (context, state) {
                  /*
                  if (state is PinErrorState) {
                    debugPrint('hello');
                    CustomToastification(
                      context,
                      message: state.message,
                    );
                    resetPinState(setModalState);
                  } else if (state is ConfirmTransferSuccess) {
                    setModalState(() {});
                    onTransactionSuccess(state.transaction);
                  }
                  */
                },
                builder: (context, state) {
                  return CustomPinScreen(
                    controller: pinController,
                    onChanged: (keys, isKey) {
                      if (_isCheckingPin) return;

                      setModalState(() {
                        if (!isKey) {
                          pinController.text = pinController.text.isNotEmpty
                              ? pinController.text
                                  .substring(0, pinController.text.length - 1)
                              : '';
                          pinController.selection = TextSelection.fromPosition(
                            TextPosition(offset: pinController.text.length),
                          );
                        }
                        pinController.text = "${pinController.text}$keys";
                        pinController.selection = TextSelection.fromPosition(
                          TextPosition(offset: pinController.text.length),
                        );
                      });
                    },
                    onSubmitted: (pin) {
                      if (_isCheckingPin) return;

                      setModalState(() => _isCheckingPin = true);
                      onPinSubmitted(pin);
                    },
                    isLoading: _isCheckingPin,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Future<void> resetPinState(StateSetter setModalState) async {
    setModalState(() {
      _isCheckingPin = false;
      pinController.clear();
    });
  }

  void showSuccessScreenBottomSheet(
    Map<String, dynamic> data, {
    required double totalAmount,
    required double billAmount,
    required String transactionId,
    required String billRefNo,



    required String status,
    String originalCurrency = '', //

    String title = '', //
    bool showActionButtons = true,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) {
        return CustomSuccessTransactionBottomSheet(
          data: data,
          originalCurrency: originalCurrency,
          totalAmount: totalAmount,
          billAmount: billAmount,
          status: status,
          title: title,
          transactionId: transactionId,
          billRefNo: billRefNo,
          transactionType: transactionType,
          onContinue: onTransactionComplete,
          showActionButtons: showActionButtons,
        );
      },
    );
  }
}
