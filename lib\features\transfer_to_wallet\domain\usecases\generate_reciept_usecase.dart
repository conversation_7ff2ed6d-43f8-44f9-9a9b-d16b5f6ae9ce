import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/repositories/wallet_transfer_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

class GenerateRecieptUsecase
    extends UsecaseWithParams<String, GenerateReceiptParams> {
  const GenerateRecieptUsecase(this._repository);
  final WalletTransferRepository _repository;

  @override
  ResultFuture<String> call(GenerateReceiptParams params) async {
      return await _repository.generateReceipt(
        billRefNo: params.billRefNo,
      );
   
  }
}

class GenerateReceiptParams extends Equatable {
  const GenerateReceiptParams({
    required this.billRefNo,
  });
  final String billRefNo;

  @override
  List<Object?> get props => [billRefNo];
}
