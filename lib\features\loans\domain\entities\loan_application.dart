import 'package:equatable/equatable.dart';

/// Unified loan application entity for all loan types
class LoanApplication extends Equatable {
  const LoanApplication({
    required this.id,
    required this.memberId,
    required this.loanDetails,
    required this.paymentDetails,
    required this.productDetails,
    required this.dates,
    required this.type,
  });
  final String id;
  final String memberId;
  final LoanDetails loanDetails;
  final PaymentDetails paymentDetails;
  final ProductDetails productDetails;
  final Dates dates;
  final LoanApplicationType type;

  /// Factory for car loan applications
  factory LoanApplication.car({
    required String id,
    required String memberId,
    required LoanDetails loanDetails,
    required PaymentDetails paymentDetails,
    required ProductDetails productDetails,
    required Dates dates,
  }) {
    return LoanApplication(
      id: id,
      memberId: memberId,
      loanDetails: loanDetails,
      paymentDetails: paymentDetails,
      productDetails: productDetails,
      dates: dates,
      type: LoanApplicationType.car,
    );
  }

  /// Factory for mortgage loan applications
  factory LoanApplication.mortgage({
    required String id,
    required String memberId,
    required LoanDetails loanDetails,
    required PaymentDetails paymentDetails,
    required ProductDetails productDetails,
    required Dates dates,
  }) {
    return LoanApplication(
      id: id,
      memberId: memberId,
      loanDetails: loanDetails,
      paymentDetails: paymentDetails,
      productDetails: productDetails,
      dates: dates,
      type: LoanApplicationType.mortgage,
    );
  }

  /// Check if this is a car loan application
  bool get isCarLoan => type == LoanApplicationType.car;

  /// Check if this is a mortgage loan application
  bool get isMortgageLoan => type == LoanApplicationType.mortgage;

  @override
  List<Object?> get props => [
        id,
        memberId,
        loanDetails,
        paymentDetails,
        productDetails,
        dates,
        type,
      ];
}

/// Enum for loan application types
enum LoanApplicationType {
  car,
  mortgage,
  general,
}

/// Unified loan details for applications
class LoanDetails extends Equatable {
  final String id;
  final String bankId;
  final String loanType;
  final String status;
  final num applicationFee;
  final num facilitationRate;
  final Map<String, dynamic> penaltyStructure;
  final int gracePeriodInDays;
  final int loanPeriod;
  final String amortizationType;

  const LoanDetails({
    required this.id,
    required this.bankId,
    required this.loanType,
    required this.status,
    required this.applicationFee,
    required this.facilitationRate,
    required this.penaltyStructure,
    required this.gracePeriodInDays,
    required this.loanPeriod,
    required this.amortizationType,
  });

  @override
  List<Object?> get props => [
        id,
        bankId,
        loanType,
        status,
        applicationFee,
        facilitationRate,
        penaltyStructure,
        gracePeriodInDays,
        loanPeriod,
        amortizationType,
      ];
}

/// Unified payment details for applications
class PaymentDetails extends Equatable {
  final double upfrontPaymentAmount;
  final String upfrontPaymentPercentage;
  final double upfrontFacilitationFee;
  final double loanAmount;
  final double monthlyPayment;
  final double monthlyFacilitationFee;
  final double totalMonthlyPayment;
  final String totalPayment;
  final String totalInterest;
  final double interestRate;
  final bool isAutoRepay;

  const PaymentDetails({
    required this.upfrontPaymentAmount,
    required this.upfrontPaymentPercentage,
    required this.upfrontFacilitationFee,
    required this.loanAmount,
    required this.monthlyPayment,
    required this.monthlyFacilitationFee,
    required this.totalMonthlyPayment,
    required this.totalPayment,
    required this.totalInterest,
    required this.interestRate,
    required this.isAutoRepay,
  });

  @override
  List<Object?> get props => [
        upfrontPaymentAmount,
        upfrontPaymentPercentage,
        upfrontFacilitationFee,
        loanAmount,
        monthlyPayment,
        monthlyFacilitationFee,
        totalMonthlyPayment,
        totalPayment,
        totalInterest,
        interestRate,
        isAutoRepay,
      ];
}

/// Unified product details for applications
class ProductDetails extends Equatable {
  final String id;
  final String name;
  final String type;
  final String price;
  final List<String> images;
  final Map<String, dynamic> specifications;

  const ProductDetails({
    required this.id,
    required this.name,
    required this.type,
    required this.price,
    required this.images,
    required this.specifications,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        price,
        images,
        specifications,
      ];
}

/// Unified dates for applications
class Dates extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final DateTime nextPaymentDate;

  const Dates({
    required this.startDate,
    required this.endDate,
    required this.nextPaymentDate,
  });

  @override
  List<Object?> get props => [
        startDate,
        endDate,
        nextPaymentDate,
      ];
}
