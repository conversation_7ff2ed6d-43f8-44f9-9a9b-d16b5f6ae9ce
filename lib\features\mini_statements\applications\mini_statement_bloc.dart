// lib/features/mini_statement/presentation/bloc/mini_statement_bloc.dart

import 'package:bloc/bloc.dart';
import 'package:cbrs/features/mini_statements/domain/usecases/generate_mini_statement_usecase.dart';

import 'package:cbrs/features/mini_statements/applications/mini_statement_event.dart';
import 'package:cbrs/features/mini_statements/applications/mini_statement_state.dart';

class MiniStatementBloc extends Bloc<MiniStatementEvent, MiniStatementState> {
  MiniStatementBloc({
    required GenerateMiniStatementUsecase generateMiniStatementUsecase,
  })  : _generateMiniStatementUsecase = generateMiniStatementUsecase,
        super(MiniStatementInitial()) {
    on<GenerateMiniStatementEvent>((event, emit) async {
      emit(MiniStatementLoading());

      final result = await _generateMiniStatementUsecase(
        GenerateMiniStatementParams(
          startDate: event.startDate,
          endDate: event.endDate,
          currency: event.currency,
        ),
      );

      result.fold(
        (failure) => emit(MiniStatementError(failure.message)),
        (miniStatementURL) => emit(MiniStatementLoaded(miniStatementURL)),
      );
    });
  }
  final GenerateMiniStatementUsecase _generateMiniStatementUsecase;
}
