import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

class GetLoanBanks
    extends UsecaseWithParams<List<LoanBank>, GetLoanBanksParams> {
  const GetLoanBanks(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<List<LoanBank>> call(GetLoanBanksParams params) async {
    return repository.getLoanBanks(
      productId: params.productId,
      loanType: params.loanType,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetLoanBanksParams extends Equatable {
  const GetLoanBanksParams({
    required this.productId,
    required this.loanType,
    this.page = 1,
    this.limit = 10,
  });

  final String productId;
  final LoanItemType loanType;
  final int page;
  final int limit;

  @override
  List<Object?> get props => [productId, loanType, page, limit];
}
