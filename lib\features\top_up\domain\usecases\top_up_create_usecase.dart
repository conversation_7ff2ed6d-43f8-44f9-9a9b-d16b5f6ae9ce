import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_confirm_response_entities.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_success_response_entities.dart';
import 'package:cbrs/features/top_up/domain/repositories/top_up_repositories.dart';
import 'package:equatable/equatable.dart';

class TopUpCreateTopUpUsecase
    extends UsecaseWithParams<TopUpSuccessResponseEntities, CreateTopUpParams> {
  const TopUpCreateTopUpUsecase(this._repositores);
  final TopUpRepositories _repositores;

  @override
  ResultFuture<TopUpSuccessResponseEntities> call(CreateTopUpParams params) {
    return _repositores.createTopUp(amount: params.amount, phoneNumber: params.phoneNumber, beneficiaryId: params.beneficiaryId, billReason: params.billReason);
  }
}
class CreateTopUpParams extends Equatable{

CreateTopUpParams({required this.amount, required this.phoneNumber, required this.beneficiaryId, required this.billReason});
    final String amount;
    final String phoneNumber;
    final String billReason;
    final String beneficiaryId;



   

  @override
  // TODO: implement props
  List<Object?> get props => throw UnimplementedError();

}