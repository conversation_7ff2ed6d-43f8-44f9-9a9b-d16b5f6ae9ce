import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/services/connectivity/connectivity_service.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_event.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_state.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_categories.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_item_card.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_item_shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class LoansApplicationPage extends StatefulWidget {
  const LoansApplicationPage({
    required this.loanType,
    super.key,
  });

  final String loanType;

  @override
  State<LoansApplicationPage> createState() => _LoansApplicationPageState();
}

class _LoansApplicationPageState extends State<LoansApplicationPage> {
  String? selectedCategory = '';
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializeLoanItems();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeLoanItems() {
    final loanType = _getLoanItemType();
    context.read<LoanItemsBloc>().add(
          FetchLoanItemsEvent(
            params: GetPaginatedLoanItemsParams(
              loanType: loanType,
              page: 1,
              limit: 10,
            ),
          ),
        );
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreItems();
      }
    });
  }

  void _loadMoreItems() {
    final state = context.read<LoanItemsBloc>().state;
    if (state is LoanLoaded && state.canLoadMore) {
      final loanType = _getLoanItemType();
      context.read<LoanItemsBloc>().add(
            LoadMoreLoanItemsEvent(
              params: GetPaginatedLoanItemsParams(
                loanType: loanType,
                page: state.currentPage + 1,
                limit: 10,
                categoryId:
                    selectedCategory?.isEmpty == true ? null : selectedCategory,
              ),
            ),
          );
    }
  }

  void _filterByCategory(String? categoryId) {
    setState(() {
      selectedCategory = categoryId;
    });

    final loanType = _getLoanItemType();
    context.read<LoanItemsBloc>().add(
          FilterLoanItemsByCategoryEvent(
            categoryId: categoryId ?? '',
            loanType: loanType,
            page: 1,
            limit: 10,
          ),
        );
  }

  void _clearFiltersWithFeedback() {
    // Provide haptic feedback for better UX
    HapticFeedback.lightImpact();

    _filterByCategory('');
  }

  LoanItemType _getLoanItemType() {
    switch (widget.loanType) {
      case 'car_loan':
        return LoanItemType.car;
      case 'mortgage':
        return LoanItemType.house;
      default:
        return LoanItemType.general;
    }
  }

  Widget _buildLoanItemsList(LoanState state) {
    if (state is LoanLoading) {
      return _buildLoadingState();
    } else if (state is LoanLoaded) {
      return _buildLoadedState(state);
    } else if (state is LoanLoadingMore) {
      return _buildLoadingMoreState(state);
    } else if (state is LoanRefreshing) {
      return _buildRefreshingState(state);
    } else if (state is LoanEmpty) {
      return _buildEmptyState(state);
    } else if (state is LoanError) {
      return _buildErrorState(state);
    } else {
      return _buildInitialState();
    }
  }

  Widget _buildLoadingState() {
    return const LoanItemsListShimmer(itemCount: 6);
  }

  Widget _buildRetryingState() {
    // Auto-retry every 3 seconds when offline
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _initializeLoanItems();
      }
    });

    return Column(
      children: [
        // Retrying header
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16.w,
                height: 16.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'No internet connection. Retrying...',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        // Shimmer items
        const Expanded(
          child: LoanItemsListShimmer(itemCount: 6),
        ),
      ],
    );
  }

  Widget _buildLoadedState(LoanLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        final loanType = _getLoanItemType();
        context.read<LoanItemsBloc>().add(
              RefreshLoanItemsEvent(
                params: GetPaginatedLoanItemsParams(
                  loanType: loanType,
                  categoryId: selectedCategory?.isEmpty == true
                      ? null
                      : selectedCategory,
                ),
              ),
            );
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: state.items.length + (state.canLoadMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == state.items.length) {
            // Shimmer loading indicator for pagination
            return const LoanItemsPaginationShimmer();
          }

          final loanItem = state.items[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: LoanItemCard(
              loanItem: loanItem,
              onTap: () {
                _navigateToLoanDetails(loanItem);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingMoreState(LoanLoadingMore state) {
    return RefreshIndicator(
      onRefresh: () async {
        final loanType = _getLoanItemType();
        context.read<LoanItemsBloc>().add(
              RefreshLoanItemsEvent(
                params: GetPaginatedLoanItemsParams(
                  loanType: loanType,
                  categoryId: selectedCategory?.isEmpty == true
                      ? null
                      : selectedCategory,
                ),
              ),
            );
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: state.currentItems.length + 1,
        itemBuilder: (context, index) {
          if (index == state.currentItems.length) {
            return const LoanItemsPaginationShimmer();
          }

          final loanItem = state.currentItems[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: LoanItemCard(
              loanItem: loanItem,
              onTap: () {
                _navigateToLoanDetails(loanItem);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildRefreshingState(LoanRefreshing state) {
    return RefreshIndicator(
      onRefresh: () async {
        // Already refreshing, do nothing
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: state.currentItems.length,
        itemBuilder: (context, index) {
          final loanItem = state.currentItems[index];
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: LoanItemCard(
              loanItem: loanItem,
              onTap: () {
                _navigateToLoanDetails(loanItem);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(LoanEmpty state) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              state.isFiltered ? Icons.search_off : _getLoanIcon(),
              size: 80.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24.h),
            Text(
              state.isFiltered ? 'No Results Found' : 'No Items Available',
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            Text(
              state.message,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (state.isFiltered) ...[
              SizedBox(height: 32.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 32.w),
                child: CustomButton(
                  text: 'Clear All Filters',
                  onPressed: _clearFiltersWithFeedback,
                  options: CustomButtonOptions(
                    height: 56.h,
                    width: double.infinity,
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    textStyle: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    borderRadius: BorderRadius.circular(16.r),
                    elevation: 0,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(LoanError state) {
    // Check connectivity and show shimmer if offline
    return FutureBuilder<bool>(
      future: _checkConnectivity(),
      builder: (context, connectivitySnapshot) {
        if (connectivitySnapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingState();
        }

        final hasConnectivity = connectivitySnapshot.data ?? true;
        if (!hasConnectivity) {
          // Show shimmer with retrying message when offline
          return _buildRetryingState();
        } else {
          // Show error when online but API failed
          return Center(
            child: Padding(
              padding: EdgeInsets.all(32.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 80.w,
                    color: Colors.red[400],
                  ),
                  SizedBox(height: 24.h),
                  Text(
                    'Something went wrong',
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    state.message,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24.h),
                  ElevatedButton(
                    onPressed: _initializeLoanItems,
                    child: const Text('Try Again'),
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }

  Future<bool> _checkConnectivity() async {
    try {
      final connectivityService = ConnectivityService();
      return await connectivityService.hasInternetConnection();
    } catch (e) {
      // If connectivity check fails, assume no connectivity
      return false;
    }
  }

  Widget _buildInitialState() {
    return const LoanItemsListShimmer(itemCount: 4);
  }

  void _navigateToLoanDetails(LoanItem loanItem) {
    context.pushNamed(
      AppRouteName.loanItemDetails,
      extra: {
        'loanItem': loanItem,
        'isGuest': false, // TODO(dev): Get actual guest status
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          _getPageTitle(),
        ),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: CustomPageHeader(
                pageTitle: _getHeaderTitle(),
                description: _getHeaderDescription(),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LoanCategories(
                    selectedCategory: selectedCategory,
                    onCategorySelected: _filterByCategory,
                    loanType: widget.loanType,
                    useApi: true,
                    connectivityService: ConnectivityService(),
                  ),
                  SizedBox(height: 12.h),
                  // Loan Items List
                  Expanded(
                    child: BlocBuilder<LoanItemsBloc, LoanState>(
                      builder: (context, state) {
                        return _buildLoanItemsList(state);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getPageTitle() {
    switch (widget.loanType) {
      case 'car_loan':
        return 'Car Loan Application';
      case 'mortgage':
        return 'Mortgage Application';
      default:
        return 'Loan Application';
    }
  }

  IconData _getLoanIcon() {
    switch (widget.loanType) {
      case 'car_loan':
        return Icons.directions_car;
      case 'mortgage':
        return Icons.home;
      default:
        return Icons.account_balance;
    }
  }

  String _getHeaderTitle() {
    switch (widget.loanType) {
      case 'car_loan':
        return 'Cars List';
      case 'mortgage':
        return 'Mortgage List';
      default:
        return 'Loan Options';
    }
  }

  String _getHeaderDescription() {
    switch (widget.loanType) {
      case 'car_loan':
        return 'Choose your preferred car, review the details, and apply for '
            'your car effortlessly.';
      case 'mortgage':
        return 'Choose your preferred apartment, review the details, and apply '
            'for your dream home effortlessly.';
      default:
        return 'Choose your preferred loan option and apply effortlessly.';
    }
  }
}
