import 'package:cbrs/features/guest/presentation/widget/show_guest_mode_bottom_sheet.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_action_icons.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:cbrs/features/mini_statements/presentations/views/mini_statements_menu_screen.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:go_router/go_router.dart';

class AdditionalServices extends StatelessWidget {
  const AdditionalServices({super.key, this.isGuest = false});
  final bool isGuest;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const HomeSectionHeaders(
            title: 'Addition Services',
            description:
                'Find nearby agent locations and access your transaction statements.',
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              /*
                  // _buildPaymentOption(
                //   context: context,
                //   image: MediaRes.mobileTopUp,
                //   label: 'Mobile\nTop Up',
                //   onTap: () {
                //     context.pushNamed(AppRouteName.topUpMenu);
                //   },
                */

              Expanded(
                child: _buildServiceCard(
                  context: context,
                  title: 'Mobile\nTop-up',
                  icon: MediaRes.mobileTopUp,
                  onTap: () {
                    if (isGuest) {
                      showGuestModeBottomSheet(context);
                    } else {
                      context.pushNamed(AppRouteName.topUpMenu);
                    }
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildServiceCard(
                  context: context,
                  title: 'Agent\nlocator',
                  icon: MediaRes.agentLocator,
                  onTap: () {
                    if (isGuest) {
                      showGuestModeBottomSheet(context);
                    } else {
                      context.pushNamed(AppRouteName.agentLocator);
                    }
                  },
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildServiceCard(
                  context: context,
                  title: 'Mini\nStatement',
                  icon: MediaRes.miniStatement,
                  onTap: () {
                    if (isGuest) {
                      showGuestModeBottomSheet(context);
                    } else {
                      context.pushNamed(AppRouteName.miniStatements);

                    
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard({
    required BuildContext context,
    required String title,
    required String icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        if (isGuest) {
          showGuestModeBottomSheet(context);
        } else {
          onTap();
        }
      },
      child: Container(
        // height: 120.h,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: Colors.white),
          image: const DecorationImage(
            image: AssetImage(MediaRes.homeBirrCardPattern),
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            HomeActionIcons(
              imageIcon: icon,
              bgColor: const Color(0xFFF9F9F9),
              // width: 48,
              // height: 48,
            ),

            // SizedBox(
            //   width: 64.w,
            //   height: 64.h,
            //   child: Center(
            //     child: Image.asset(
            //       icon,
            //       width: 64.w,
            //       height: 64.h,
            //     ),
            //   ),
            // ),
            SizedBox(height: 4.h),
            Text(
              title,
              textAlign: TextAlign.center,
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
