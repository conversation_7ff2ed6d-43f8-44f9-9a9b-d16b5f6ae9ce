import 'dart:async';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_error_retry.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/get_loan_status.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_bloc.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_event.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_state.dart';
import 'package:cbrs/features/my_loan/domain/entity/my_loan.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/_rounded_button.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/custom_listview_builder.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/my_loan_skeleton_card.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/status_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;

class ScreenMyLoan extends StatefulWidget {
  const ScreenMyLoan({super.key});

  @override
  State<ScreenMyLoan> createState() => _ScreenMyLoanState();
}

class _ScreenMyLoanState extends State<ScreenMyLoan> {
  String selectedTab = 'Active';
  final List<String> tabList = ['Active', 'Pending', 'Completed'];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _fetchLoans();

    _scrollController.addListener(() {
      final bloc = context.read<RepaymentBloc>();
      final state = bloc.state;

      if (state is RepaymentLoadedState &&
          state.hasNextPage &&
          _scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent * 0.9) {
        bloc.add(FetchLoansEvent(loanStatus: selectedTab));
      }
    });
  }

  void _fetchLoans() {
    context.read<RepaymentBloc>().add(FetchLoansEvent(loanStatus: selectedTab));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    context.read<RepaymentBloc>().add(FetchLoansEvent(loanStatus: selectedTab));

    // DID change UPdat method
  }

  Future<void> _handleRefresh(String status) async {
    debugPrint('handle refresh');
    // Clear previous state
    context.read<RepaymentBloc>().add(ClearLoansEvent());

    // Request new data
    context.read<RepaymentBloc>().add(FetchLoansEvent(loanStatus: status));

    // Wait for the state to properly update
    // Use a Completer to properly resolve when data is loaded
    // final completer = Completer<void>();

    // // Set up a listener to complete when data is loaded or error occurs
    // final subscription = context.read<RepaymentBloc>().stream.listen((state) {
    //   if (state is RepaymentLoadedState || state is RepaymentErrorState) {
    //     if (!completer.isCompleted) {
    //       completer.complete();
    //     }
    //   }
    // });

    // // Ensure we don't wait forever
    // try {
    //   await completer.future.timeout(const Duration(seconds: 10));
    // } catch (e) {
    //   debugPrint('Refresh timed out: $e');
    // } finally {
    //   subscription.cancel();
    // }


  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: false,
        title: const Text(
          'My Loans',
        ),
        titleSpacing: 22.w,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
            child: CustomRoundedTabs(
              tabList: tabList,
              selectedTab: selectedTab,
              onTap: (selected) {
                if (selected != selectedTab) {
                  setState(() {
                    selectedTab = selected;
                  });

                  _fetchLoans();
                }
              },
            ),
          ),
          Expanded(
            child: returnTabBody(),
          ),
        ],
      ),
    );
  }

  Widget returnTabBody() {
    switch (selectedTab) {
      case 'Active':
        return _buildContainer(context, 'active');
      case 'Pending':
        return _buildContainer(context, 'pending');
      default:
        return _buildContainer(context, 'completed');
    }
  }

  Widget _buildContainer(BuildContext context, String loanType) {
    return BlocBuilder<RepaymentBloc, RepaymentLoanState>(
      builder: (context, state) {
        if (state is RepaymentLoadingState) {
          return Container(
            // padding: const EdgeInsets.all(16),
            child: CustomListviewBuilder(
              shrinkWrap: true,
              scrollDirection: CustomDirection.vertical,
              itemCount: 10,
              itemBuilder: (context, index) {
                return _buildBodySkeletons(context);
              },
            ),
          );
        } else if (state is RepaymentLoadedState) {
          final loanItems = state.loans;

          if (loanItems.isNotEmpty) {
            return RefreshIndicator(
              onRefresh: () => _handleRefresh(loanType),
              child: ListView.builder(
                controller: _scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.only(top: 8.h),
                itemCount: loanItems.length + (state.hasNextPage ? 1 : 0),
                itemBuilder: (context, index) {
                  if (state.hasNextPage && index == loanItems.length) {
                    return _buildBodySkeletons(context);
                  }

                  final item = loanItems[index];
                  return GestureDetector(
                    onTap: () async {
                      final result = await context.pushNamed(
                        AppRouteName.loanDetailPage,
                        extra: {
                          'loanStatus': item.loanDetails?.status,
                          'loanType': item.loanDetails?.loanType,
                          'loanApplication': item,
                          'loanId': item.loanPaymentId,
                        },
                      );
                      if (result == true) {
                        _handleRefresh(selectedTab);
                      }
                    },
                    child: buildLoanCard(context, item),
                  );
                },
              ),
            );
          } else {
            return RefreshIndicator(
              onRefresh: () => _handleRefresh(loanType),
              color: Theme.of(context).primaryColor,
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  Container(
                    height: MediaQuery.of(context).size.height * 0.7,
                    alignment: Alignment.center,
                    child: _buildNoLoans(loanType),
                  ),
                ],
              ),
            );
          }
        } else if (state is RepaymentErrorState) {
          return RefreshIndicator(
            onRefresh: () => _handleRefresh(loanType),
            color: Theme.of(context).primaryColor,
            backgroundColor: Colors.white,
            child: ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                Container(
                  height: MediaQuery.of(context).size.height * 0.7,
                  alignment: Alignment.center,
                  child: CustomErrorRetry(
                    onTap: () {
                      _handleRefresh(loanType);
                      debugPrint('exchange rate retry tapped.');
                    },
                  ),
                ),
              ],
            ),
          );
        } else {
          return const Center(child: Text('No loans available.'));
        }
      },
    );
  }

  // card
  Widget buildLoanCard(BuildContext context, MyLoanRepaymentDataEntity loan) {
    return Container(
      // height: 116.h,
      margin: EdgeInsets.only(bottom: 12.h, left: 16.w, right: 16.w),
      clipBehavior: Clip.antiAlias,
      padding: EdgeInsets.all(8.r),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 24,
          ),
        ],
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: buildLoanImage(
                context,
                '${loan.productDetails?.featureImage}',
              ),
            ),
            SizedBox(
              width: 12.0.w,
            ),
            Expanded(flex: 6, child: buildLoanDesc(context, loan)),
          ],
        ),
      ),
    );
  }

  Widget buildLoanImage(BuildContext context, String assetName) {
    return IntrinsicHeight(
      child: Container(
        // height: 107,
        width: 120.w,
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.84.r),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.84.r),
          child: SizedBox(
            width: 120, // Set explicit width
            height: 100, // Set explicit height
            child: CustomCachedImage(
              url: assetName,
              borderRadius: 12,
              boxFit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }

  Widget buildLoanDesc(BuildContext context, MyLoanRepaymentDataEntity loan) {
    final dateTime = DateTime.parse('2024-01-15');
    final formattedDate = DateFormat('MMM d, y').format(dateTime);

    final status =
     getLoanStatusMessage(loan.loanDetails?.status ?? '');

    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Container(
                child: CustomBuildText(
                  // text: "how are u know about how are u know about my name",
                  text: '${loan.productDetails?.name} ',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
            if (status != 'active') StatusButton(status: status),
          ],
        ),

        Container(
          child: buildText(
            text: (loan.loanDetails?.loanType?[0].toUpperCase() ?? '') +
                (loan.loanDetails?.loanType?.substring(1) ?? ''),
            fontSize: 14.sp,
            color: Colors.black.withOpacity(0.4),
          ),
        ),
        SizedBox(height: 5.h),
        Container(
          child: buildText(
            text: loan.loanDetails?.loanType == 'mortgage'
                ? '\$${loan.productDetails?.totalPrice}'
                : '\$${loan.productDetails?.amount}',
            fontSize: 18.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
        //  const SizedBox(height: 8),

        // const SizedBox(height: 12),
        const Spacer(),

        if (loan.dates?.contractCreatedAt?.isNotEmpty ?? false)
          Container(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                buildText(
                  text: 'Approved on: ',
                  fontSize: 14.sp,
                  color: Colors.black.withOpacity(0.4),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: buildText(
                    text: loan.dates?.contractCreatedAt ?? '',
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildNoLoans(String loanStatus) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            MediaRes.noActiveLoan,
            width: 120.w,
            height: 120.h,
          ),
          SizedBox(
            height: 10.h,
          ),
          CustomBuildText(
            text: loanStatus == 'active'
                ? 'No active Loans'
                : loanStatus == 'pending'
                    ? 'No pended Loans'
                    : 'No completed Loans',
            fontSize: 18.sp,
            fontWeight: FontWeight.w700,
          ),
          SizedBox(
            height: 4.h,
          ),
          CustomBuildText(
            text:
                "You don't have any $loanStatus mortgage loans at the moment. Start a new application to explore your options",
            textAlign: TextAlign.center,
            color: Colors.black.withOpacity(0.4),
            caseType: '',
          ),
        ],
      ),
    );
  }

  Widget buildText({
    required String text,
    Color color = Colors.black,
    FontWeight fontWeight = FontWeight.w400,
    double fontSize = 14,
  }) {
    return CustomBuildText(
      text: text,
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
    );
  }

  ///skeletons
  ///
  Widget _buildBodySkeletons(BuildContext Context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h, left: 16, right: 16),
      padding: EdgeInsets.all(8.r),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 24,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 128.w,
            height: 116.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.sp),
            ),
            child: const MyLoanSkeletonCard(),
          ),
          SizedBox(
            width: 16.w,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: MyLoanSkeletonCard(
                            width: 100.w,
                            height: 16.h,
                            borderRadius: 4.r,
                          ),
                        ),
                        SizedBox(
                          width: 16.w,
                        ),
                        MyLoanSkeletonCard(
                          width: 50.w,
                          height: 12.h,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    MyLoanSkeletonCard(
                      width: 120.w,
                      height: 12.h,
                      borderRadius: 4.r,
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    MyLoanSkeletonCard(
                      width: 100.w,
                      height: 12.h,
                      borderRadius: 4.r,
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: MyLoanSkeletonCard(
                        width: 100.w,
                        height: 14.h,
                        borderRadius: 4.r,
                      ),
                    ),
                    SizedBox(
                      width: 16.w,
                    ),
                    SizedBox(
                      width: 50.w,
                      height: 16.h,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

