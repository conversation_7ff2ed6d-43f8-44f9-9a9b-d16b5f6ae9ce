// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recent_transaction_hive.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RecentTransactionHiveAdapter extends TypeAdapter<RecentTransactionHive> {
  @override
  final int typeId = 0;

  @override
  RecentTransactionHive read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RecentTransactionHive(
      bankId: fields[0] as String,
      recipientName: fields[1] as String,
      accountNumber: fields[2] as String,
      createdAt: fields[3] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, RecentTransactionHive obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.bankId)
      ..writeByte(1)
      ..write(obj.recipientName)
      ..writeByte(2)
      ..write(obj.accountNumber)
      ..writeByte(3)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecentTransactionHiveAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
