import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/mortgage_loan/data/datasources/mortgage_bank_remote_datasource.dart';
import 'package:cbrs/features/mortgage_loan/data/models/application_fee_transaction_model.dart';
import 'package:cbrs/features/mortgage_loan/data/models/loan_payment_info_model.dart';
import 'package:cbrs/features/mortgage_loan/data/models/mortgage_bank_model.dart';
import 'package:cbrs/features/mortgage_loan/data/models/mortgage_loan_application_model.dart';
import 'package:flutter/widgets.dart';

class MortgageBankRemoteDataSourceImpl implements MortgageBankRemoteDataSource {
  MortgageBankRemoteDataSourceImpl(this.apiService, this.authLocalDataSource);
  final ApiService apiService;
  final AuthLocalDataSource authLocalDataSource;

  @override
  Future<List<MortgageBankModel>> getMortgageBanks({
    required String productId,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final result = await apiService.get(
        ApiEndpoints.carLoanBanks,
        parser: (dynamic data) {
          if (data['success'] == true) {
            final banks = data['data']['banks'] as List<dynamic>;
            return banks
                .map(
                  (bank) =>
                      MortgageBankModel.fromJson(bank as Map<String, dynamic>),
                )
                .toList();
          }
          throw Exception('Failed to fetch mortgage banks');
        },
        queryParameters: {
          'page': page,
          'limit': limit,
          'loanType': 'mortgage',
        },
        requiresAuth: false,
      );

      return result.fold(
        (data) {
          debugPrint('data returneed ${data.length}');

          return data;
        },
        (error) =>
            throw Exception('Failed to fetch mortgage banks: ${error.message}'),
      );
    } catch (e) {
      throw Exception('Failed to fetch mortgage banks: $e');
    }
  }

  @override
  Future<LoanPaymentInfoModel> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
  }) async {
    try {
      final result = await apiService.get(
        ApiEndpoints.carLoansPaymentInfo,
        parser: (dynamic data) {
          print('API Response: $data');
          if (data['success'] == true) {
            return LoanPaymentInfoModel.fromJson(data as Map<String, dynamic>);
          }
          throw Exception(
            data['message'] ?? 'Failed to fetch loan payment info',
          );
        },
        queryParameters: {
          'productId': productId,
          'bankId': bankId,
          'upfrontPaymentPercentage': upfrontPaymentPercentage,
          'loanType': 'mortgage',
        },
      );

      return result.fold(
        (data) => data,
        (error) => throw Exception(
          'Failed to fetch loan payment info: ${error.message}',
        ),
      );
    } catch (e) {
      print('Error fetching loan payment info: $e');
      throw Exception('Failed to fetch loan payment info: $e');
    }
  }

  @override
  Future<MortgageLoanApplicationModel> applyMortgageLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    bool isAutoRepay = false,
  }) async {
    try {
      print(
        'Applying for mortgage loan with data: {loanId: $loanId, productId: $productId, upfrontPaymentPercentage: $upfrontPaymentPercentage, isAutoRepay: $isAutoRepay}',
      );

      final result = await apiService.post(
        ApiEndpoints.carLoansApply,
        parser: (dynamic data) {
          print('API Response: $data');
          if (data['success'] == true) {
            return MortgageLoanApplicationModel.fromJson(
              data as Map<String, dynamic>,
            );
          }
          throw Exception(
            data['message'] ?? 'Failed to apply for mortgage loan',
          );
        },
        data: {
          'loanId': loanId,
          'productId': productId,
          'upfrontPaymentPercentage': upfrontPaymentPercentage,
          'isAutoRepay': isAutoRepay,
          'loanType': 'mortgage',
        },
      );

      return result.fold(
        (data) => data,
        (error) => throw ApiException(
          message: error.message,
          statusCode: 400,
        ),
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (e, stackTrace) {
      print('Error applying for mortgage loan: $e');
      print('Stack trace: $stackTrace');
      throw Exception('Failed to apply for mortgage loan: $e');
    }
  }

  @override
  Future<ApplicationFeeTransactionModel> generateApplicationTransaction({
    required String loanApplicationId,
  }) async {
    try {
      final result = await apiService.post(
        ApiEndpoints.generateApplicationTransaction(loanApplicationId),
        parser: (dynamic data) {
          print('API Response: $data');
          if (data['success'] == true) {
            if (data['data'] == null) {
              throw Exception('Invalid response format: missing data field');
            }

            final responseData = data['data'];
            if (responseData is! Map<String, dynamic>) {
              throw Exception('Invalid response format: data is not a map');
            }

            try {
              return ApplicationFeeTransactionModel.fromJson(responseData);
            } catch (e) {
              print('Error parsing response data: $e');
              print('Response data structure: $responseData');
              rethrow;
            }
          }
          throw Exception(
            data['message'] ?? 'Failed to generate application transaction',
          );
        },
      );

      return result.fold(
        (data) => data,
        (error) => throw Exception(
          'Failed to generate application transaction: ${error.message}',
        ),
      );
    } catch (e) {
      print('Error generating application transaction: $e');
      throw Exception('Failed to generate application transaction: $e');
    }
  }

  @override
  Future<String> getLoanTerms({required String bankId}) async {
    try {
      final result = await apiService.get(
        ApiEndpoints.getLoanTerms(bankId),
        parser: (dynamic data) {
          print('🎏🎏🎏🎏 Loan Terms and Conditions Response 🎏🎏🎏');
          print(data);
          if (data['success'] == true) {
            return data['data']['content'] as String;
          }
          throw Exception('Failed to fetch loan terms');
        },
      );

      return result.fold(
        (data) => data,
        (error) =>
            throw Exception('Failed to fetch loan terms: ${error.message}'),
      );
    } catch (e) {
      throw Exception('Failed to fetch loan terms: $e');
    }
  }
}
