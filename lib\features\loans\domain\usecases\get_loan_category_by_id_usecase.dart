import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

class GetLoanCategoryById
    extends UsecaseWithParams<LoanCategory, GetLoanCategoryByIdParams> {
  GetLoanCategoryById(this._repository);

  final LoanRepository _repository;

  @override
  ResultFuture<LoanCategory> call(GetLoanCategoryByIdParams params) async {
    try {
      final result = await _repository.getLoanCategoryById(
        categoryId: params.categoryId,
      );
      return result;
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

class GetLoanCategoryByIdParams extends Equatable {
  const GetLoanCategoryByIdParams({
    required this.categoryId,
  });

  final String categoryId;

  @override
  List<Object> get props => [categoryId];
}
