// import 'package:cbrs/core/error/exceptions.dart';
// import 'package:cbrs/features/utility/data/models/utility_transaction_model.dart';
// import 'package:cbrs/features/utility/presentation/widgets/utility_transaction_modal.dart';
// import 'package:dartz/dartz.dart';
// import 'package:cbrs/core/error/failures.dart';
// import 'package:cbrs/features/utility/domain/entities/payment_request.dart';
// import 'package:cbrs/features/utility/domain/repositories/utility_payment_repository.dart';
// import 'package:cbrs/features/utility/data/datasources/utility_payment_data_source.dart';

// class UtilityPaymentRepositoryImpl implements UtilityPaymentRepository {
//   final UtilityPaymentDataSource paymentService;

//   UtilityPaymentRepositoryImpl({
//     required this.paymentService,
//   });

//   @override
//   Future<Either<Failure,
// UtilityTransactionModel>
//    > processPayment(
//   //    UtilityPaymentRequest 
//       dynamic request) async {
//     try {
//       final response = await paymentService.processUtilityPayment(request);
//       return Right(response);
//     } on ServerException catch (e) {
//       return Left(ServerFailure(e.message));
//     } catch (e) {
//       return Left(ServerFailure(e.toString()));
//     }
//   }
// }
