import 'package:bloc/bloc.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/send_money/domain/entities/recent_transaction_hive.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

class BankTransferRecentTransactionBloc
    extends Bloc<BankTransferEvent, BankTransferState> {
  BankTransferRecentTransactionBloc() : super(const BankTransferInitial()) {
    on<GetRecentTransactionsEvent>(_getRecentTransactions);
    on<SaveRecentTransactionsEvent>(_saveRecentTransactions);

    //
  }

  Future<void> _saveRecentTransactions(
    SaveRecentTransactionsEvent event,
    Emitter<BankTransferState> emit,
  ) async {
    /// 1. reading from hive
    /// 2 check if recipent with the same bank found
    /// 3 if it is found removing from local and replace with the one
    /// 4. new transaction must be on the top. if it is not it should be sorted.
    final box = Hive.box<RecentTransactionHive>('recentTransactionsBox');

    // Remove old if exists
    final existingKey = box.keys.firstWhere(
      (key) => box.get(key)!.uniqueKey == event.transaction.uniqueKey,
      orElse: () => null,
    );

    if (existingKey != null) {
      await box.delete(existingKey);
    }

    final newTransaction = RecentTransactionHive(
      bankId: event.transaction.bankId,
      recipientName: event.transaction.recipientName,
      accountNumber: event.transaction.accountNumber,
      createdAt: DateTime.now(),
    );

    debugPrint('saving the transactions $newTransaction');

    await box.add(newTransaction);
  }

  Future<void> _getRecentTransactions(
    GetRecentTransactionsEvent event,
    Emitter<BankTransferState> emit,
  ) async {
    emit(const LoadingRecentTransactions());
    final box = Hive.box<RecentTransactionHive>('recentTransactionsBox');

    final filteredTransactions = box.values
        .where((tx) => tx.bankId == event.bankId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    debugPrint(
      'transction fecttted length ${filteredTransactions.length} for orfgcod ${event.bankId} ',
    );
    emit(LoadedRecentTransactions(filteredTransactions));

    // return filteredTransactions;
  }
}
