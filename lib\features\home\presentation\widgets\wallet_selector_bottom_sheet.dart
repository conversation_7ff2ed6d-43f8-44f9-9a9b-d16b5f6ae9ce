import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_drop_balance.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:async';

class WalletSelector {
  static OverlayEntry? _overlayEntry;

  static void show(
    BuildContext context, {
    required String selectedWallet,
    required Function(String) onWalletSelected,
    String? phoneNumber,
    String? registrationDate,
  }) {
    // Get current balances from callback

    // Close any existing overlay first
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }

    final formattedPhone = phoneNumber ?? '+251 9 **** 5919';
    final formattedDate = registrationDate ?? 'March 03,2024';

    // Create the controller for animations
    final controller = _WalletSelectorController();
    final walletBalanceBloc = context.read<WalletBalanceBloc>();

    _overlayEntry = OverlayEntry(
      builder: (context) => BlocProvider.value(
        value: walletBalanceBloc,
        child: _WalletSelectorOverlay(
          controller: controller,
          selectedWallet: selectedWallet,
          onWalletSelected: onWalletSelected,
          phoneNumber: formattedPhone,
          registrationDate: formattedDate,
          onDismiss: () {
            if (_overlayEntry != null) {
              _overlayEntry!.remove();
              _overlayEntry = null;
            }
          },
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);

    // Start the entrance animation
    controller.animateIn();
  }

  /// Builds a wallet selector button
  static Widget buildWalletSelectorButton({
    required String selectedWallet,
    required VoidCallback onTap,
  }) {
    return CustomDropBalance(
      selectedWallet: selectedWallet,
      onTap: onTap,
    );
  }
}

/// Controller for the wallet selector animations and drag behavior
class _WalletSelectorController extends ChangeNotifier {
  // Animation values
  double _offset = -1000;

  // Animation timing
  final Duration _animInDuration = const Duration(milliseconds: 400);
  final Duration _animOutDuration = const Duration(milliseconds: 400);
  final Curve _animInCurve = Curves.easeOutCubic;
  final Curve _animOutCurve = Curves.easeInCubic;

  // Animation controllers
  Timer? _animationTimer;

  double get offset => _offset;

  // Animate the entrance
  void animateIn() {
    final startTime = DateTime.now().millisecondsSinceEpoch;
    final startOffset = _offset;
    const endOffset = 0.0;

    // Cancel any existing animation
    _animationTimer?.cancel();

    // Create a repeating timer for smooth animation
    _animationTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final elapsed = currentTime - startTime;

      if (elapsed >= _animInDuration.inMilliseconds) {
        // Animation complete
        _offset = endOffset;
        timer.cancel();
        _animationTimer = null;
        notifyListeners();
        return;
      }

      // Calculate progress with easing
      final progress = elapsed / _animInDuration.inMilliseconds;
      final easedProgress = _animInCurve.transform(progress);

      // Update position
      _offset = startOffset + (endOffset - startOffset) * easedProgress;
      notifyListeners();
    });
  }

  // Animate out and dismiss
  void dismiss(VoidCallback onDismiss) {
    _animateToPosition(
      -1000, //
      _animOutDuration,
      _animOutCurve,
      onComplete: onDismiss,
    );
  }

  // Helper to animate to a specific position
  void _animateToPosition(
    double targetOffset,
    Duration duration,
    Curve curve, {
    VoidCallback? onComplete,
  }) {
    // Cancel any existing animation
    _animationTimer?.cancel();

    final startTime = DateTime.now().millisecondsSinceEpoch;
    final startOffset = _offset;

    // Create a repeating timer for smooth animation
    _animationTimer = Timer.periodic(const Duration(milliseconds: 30), (timer) {
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final elapsed = currentTime - startTime;

      if (elapsed >= duration.inMilliseconds) {
        // Animation complete
        _offset = targetOffset;
        timer.cancel();
        _animationTimer = null;
        notifyListeners();
        onComplete?.call();
        return;
      }

      // Calculate progress with easing
      final progress = elapsed / duration.inMilliseconds;
      final easedProgress = curve.transform(progress);

      // Update position
      _offset = startOffset + (targetOffset - startOffset) * easedProgress;
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _animationTimer?.cancel();
    super.dispose();
  }
}

/// Overlay widget for the wallet selector
class _WalletSelectorOverlay extends StatefulWidget {
  const _WalletSelectorOverlay({
    required this.controller,
    required this.selectedWallet,
    required this.onWalletSelected,
    required this.phoneNumber,
    required this.registrationDate,
    required this.onDismiss,
  });
  final _WalletSelectorController controller;
  final String selectedWallet;
  final Function(String) onWalletSelected;

  final String phoneNumber;
  final String registrationDate;
  final VoidCallback onDismiss;

  @override
  State<_WalletSelectorOverlay> createState() => _WalletSelectorOverlayState();
}

class _WalletSelectorOverlayState extends State<_WalletSelectorOverlay> {
  // Track visibility of wallet balances
  late bool _isEtbBalanceVisible;
  late bool _isUsdBalanceVisible;

  @override
  void initState() {
    super.initState();
    // Initialize balance visibility to false - start with balances hidden
    _isEtbBalanceVisible = false;
    _isUsdBalanceVisible = false;

    // Refresh balances immediately when sheet opens
  }

  void _toggleEtbBalanceVisibility() {
    setState(() {
      _isEtbBalanceVisible = !_isEtbBalanceVisible;
    });
  }

  void _toggleUsdBalanceVisibility() {
    setState(() {
      _isUsdBalanceVisible = !_isUsdBalanceVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, _) {
        // Calculate the background opacity based on the offset
        final backgroundOpacity =
            (1 - (widget.controller.offset / -1000)).clamp(0.0, 1.0);

        return Material(
          type: MaterialType.transparency,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Semi-transparent background with fade-in effect
              Positioned.fill(
                child: Opacity(
                  opacity: backgroundOpacity * 0.4,
                  child: GestureDetector(
                    onTap: () => widget.controller.dismiss(widget.onDismiss),
                    child: Container(
                      color: Colors.black,
                    ),
                  ),
                ),
              ),

              Positioned(
                top: widget.controller.offset,
                left: 0,
                right: 0,
                child: Container(
                  margin: EdgeInsets.zero,
                  padding: EdgeInsets.zero,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(32.r),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: SafeArea(
                    bottom: false,
                    child: SizedBox(
                      height: Platform.isIOS
                          ? MediaQuery.sizeOf(context).height * 0.85
                          : MediaQuery.sizeOf(context).height * 0.95,
                      child: BlocBuilder<WalletBalanceBloc, HomeState>(
                        builder: (context, state) {
                          if (state is WalletLoadedState) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Title and description
                                Padding(
                                  padding: EdgeInsets.fromLTRB(
                                    16.w,
                                    Platform.isIOS ? 0 : 16.h,
                                    24.w,
                                    16.h,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Select Wallet',
                                        style: GoogleFonts.outfit(
                                          fontWeight: FontWeight.w700,
                                          fontSize: 18.sp,
                                          color: Colors.black,
                                        ),
                                      ),
                                      SizedBox(height: 2.h),
                                      Text(
                                        'Choose from your ETB or USD wallet to make transfers and payments allowed for each wallet.',
                                        style: GoogleFonts.outfit(
                                          fontSize: 14.sp,
                                          color: Colors.black.withOpacity(0.4),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // SizedBox(height: 16.h),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  child: Column(
                                    children: [
                                      _buildWalletCard(
                                        context: context,
                                        countryCode: 'US',
                                        walletName: 'USD Wallet',
                                        walletType: 'USD',
                                        balance: state.usdBalance,
                                        phoneNumber: widget.phoneNumber,
                                        registrationDate:
                                            widget.registrationDate,
                                        isSelected:
                                            widget.selectedWallet == 'USD',
                                        isBalanceVisible: _isUsdBalanceVisible,
                                        onToggleBalanceVisibility:
                                            _toggleUsdBalanceVisibility,
                                        onTap: () {
                                          widget.controller.dismiss(() {
                                            widget.onWalletSelected('USD');
                                            debugPrint('Usd selected');
                                            widget.onDismiss();
                                          });
                                        },
                                      ),
                                      SizedBox(height: 20.h),
                                      _buildWalletCard(
                                        context: context,
                                        countryCode: 'ET',
                                        walletName: 'ETB Wallet',
                                        walletType: 'ETB',
                                        balance: state.etbBalance,
                                        phoneNumber: widget.phoneNumber,
                                        registrationDate:
                                            widget.registrationDate,
                                        isSelected:
                                            widget.selectedWallet == 'ETB',
                                        isBalanceVisible: _isEtbBalanceVisible,
                                        onToggleBalanceVisibility:
                                            _toggleEtbBalanceVisibility,
                                        onTap: () {
                                          // First dismiss the sheet, then select the wallet
                                          widget.controller.dismiss(() {
                                            // Wait a short time before changing wallet to avoid UI glitches

                                            widget.onWalletSelected('ETB');
                                            widget.onDismiss();
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 320.h),

                                Center(
                                  child: Container(
                                    padding:
                                        const EdgeInsets.fromLTRB(10, 8, 17, 8),
                                    decoration: BoxDecoration(
                                      color: Colors.black,
                                      borderRadius: BorderRadius.circular(32),
                                    ),
                                    child: InkWell(
                                      onTap: () => widget.controller
                                          .dismiss(widget.onDismiss),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.close,
                                            size: 20.r,
                                            color: Colors.white,
                                          ),
                                          SizedBox(width: 2.w),
                                          Text(
                                            'Close',
                                            style: GoogleFonts.outfit(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                const SizedBox(
                                  height: 20,
                                ),

                                // Close button
                              ],
                            );
                          } else
                            return const SizedBox.shrink();
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Builds a wallet card
Widget _buildWalletCard({
  required BuildContext context,
  required String countryCode,
  required String walletName,
  required String walletType,
  required double? balance,
  required String phoneNumber,
  required String registrationDate,
  required bool isSelected,
  required bool isBalanceVisible,
  required VoidCallback onToggleBalanceVisibility,
  required VoidCallback onTap,
  VoidCallback? onRefresh,
}) {
  final primaryColor =
      walletType == 'USD' ? const Color(0xFF0D3C89) : const Color(0xFF085905);
  final balancePrefix = walletType == 'USD' ? r'$' : '';

  // Check if balance is null before formatting
  final balanceStr = balance?.toStringAsFixed(2) ?? '--';
  final parts = balanceStr.split('.');
  final wholePart = parts[0];
  final decimalPart = parts.length > 1 ? parts[1] : '00';

  // Add commas only if we have a valid number
  final formattedWhole = balance != null ? _addCommas(wholePart) : '--';

  final backgroundImage = walletType == 'USD'
      ? const AssetImage(MediaRes.usdBackground)
      : const AssetImage(MediaRes.birrBackground);

  return InkWell(
    onTap: onTap,
    child: Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: backgroundImage,
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(
            Colors.black.withOpacity(0.1),
            BlendMode.darken,
          ),
        ),
        color: primaryColor,
        borderRadius: BorderRadius.circular(24.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Wallet Balance',
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.24),
                  borderRadius: BorderRadius.circular(60.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12.r),
                      child: CountryFlag.fromCountryCode(
                        countryCode,
                        height: 19.h,
                        width: 19.w,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      walletName,
                      style: GoogleFonts.outfit(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Row(
            children: [
              if (isBalanceVisible)
                Row(
                  children: [
                    CustomBuildText(
                      text: balancePrefix,
                      style: GoogleFonts.outfit(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    CustomBuildText(
                      text: formattedWhole,
                      style: GoogleFonts.outfit(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                    CustomBuildText(
                      text: '.$decimalPart',
                      style: GoogleFonts.outfit(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    CustomBuildText(
                      text: walletType == 'ETB' ? ' ETB' : '',
                      style: GoogleFonts.outfit(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ],
                )
              else
                Padding(
                  padding: const EdgeInsets.only(bottom: 6),
                  child: Text(
                    '************',
                    style: GoogleFonts.outfit(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
              SizedBox(
                width: 8.w,
              ),
              InkWell(
                onTap: onToggleBalanceVisibility,
                borderRadius: BorderRadius.circular(20.r),
                child: Padding(
                  padding: EdgeInsets.all(4.r),
                  child: Image.asset(
                    isBalanceVisible ? MediaRes.eyeOpen : MediaRes.eyeClose,
                    color: Colors.white,
                    width: 20.h,
                    height: 20.h,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 32.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Phone Number',
                    style: GoogleFonts.outfit(
                      fontSize: 12.sp,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    phoneNumber,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    'Registered On:',
                    style: GoogleFonts.outfit(
                      fontSize: 10.sp,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: 4.h),
                  Text(
                    registrationDate,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ),
  );
}

/// Add commas to format number with thousands separators
String _addCommas(String value) {
  final buffer = StringBuffer();
  final chars = value.split('').reversed.toList();

  for (var i = 0; i < chars.length; i++) {
    if (i > 0 && i % 3 == 0) {
      buffer.write(',');
    }
    buffer.write(chars[i]);
  }

  return buffer.toString().split('').reversed.join();
}
