// lib/features/mini_statement/presentation/bloc/mini_statement_event.dart

import 'package:equatable/equatable.dart';

abstract class MiniStatementEvent extends Equatable {
  const MiniStatementEvent();

  @override
  List<Object?> get props => [];
}

class GenerateMiniStatementEvent extends MiniStatementEvent {
  final String startDate;
  final String endDate;
  final String currency;

  const GenerateMiniStatementEvent({
    required this.startDate,
    required this.endDate,
    required this.currency,
  });

  @override
  List<Object?> get props => [startDate, endDate, currency];
}
