import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/transfer_to_wallet/presentation/widgets/my_contact_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:phone_form_field/phone_form_field.dart';

class ContactSelector extends StatelessWidget {
  final PhoneController phoneController;
  final void Function(MemberLookupEntity, String, String, String, bool)
      onContactSelected;

  const ContactSelector({
    super.key,
    required this.phoneController,
    required this.onContactSelected,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
          showModalBottomSheet<void>(
            context: context,
            isScrollControlled: true,
            builder: (context) => ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height,
              ),
              child: MyContactListScreen(
                onTap: (SyncedContactData contactData) {
                  phoneController.value =
                      PhoneNumber.parse(contactData.phoneNumber);

                  final memberInfo = MemberLookupEntity(
                    id: contactData.id,
                    avatar: contactData.avatar,
                    lastName: contactData.lastName,
                    firstName: contactData.firstName,
                    middleName: contactData.middleName,
                    phoneNumber: contactData.phoneNumber,
                    emailAddress: contactData.email,
                  );

                  onContactSelected(
                    memberInfo,
                    '${contactData.firstName} ${contactData.lastName}',
                    contactData.phoneNumber,
                    contactData.avatar,
                    true,
                  );
                },
              ),
            ),
          );
        },
        child: Container(
          margin: EdgeInsets.only(right: 8.w),
          padding: EdgeInsets.all(10.h),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Image.asset(
              'assets/vectors/contact_field_image.png',
              color: Theme.of(context).primaryColor,
              width: 36,
              height: 36,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }
}
