import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class IdentityVerificationCard extends StatelessWidget {
  const IdentityVerificationCard({
    super.key,
    this.isUSD = true,
  });
  final bool isUSD;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20),
      child: BlocBuilder<WalletBalanceBloc, HomeState>(
        builder: (context, state) {
          final isUsd = state is WalletLoadedState
              ? state.isUsdWallet
                  ? true
                  : false
              : true;
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            decoration: BoxDecoration(
              // color: Colors.green,
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(color: Colors.white),
              image:  DecorationImage(
                image:
                    //           static const identityPreviewMobileUsd = '$_baseImages/USD_BG.png';
                    // static const identityPreviewMobileEtb = '$_baseImages/ETB_BG.png';
                    AssetImage(isUsd ? MediaRes.identityPreviewMobileUsd : MediaRes.identityPreviewMobileEtb),
                fit: BoxFit.cover,
              ),

              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 24,
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Please verify your account',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      // Description text
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'To ensure the security of your account and protect against fraud, we require you to complete our identity verification process.',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[700],
                                fontWeight: FontWeight.w500,
                              ),
                              softWrap: true,
                              maxLines: 3,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12.h),
                      // Just the button - icon is positioned separately
                      SizedBox(
                        height: 50.h,
                        width: 130.w,
                        child: ElevatedButton(
                          onPressed: () {
                            context
                                .pushNamed(AppRouteName.identityVerification);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colors.white,
                            elevation: 0, // No shadow
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(40.r),
                            ),
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                          ),
                          child: Text(
                            'Get Verified',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Positioned icon that doesn't affect card height
                Image.asset(
                  MediaRes.verifyAccountIcon,
                  width: 110.h,
                  height: 110.h,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
