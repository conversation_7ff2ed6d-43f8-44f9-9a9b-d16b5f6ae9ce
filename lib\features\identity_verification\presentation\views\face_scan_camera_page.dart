import 'dart:io';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/identity_verification/presentation/widgets/identity_verification_status_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path_provider/path_provider.dart';
import 'package:cross_file/cross_file.dart';

class FaceScanCameraPage extends StatefulWidget {
  const FaceScanCameraPage({super.key});

  @override
  State<FaceScanCameraPage> createState() => _FaceScanCameraPageState();
}

class _FaceScanCameraPageState extends State<FaceScanCameraPage> {
  bool _isCapturing = false;
  bool _showPreview = false;
  File? _capturedImage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _showPreview ? Colors.white : Colors.black,
      body: SafeArea(
        child: _showPreview ? _buildPreviewView() : _buildCameraView(),
      ),
    );
  }

  Widget _buildCameraView() {
    return Stack(
      children: [
        // Full-screen camera preview (3:4 aspect ratio)
        Positioned.fill(
          child: Center(
            child: AspectRatio(
              aspectRatio: 3 / 4,
              child: CameraAwesomeBuilder.awesome(
                saveConfig: SaveConfig.photo(
                  pathBuilder: (sensors) async {
                    try {
                      final Directory extDir = await getTemporaryDirectory();
                      final String dirPath = '${extDir.path}/camerawesome';

                      // Ensure directory exists
                      final dir = Directory(dirPath);
                      if (!await dir.exists()) {
                        await dir.create(recursive: true);
                      }

                      final String filePath =
                          '$dirPath/${DateTime.now().millisecondsSinceEpoch}.jpg';
                      return SingleCaptureRequest(filePath, sensors.first);
                    } catch (e) {
                      debugPrint('Error creating file path: $e');
                      // Fallback to app documents directory
                      final appDir = await getApplicationDocumentsDirectory();
                      final String filePath =
                          '${appDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                      return SingleCaptureRequest(filePath, sensors.first);
                    }
                  },
                  mirrorFrontCamera: false,
                ),
                sensorConfig: SensorConfig.single(
                  sensor: Sensor.position(SensorPosition.front),
                  flashMode: FlashMode.none,
                  aspectRatio: CameraAspectRatios.ratio_4_3,
                ),
                enablePhysicalButton: false,
                defaultFilter: AwesomeFilter.None,
                availableFilters: const [],
                onMediaCaptureEvent: (mediaCapture) {
                  // Set capturing state
                  if (mounted) {
                    setState(() => _isCapturing = true);
                  }

                  try {
                    mediaCapture.captureRequest.when(
                      single: (single) async {
                        try {
                          final file = await single.file;
                          if (file != null && mounted) {
                            final capturedFile = File(file.path);
                            // Verify file exists before setting state
                            if (await capturedFile.exists()) {
                              setState(() {
                                _capturedImage = capturedFile;
                                _showPreview = true;
                                _isCapturing = false;
                              });
                            } else {
                              debugPrint(
                                  'Captured file does not exist: ${file.path}');
                              if (mounted) {
                                setState(() => _isCapturing = false);
                              }
                            }
                          } else if (mounted) {
                            setState(() => _isCapturing = false);
                          }
                        } catch (e) {
                          debugPrint('Error processing single capture: $e');
                          if (mounted) {
                            setState(() => _isCapturing = false);
                          }
                        }
                      },
                      multiple: (multiple) async {
                        try {
                          final file = await multiple.fileBySensor.values.first;
                          if (file != null && mounted) {
                            final capturedFile = File(file.path);
                            // Verify file exists before setting state
                            if (await capturedFile.exists()) {
                              setState(() {
                                _capturedImage = capturedFile;
                                _showPreview = true;
                                _isCapturing = false;
                              });
                            } else {
                              debugPrint(
                                  'Captured file does not exist: ${file.path}');
                              if (mounted) {
                                setState(() => _isCapturing = false);
                              }
                            }
                          } else if (mounted) {
                            setState(() => _isCapturing = false);
                          }
                        } catch (e) {
                          debugPrint('Error processing multiple capture: $e');
                          if (mounted) {
                            setState(() => _isCapturing = false);
                          }
                        }
                      },
                    );
                  } catch (e) {
                    debugPrint('Error handling captured photo: $e');
                    if (mounted) {
                      setState(() => _isCapturing = false);
                    }
                  }
                },
              ),
            ),
          ),
        ),

        // Status indicator overlay
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: ColoredBox(
            color: Colors.black.withValues(alpha: 0.3),
            child: const SafeArea(
              child: IdentityVerificationStatusIndicator(
                currentStep: IdentityVerificationStep.faceCapture,
              ),
            ),
          ),
        ),

        // Title text and instructions
        Positioned(
          top: 50.h,
          left: 24.w,
          right: 24.w,
          child: Column(
            children: [
              Text(
                'Take a Selfie',
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                      color: Colors.black.withValues(alpha: 0.8),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'Position your face within the outline',
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black.withValues(alpha: 0.8),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Face outline guide
        Center(
          child: Container(
            width: 220.w,
            height: 280.h,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white.withOpacity(0.7),
                width: 2.0,
              ),
              borderRadius: BorderRadius.circular(150.r),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewView() {
    return Column(
      children: [
        // Status indicator
        IdentityVerificationStatusIndicator(
          currentStep: IdentityVerificationStep.faceReview,
          onBackPressed: _retakePhoto,
        ),

        SizedBox(height: 20.h),

        // Captured image display
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 32.w),
          child: Container(
            width: double.infinity,
            height: 320.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16.r),
              child: _capturedImage != null
                  ? Image.file(
                      _capturedImage!,
                      fit: BoxFit.cover,
                    )
                  : ColoredBox(
                      color: Colors.grey[200]!,
                      child: Icon(
                        Icons.person,
                        size: 60.sp,
                        color: Colors.grey[400],
                      ),
                    ),
            ),
          ),
        ),

        SizedBox(height: 24.h),

        // Description text
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Text(
            'If you are satisfied with the photo, you may proceed to the next '
            'step. Alternatively, you may retake the photo if you wish to '
            'improve its clarity or alignment.',
            textAlign: TextAlign.left,
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ),

        const Spacer(),

        // Action buttons
        Padding(
          padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 30.h),
          child: Column(
            children: [
              CustomRoundedBtn(
                btnText: 'Confirm',
                onTap: _confirmPhoto,
                isLoading: false,
              ),
              SizedBox(height: 12.h),
              CustomRoundedBtn(
                btnText: 'Retake',
                onTap: _retakePhoto,
                isLoading: false,
                bgColor: Colors.white,
                textColor: Theme.of(context).primaryColor,
                borderSide: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _retakePhoto() {
    setState(() {
      _showPreview = false;
      _capturedImage = null;
    });
  }

  void _confirmPhoto() {
    if (_capturedImage != null) {
      context.pushNamed(
        AppRouteName.documentScan,
        extra: _capturedImage,
      );
    }
  }
}
