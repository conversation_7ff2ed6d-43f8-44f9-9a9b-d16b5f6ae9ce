import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/balance_check/data/models/balance_check_model.dart';
import 'package:cbrs/features/balance_check/data/models/linked_account_response_model.dart';
import 'package:cbrs/features/top_up/data/model/top_up_providers_model.dart';
import 'package:cbrs/features/top_up/data/model/top_up_success_model.dart';
import 'package:cbrs/features/transactions/data/models/transaction_model.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/otp_resend_response.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

abstract class TopUpRemoteDataSource {
  Future<TopUpProvidersModel> getTopUpProviders();

  Future<TopUpSuccessResponseModel> createTopUp({
    required String phoneNumber,
    required String amount,
    required String beneficiaryId,
    required String billReason,
  });
}

class TopUpRemoteDataSourceImpl implements TopUpRemoteDataSource {
  const TopUpRemoteDataSourceImpl({
    required ApiService apiService,
  }) : _apiService = apiService;
  final ApiService _apiService;

  @override
  Future<TopUpProvidersModel> getTopUpProviders() async {
    try {
      final result = await _apiService.get(
        ApiEndpoints.getTopUpProviders,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return TopUpProvidersModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }
  }

  @override
  Future<TopUpSuccessResponseModel> createTopUp({
    required String phoneNumber,
    required String amount,
    required String beneficiaryId,
    required String billReason,
  }) async {
    try {
      final postData = {
        'phoneNumber': phoneNumber,
        'amount': AppMapper.safeInt(amount),
        'beneficiaryId': beneficiaryId,
        'billReason': billReason,
      };

      debugPrint(postData.toString());
      final result = await _apiService.post(
        ApiEndpoints.createTopUp,
        data: {'data': postData},
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return TopUpSuccessResponseModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

    //     final response = await _safeApiCall<DataMap>(
    //       () => _dio.post<DataMap>(
    //         ApiEndpoints.createTopUp,
    //         // ApiEndpoints.internalError,

    //         data: {
    //           'phoneNumber': phoneNumber,
    //           'amount': AppMapper.safeInt(amount),
    //         },
    //       ),
    //     );
    //     debugPrint(
    //       ' yes 🎀 \nRemote - pay updfornt or repayment with resposne response \n\n 🎊',
    //     );
    //     if (response.data != null) {
    //       final returnData = TopUpSuccessResponseModel.fromJson(response.data!);

    //       return returnData;
    //     } else {
    //       throw ApiException(
    //         message: 'Empty response from server',
    //         statusCode: response.statusCode ?? 500,
    //       );
    //     }
    //   } on ApiException catch (e) {
    //     debugPrint('Error in creating top up: ${e.message}');
    //     rethrow;
    //   } catch (e) {
    //     throw const ApiException(
    //       message: 'Error in creating top up',
    //       statusCode: 500,
    //     );
    //   }
  }
}
