part of 'transaction_bloc.dart';

abstract class TransactionEvent extends Equatable {
  const TransactionEvent();

  @override
  List<Object?> get props => [];
}

class FetchTransactionsEvent extends TransactionEvent {
  const FetchTransactionsEvent({
    required this.page,
    required this.perPage,
    this.keepCache = true,
  });
  final int page;
  final int perPage;
  final bool keepCache;

  @override
  List<Object?> get props => [page, perPage, keepCache];
}

class FilterTransactionsEvent extends TransactionEvent {
  const FilterTransactionsEvent(this.category);
  final String category;

  @override
  List<Object?> get props => [category];
}

class FilterByDateRangeEvent extends TransactionEvent {
  const FilterByDateRangeEvent({
    required this.startDate,
    required this.endDate,
    required this.transactionType,
  });
  final DateTime startDate;
  final DateTime endDate;
  final String transactionType;

  @override
  List<Object?> get props => [startDate, endDate, transactionType];
}

class FilterBySingleDateEvent extends TransactionEvent {
  const FilterBySingleDateEvent(this.date);
  final DateTime date;

  @override
  List<Object?> get props => [date];
}

class FetchMoreTransactionsEvent extends TransactionEvent {
  const FetchMoreTransactionsEvent({
    required this.page,
    required this.perPage,
  });
  final int page;
  final int perPage;

  @override
  List<Object?> get props => [page, perPage];
}

class FetchRecentWalletTransfersEvent extends TransactionEvent {
  const FetchRecentWalletTransfersEvent({
    this.limit = 5,
  });
  final int limit;

  @override
  List<Object?> get props => [limit];
}

class FetchTransferLimitEvent extends TransactionEvent {
  const FetchTransferLimitEvent({
    required this.transactionType,
    required this.currency,
  });
  final String transactionType;
  final String currency;

  @override
  List<Object?> get props => [transactionType, currency];
}

class GetLoanInvoiceEvent extends TransactionEvent {
  const GetLoanInvoiceEvent({
    required this.billRefNo,
    required this.loanReceipt,
  });
  final String billRefNo;
  final LoanReceipt loanReceipt;

  @override
  List<Object?> get props => [billRefNo, loanReceipt];
}

class DownloadReceiptEvent extends TransactionEvent {
  const DownloadReceiptEvent({
    required this.billRefNo,
  });
  final String billRefNo;

  @override
  List<Object?> get props => [billRefNo];
}

class ConfirmTransferEvent extends TransactionEvent {
  final String pin;
  final String billRefNo;
  final TransactionType transactionType;
  final String? otp;

  const ConfirmTransferEvent({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
    this.otp,
  });

  @override
  List<Object?> get props => [pin, billRefNo, transactionType, otp];
}

class VerifyOtpEvent extends TransactionEvent {
  final String billRefNo;
  final String otpFor;
  final int otpCode;

  const VerifyOtpEvent({
    required this.billRefNo,
    required this.otpFor,
    required this.otpCode,
  });

  @override
  List<Object?> get props => [billRefNo, otpFor, otpCode];
}

class ResendOtpEvent extends TransactionEvent {
  final String billRefNo;
  final String otpFor;

  const ResendOtpEvent({
    required this.billRefNo,
    required this.otpFor,
  });

  @override
  List<Object?> get props => [billRefNo, otpFor];
}
