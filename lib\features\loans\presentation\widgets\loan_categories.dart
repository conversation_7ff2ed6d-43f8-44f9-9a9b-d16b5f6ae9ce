import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/services/connectivity/connectivity_service.dart';
import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_categories_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class LoanCategories extends StatefulWidget {
  const LoanCategories({
    required this.onCategorySelected,
    required this.selectedCategory,
    super.key,
    this.localCategories,
    this.loanType,
    this.useApi = false,
    this.connectivityService,
  });

  final void Function(String?) onCategorySelected;
  final String? selectedCategory;
  final List<LoanCategoryWidget>? localCategories;
  final String? loanType;
  final bool useApi;
  final ConnectivityService? connectivityService;

  @override
  State<LoanCategories> createState() => _LoanCategoriesState();
}

class _LoanCategoriesState extends State<LoanCategories> {
  @override
  void initState() {
    super.initState();
    if (widget.useApi) {
      // Fetch categories from API when using cubit
      context.read<LoanCategoriesCubit>().fetchCategories(
            loanType: widget.loanType,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.useApi) {
      return BlocBuilder<LoanCategoriesCubit, LoanCategoriesState>(
        builder: (context, state) {
          if (state is LoanCategoriesLoading) {
            return _buildLoadingState();
          } else if (state is LoanCategoriesError) {
            // Check if error is due to connectivity issues
            return FutureBuilder<bool>(
              future: _checkConnectivity(),
              builder: (context, connectivitySnapshot) {
                if (connectivitySnapshot.connectionState ==
                    ConnectionState.waiting) {
                  return _buildLoadingState();
                }

                final hasConnectivity = connectivitySnapshot.data ?? true;
                if (!hasConnectivity) {
                  // Show shimmer with retrying message when offline
                  return _buildRetryingState();
                } else {
                  // Show error when online but API failed
                  return _buildErrorState(state.message);
                }
              },
            );
          } else if (state is LoanCategoriesLoaded) {
            return _buildCategoriesList(
              _convertApiCategoriesToLocal(state.categories),
            );
          }
          return _buildCategoriesList(widget.localCategories ?? []);
        },
      );
    } else {
      return _buildCategoriesList(widget.localCategories ?? []);
    }
  }

  Future<bool> _checkConnectivity() async {
    try {
      final connectivityService =
          widget.connectivityService ?? ConnectivityService();
      return await connectivityService.hasInternetConnection();
    } catch (e) {
      // If connectivity check fails, assume no connectivity
      return false;
    }
  }

  Widget _buildCategoriesList(List<LoanCategoryWidget> categories) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: CustomBuildText(
            text: 'Categories',
            fontSize: 14.sp,
            fontWeight: FontWeight.w700,
            color: Colors.black45,
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          height: 110.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = widget.selectedCategory == category.id;
              return _buildCategoryItem(
                context,
                category,
                isSelected,
                category.isAsset,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: CustomBuildText(
            text: 'Categories',
            fontSize: 14.sp,
            fontWeight: FontWeight.w700,
            color: Colors.black45,
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          height: 110.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: 4,
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.only(right: 24.w),
                child: Column(
                  children: [
                    _buildShimmer(size: 64.w, height: 64.h),
                    SizedBox(height: 8.h),
                    _buildShimmer(size: 60.w, height: 12.h),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRetryingState() {
    // Auto-retry every 3 seconds when offline
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && widget.useApi) {
        context.read<LoanCategoriesCubit>().fetchCategories(
              loanType: widget.loanType,
            );
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            children: [
              CustomBuildText(
                text: 'Categories',
                fontSize: 14.sp,
                fontWeight: FontWeight.w700,
                color: Colors.black45,
              ),
              SizedBox(width: 8.w),
              SizedBox(
                width: 12.w,
                height: 12.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                'Retrying...',
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          height: 110.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: 4,
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.only(right: 24.w),
                child: Column(
                  children: [
                    _buildShimmer(size: 64.w, height: 64.h),
                    SizedBox(height: 8.h),
                    _buildShimmer(size: 60.w, height: 12.h),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: CustomBuildText(
            text: 'Categories',
            fontSize: 14.sp,
            fontWeight: FontWeight.w700,
            color: Colors.black45,
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          height: 110.h,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red[400],
                  size: 32.w,
                ),
                SizedBox(height: 8.h),
                Text(
                  'Failed to load categories',
                  style: GoogleFonts.outfit(
                    fontSize: 12.sp,
                    color: Colors.red[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                GestureDetector(
                  onTap: () {
                    context.read<LoanCategoriesCubit>().fetchCategories(
                          loanType: widget.loanType,
                        );
                  },
                  child: Text(
                    'Tap to retry',
                    style: GoogleFonts.outfit(
                      fontSize: 10.sp,
                      color: Colors.blue[600],
                      fontWeight: FontWeight.w500,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<LoanCategoryWidget> _convertApiCategoriesToLocal(
    List<LoanCategory> apiCategories,
  ) {
    return apiCategories
        .map(
          (apiCategory) => LoanCategoryWidget(
            id: apiCategory.id,
            name: apiCategory.name,
            description: apiCategory.description,
            icon: apiCategory.icon,
            // Check if this is an "All" category (asset) or API category
            // (network)
            isAsset: apiCategory.icon.startsWith('assets/'),
          ),
        )
        .toList();
  }

  Widget _buildCategoryItem(
    BuildContext context,
    LoanCategoryWidget category,
    bool isSelected,
    bool isAsset,
  ) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => widget.onCategorySelected(category.id),
      child: Container(
        margin: EdgeInsets.only(right: 24.w),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Column(
              children: [
                Container(
                  padding: EdgeInsets.only(top: isSelected ? 4.h : 0),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 10.w,
                      vertical: 12.h,
                    ),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? theme.primaryColor.withValues(alpha: 0.1)
                          : Colors.white,
                      border: Border.all(
                        color:
                            isSelected ? theme.primaryColor : Colors.grey[300]!,
                        width: isSelected ? 1.5 : 1.0,
                      ),
                    ),
                    child: CircleAvatar(
                      radius:
                          isSelected ? 24.r : 20.r, // Smaller when not selected
                      backgroundColor: Colors.transparent,
                      child: isAsset
                          ? Image.asset(
                              category.icon,
                              width: isSelected ? 64.w : 56.w,
                              height: isSelected ? 64.h : 56.h,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.directions_car,
                                  color: Colors.grey.shade400,
                                  size: isSelected ? 24 : 20,
                                );
                              },
                            )
                          : ClipOval(
                              child: CachedNetworkImage(
                                imageUrl: category.icon,
                                width: isSelected ? 64.w : 56.w,
                                height: isSelected ? 64.h : 56.h,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => _buildShimmer(),
                                errorWidget: (context, url, error) => Icon(
                                  Icons.directions_car,
                                  color: Colors.grey.shade400,
                                  size: isSelected ? 24 : 20,
                                ),
                              ),
                            ),
                    ),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  category.name,
                  style: GoogleFonts.outfit(
                    fontSize: isSelected ? 14.sp : 14.sp,
                    color: isSelected ? theme.primaryColor : Colors.grey[600],
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            // "Selected" label for selected items - moved to front of Stack
            if (isSelected)
              Positioned(
                top: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    'Selected',
                    style: GoogleFonts.outfit(
                      fontSize: 10.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmer({double? size, double? height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: size ?? 40.w,
        height: height ?? 40.h,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

class LoanCategoryWidget {
  const LoanCategoryWidget({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    this.isAsset = false,
  });

  final String id;
  final String name;
  final String description;
  final String icon;
  final bool isAsset;
}
