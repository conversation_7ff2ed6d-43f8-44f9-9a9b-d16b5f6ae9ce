import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/errors/api_error_handler.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/change_to_birr/data/models/change_to_birr_request.dart';
import 'package:cbrs/features/change_to_birr/data/models/change_to_birr_response.dart';
import 'package:cbrs/features/change_to_birr/data/models/otp_resend_response.dart';
import 'package:cbrs/features/change_to_birr/data/models/check_transfer_rules_request.dart';
import 'package:cbrs/features/change_to_birr/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/change_to_birr/domain/repositories/change_to_birr_repository.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:cbrs/features/change_to_birr/data/datasources/change_to_birr_remote_datasource.dart';

class ChangeToBirrRepositoryImpl implements ChangeToBirrRepository {
  ChangeToBirrRepositoryImpl(this.remoteDataSource);
  final ChangeToBirrRemoteDataSource remoteDataSource;

  @override
  ResultFuture<ChangeToBirrResponse> changeToBirr(
      ChangeToBirrRequest request) async {
    try {
      final response = await remoteDataSource.changeToBirr(request.toJson());
      return Right(ChangeToBirrResponse.fromJson(response));
    } on ChangeToBirrException catch (e) {
      return Left(
        ServerFailure(
          message: e.message,
        ),
      );
    } on ApiException catch (e) {
      return Left(
        ServerFailure(
          message: e.message,
        ),
      );
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Please try again later',
        ),
      );
    }
  }

  @override
  ResultFuture<CheckTransferRulesResponse> checkTransferRules(
    CheckTransferRulesRequest request,
  ) async {
    try {
      final response =
          await remoteDataSource.checkTransferRules(request.toJson());
      return Right(CheckTransferRulesResponse.fromJson(response));
    } on ApiException catch (err) {
      return Left(
        ServerFailure(
          message: err.message,
        ),
      );
    } catch (e) {
      rethrow;
    }
  }
}
