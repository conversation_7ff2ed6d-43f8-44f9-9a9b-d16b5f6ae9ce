import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

/// Use case for fetching paginated loan items (cars, houses, etc.)
class GetPaginatedLoanItems extends UsecaseWithParams<PaginatedLoanItemsResult,
    GetPaginatedLoanItemsParams> {
  const GetPaginatedLoanItems(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<PaginatedLoanItemsResult> call(
      GetPaginatedLoanItemsParams params) async {
    return repository.getPaginatedLoanItems(
      loanType: params.loanType,
      page: params.page,
      limit: params.limit,
      categoryId: params.categoryId,
      productId: params.productId,
      searchQuery: params.searchQuery,
    );
  }
}

class GetPaginatedLoanItemsParams extends Equatable {
  const GetPaginatedLoanItemsParams({
    required this.loanType,
    this.page = 1,
    this.limit = 10,
    this.categoryId,
    this.productId,
    this.searchQuery,
  });

  final LoanItemType loanType;
  final int page;
  final int limit;
  final String? categoryId;
  final String? productId;
  final String? searchQuery;

  @override
  List<Object?> get props => [
        loanType,
        page,
        limit,
        categoryId,
        productId,
        searchQuery,
      ];
}

/// Result containing paginated loan items with metadata
class PaginatedLoanItemsResult extends Equatable {
  const PaginatedLoanItemsResult({
    required this.items,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  final List<LoanItem> items;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final bool hasNextPage;
  final bool hasPreviousPage;

  @override
  List<Object?> get props => [
        items,
        currentPage,
        totalPages,
        totalItems,
        hasNextPage,
        hasPreviousPage,
      ];
}
