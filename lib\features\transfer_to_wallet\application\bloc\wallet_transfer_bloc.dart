import 'package:bloc/bloc.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/change_to_birr/data/models/authorization_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/authorization_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/contact_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/member_lookup_response.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/transfer_status.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/wallet_info.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/check_transfer_rules.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/check_transfer_status.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/generate_reciept_usecase.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/get_wallet_details.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/lookup_contacts.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/lookup_member.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/transfer_to_wallet.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/validate_transfer_amount.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

part 'wallet_transfer_event.dart';
part 'wallet_transfer_state.dart';

class WalletTransferBloc
    extends Bloc<WalletTransferEvent, WalletTransferState> {
  WalletTransferBloc({
    required LookupContacts lookupContacts,
    required LookupMember lookupMember,
    required TransferToWallet transferToWallet,
    required ValidateWalletTransferAmount validateTransferAmount,
    required CheckWalletTransferStatus checkTransferStatus,
    // required GetWalletInfo getWalletDetails,
    required GenerateRecieptUsecase generateRecieptUsecase,
    required this.checkTransferRulesUseCase,
  })  : _lookupContacts = lookupContacts,
        _lookupMember = lookupMember,
        _transferToWallet = transferToWallet,
        _validateTransferAmount = validateTransferAmount,
        _checkTransferStatus = checkTransferStatus,
        // _getWalletDetails = getWalletDetails,
        _generateRecieptUsecase = generateRecieptUsecase,
        super(const WalletTransferInitial()) {
    on<LookupContactsEvent>(_handleLookupContacts);
    on<LookupMemberEvent>(_handleLookupMember);
    on<TransferToWalletEvent>(_handleTransferToWallet);
    on<ValidateTransferAmountEvent>(_handleValidateTransferAmount);
    on<CheckTransferStatusEvent>(_handleCheckTransferStatus);
    // on<GetWalletDetailsEvent>(_handleGetWalletDetails);
    on<CheckWalletTransferRulesRequested>(_onCheckTransferRulesRequested);
    on<GenerateRecieptEvent>(_onGenerateReceiptRequested);
  }
  final LookupContacts _lookupContacts;
  final LookupMember _lookupMember;
  final TransferToWallet _transferToWallet;
  final ValidateWalletTransferAmount _validateTransferAmount;
  final CheckWalletTransferStatus _checkTransferStatus;
  // final GetWalletInfo _getWalletDetails;
  final CheckWalletTransferRules checkTransferRulesUseCase;
  final GenerateRecieptUsecase _generateRecieptUsecase;

  Future<void> _handleLookupContacts(
    LookupContactsEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const WalletTransferLoading());
    final result = await _lookupContacts(
      LookupContactsParams(phoneNumbers: event.phoneNumbers),
    );
    result.fold(
      (failure) => emit(WalletTransferError(failure.message)),
      (contacts) => emit(ContactsLookupSuccess(contacts)),
    );
  }

  Future<void> _handleLookupMember(
    LookupMemberEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const WalletTransferLoading());

    try {
      final result = await _lookupMember(
        LookupMemberParams(
          email: event.email,
          phoneNumber: event.phoneNumber,
        ),
      );

      result.fold(
        (failure) {
          if (failure is NotFoundFailure) {
            emit(MemberLookupNotFound(failure.message));
          } else {
            emit(WalletTransferError(failure.message));
          }
        },
        (member) => emit(MemberLookupSuccess(member)),
      );
    } catch (e) {
      emit(WalletTransferError(e.toString()));
    }
  }

  Future<void> _handleTransferToWallet(
    TransferToWalletEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const WalletTransferLoading());
    try {
      final result = await _transferToWallet(
        TransferToWalletParams(
          beneficiaryEmail: event.beneficiaryEmail,
          beneficiaryPhone: event.beneficiaryPhone,
          beneficiaryId: event.beneficiaryId,
          amount: event.amount,
          currency: event.currency,
        ),
      );

      result.fold(
        (failure) {
          return emit(WalletTransferError(failure.message));
        },
        (response) {
          final authResponse =
              WalletTransferAuthResponse.fromJson(response.toJson());
          debugPrint(authResponse.billRefNo);
          debugPrint(authResponse.type);

          if (authResponse.isFree && response.statusCode == 200) {
            emit(
              WalletTransferSuccess(
                message: response.message,
                response: response,
                shouldNavigate: true,
                recipientName: response.data.beneficiaryName,
                amount: response.data.totalAmount.toString(),
                transferNumber: response.data.billRefNo,
              ),
            );
          } else if (authResponse.requiresPin) {
            debugPrint('authResponse.billRefNo: ${authResponse.billRefNo}');
            emit(
              WalletTransferPinRequired(
                billRefNo: authResponse.billRefNo,
                response: response,
                requiresOtp: authResponse.requiresOtp,
                otp: authResponse.otp,
              ),
            );
          } else {
            emit(WalletTransferError(response.message));
          }
        },
      );
    } catch (e) {
      debugPrint('error: $e');
      emit(WalletTransferError(e.toString()));
    }
  }

  Future<void> _handleValidateTransferAmount(
    ValidateTransferAmountEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const WalletTransferLoading());
    final result = await _validateTransferAmount(
      ValidateTransferAmountParams(
        amount: event.amount,
        currency: event.currency,
      ),
    );
    result.fold(
      (failure) => emit(WalletTransferError(failure.message)),
      (_) => emit(const TransferAmountValidated()),
    );
  }

  Future<void> _handleCheckTransferStatus(
    CheckTransferStatusEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const WalletTransferLoading());

    try {
      final result = await _checkTransferStatus(
        CheckTransferStatusParams(transactionId: event.transactionId),
      );

      result.fold(
        (failure) => emit(WalletTransferError(failure.message)),
        (status) {
          final transferStatus = TransferStatus(
            id: status.transactionId,
            status: status.status,
            transferNumber: status.transactionId,
            amount: status.amount,
            currency: status.currency,
            recipientName: '',
            recipientMemberCode: '',
            transferDate: status.timestamp,
            lastModified: status.timestamp,
          );
          emit(TransferStatusChecked(transferStatus));
        },
      );
    } catch (e) {
      emit(WalletTransferError(e.toString()));
    }
  }

/*
  Future<void> _handleGetWalletDetails(
    GetWalletDetailsEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const WalletDetailsLoadingState());

    final result = await _getWalletDetails();

    result.fold(
      (failure) => emit(WalletTransferError(failure.message)),
      (walletDetails) {
        try {
          final balances = {
            'USD': WalletBalance(
              currency: 'USD',
              amount: walletDetails.balances.entries
                  .firstWhere((entry) => entry.key == 'USD')
                  .value
                  .amount,
              walletCode: walletDetails.balances.entries
                  .firstWhere((entry) => entry.key == 'USD')
                  .value
                  .walletCode,
            ),
            'ETB': WalletBalance(
              currency: 'ETB',
              amount: walletDetails.balances.entries
                  .firstWhere((entry) => entry.key == 'ETB')
                  .value
                  .amount,
              walletCode: walletDetails.balances.entries
                  .firstWhere((entry) => entry.key == 'ETB')
                  .value
                  .walletCode,
            ),
          };
          debugPrint("Wallet details loadddded");

          emit(
            WalletDetailsLoaded(
              WalletInfo(
                id: walletDetails.walletId,
                walletId: walletDetails.walletId,
                currency: event.currency,
                balance: balances[event.currency]?.amount ?? 0.0,
                walletCode: balances[event.currency]?.walletCode ?? '',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                isActive: true,
                balances: balances,
              ),
            ),
          );
        } catch (e) {
          emit(
            const WalletTransferError(
              'Error processing wallet details. Please try again.',
            ),
          );
        }
      },
    );
  }
*/
  Future<bool> validateTransferAmount({
    required double amount,
    required String currency,
    required WalletInfo wallet,
  }) async {
    if (amount <= 0) {
      return false;
    }

    final balance = wallet.balances[currency]?.amount ?? 0.0;
    return amount <= balance;
  }

  String? getTransferError({
    required double amount,
    required String currency,
    required WalletInfo wallet,
  }) {
    if (amount <= 0) {
      return 'Amount must be greater than 0';
    }

    final balance = wallet.balances[currency]?.amount ?? 0.0;
    if (amount > balance) {
      return 'Insufficient balance';
    }

    return null;
  }

  Future<void> _onCheckTransferRulesRequested(
    CheckWalletTransferRulesRequested event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const CheckingWalletTransferRules());

    final result = await checkTransferRulesUseCase(
      CheckWalletTransferRulesParams(
          amount: event.amount,
          currency: event.currency,
          productType: event.productType),
    );

    result.fold(
      (failure) => emit(WalletTransferFailure(failure.message)),
      (response) {
        if (!response.success) {
          emit(WalletTransferFailure(response.message));
          return;
        }
        emit(WalletTransferRulesChecked(response));
      },
    );
  }

  Future<void> _onGenerateReceiptRequested(
    GenerateRecieptEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const WalletTransferLoading());

    final result = await _generateRecieptUsecase(
      GenerateReceiptParams(
        billRefNo: event.billRefNo,
      ),
    );

    result.fold(
      (failure) => emit(WalletTransferFailure(failure.message)),
      (response) {
        debugPrint('responseff: $response');
        emit(GeneratedReceiptState(response));

        debugPrint('reciept updated tekkkle ${state}');
      },
    );
  }
}
