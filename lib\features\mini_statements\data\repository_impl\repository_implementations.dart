import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/mini_statements/data/datasource/remote_data_source.dart';
import 'package:cbrs/features/mini_statements/domain/repository/mini_statement_repository.dart';
import 'package:dartz/dartz.dart';

class MiniStatementRepositoryImpl implements MiniStatementRepository {
  MiniStatementRepositoryImpl({required MiniStatementRemoteDataSource remoteDataSource}) : _remoteDataSource = remoteDataSource ;
  final MiniStatementRemoteDataSource _remoteDataSource;

  @override
  ResultFuture<String> generateMiniStatements({
    required String startDate,
    required String endDate,
    required String Currency,
  }) async {
    try {
      final result = await _remoteDataSource.generateMiniStatements(
        startDate: startDate,
        endDate: endDate,
        currency: Currency,
      );

      return Right(result); 
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(NotFoundFailure(message: e.toString()));
    }
  }
}
