import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:google_fonts/google_fonts.dart';

class UtilityPaymentPermissionModal extends StatelessWidget {
  final String utilityName;
  final String amount;
  final String walletBalance;
  final String customerName;
  final String accountNumber;
  final VoidCallback onNext;

  const UtilityPaymentPermissionModal({
    super.key,
    required this.utilityName,
    required this.amount,
    required this.walletBalance,
    required this.customerName,
    required this.accountNumber,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(30.r),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(bottom: 24.h),
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Text(
              utilityName.toUpperCase(),
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                fontWeight: FontWeight.w800,
                letterSpacing: 1.2,
              ),
            ),
            SizedBox(height: 5.h),
            Text(
              'ETB $amount',
              style: GoogleFonts.outfit(
                fontSize: 32.sp,
                fontWeight: FontWeight.w800,
                color: const Color(0xFF559948),
              ),
            ),
            SizedBox(height: 15.h),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 24.w,
                vertical: 16.h,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFE6F7E2),
                borderRadius: BorderRadius.circular(100.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Wallet Balance: ',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    'ETB $walletBalance',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 16.h),
              decoration: BoxDecoration(
                color: const Color(0xFFF8FAF9),
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Payment Details',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildDetailRow('Customer Name:', customerName),
                  SizedBox(height: 12.h),
                  _buildDottedLine(),
                  SizedBox(height: 12.h),
                  _buildDetailRow('Account Number:', accountNumber),
                  SizedBox(height: 12.h),
                  _buildDottedLine(),
                  SizedBox(height: 12.h),
                  _buildDetailRow(
                    'Payment Date:',
                    DateTime.now().toString().split(' ')[0],
                  ),
                ],
              ),
            ),
            SizedBox(height: 32.h),
            SizedBox(
              width: double.infinity,
              height: 56.h,
              child: ElevatedButton(
                onPressed: onNext,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF559948),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100.r),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Next',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildDottedLine() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      height: 1,
      child: CustomPaint(
        painter: DottedLinePainter(
          color: Colors.grey[400]!,
        ),
        size: const Size(double.infinity, 1),
      ),
    );
  }
}

void showUtilityPaymentPermissionModal(BuildContext context) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (context) => UtilityPaymentPermissionModal(
      utilityName: 'Utility Name',
      amount: '1,500.00',
      walletBalance: '20,000.00',
      customerName: 'John Doe',
      accountNumber: '**********',
      onNext: () {
        Navigator.pop(context);
        // Add navigation logic here for the next screen
      },
    ),
  );
}
