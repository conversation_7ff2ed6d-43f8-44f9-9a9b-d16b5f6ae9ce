import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_confirmation.dart';

/// Model for loan payment confirmation response
class LoanPaymentConfirmationModel extends LoanPaymentConfirmation {
  const LoanPaymentConfirmationModel({
    required super.id,
    required super.loanId,
    required super.productId,
    required super.memberId,
    required super.status,
    required super.upfrontPaymentPercentage,
    required super.interestRate,
    required super.upfrontPaymentAmount,
    required super.loanAmount,
    required super.monthlyPayment,
    required super.startDate,
    required super.endDate,
    required super.totalPaid,
    required super.totalPrincipalPaid,
    required super.totalInterestPaid,
    required super.totalPenaltiesPaid,
    required super.remainingBalance,
    required super.totalPayment,
    required super.nextPaymentDate,
    required super.isOverdue,
    required super.totalInstallments,
    required super.paidInstallments,
    required super.missedInstallments,
    required super.isAutoRepay,
    required super.createdAt,
    required super.updatedAt,
    required super.loanApplicationCode,
    super.applicationFeeTransaction,
    super.upfrontPaymentTransaction,
    super.lastPaymentDate,
    super.completedDate,
    super.approvedAt,
    super.documents,
    super.approvedBy,
    super.rejectedBy,
    super.rejectedAt,
    super.rejectionReason,
    super.lastModifiedAt,
    super.chassisNumber,
    super.plateNumber,
    super.libreNumber,
    super.proformaNumber,
    super.lienNumber,
    super.housePlanNumber,
    super.houseLienNumber,
    super.contractNumber,
    super.estimationAmount,
    super.estimationDate,
  });

  factory LoanPaymentConfirmationModel.fromJson(Map<String, dynamic> json) {
    return LoanPaymentConfirmationModel(
      id: AppMapper.safeString(json['id']),
      loanId: AppMapper.safeString(json['loanId']),
      productId: AppMapper.safeString(json['productId']),
      memberId: AppMapper.safeString(json['memberId']),
      status: AppMapper.safeString(json['status']),
      upfrontPaymentPercentage:
          AppMapper.safeString(json['upfrontPaymentPercentage']),
      interestRate: AppMapper.safeString(json['interestRate']),
      upfrontPaymentAmount: AppMapper.safeString(json['upfrontPaymentAmount']),
      loanAmount: AppMapper.safeString(json['loanAmount']),
      monthlyPayment: AppMapper.safeString(json['monthlyPayment']),
      startDate: AppMapper.safeString(json['startDate']),
      endDate: AppMapper.safeString(json['endDate']),
      totalPaid: AppMapper.safeString(json['totalPaid']),
      totalPrincipalPaid: AppMapper.safeString(json['totalPrincipalPaid']),
      totalInterestPaid: AppMapper.safeString(json['totalInterestPaid']),
      totalPenaltiesPaid: AppMapper.safeString(json['totalPenaltiesPaid']),
      remainingBalance: AppMapper.safeString(json['remainingBalance']),
      totalPayment: AppMapper.safeString(json['totalPayment']),
      nextPaymentDate: AppMapper.safeString(json['nextPaymentDate']),
      isOverdue: AppMapper.safeBool(json['isOverdue']),
      totalInstallments: AppMapper.safeInt(json['totalInstallments']),
      paidInstallments: AppMapper.safeInt(json['paidInstallments']),
      missedInstallments: AppMapper.safeInt(json['missedInstallments']),
      isAutoRepay: AppMapper.safeBool(json['isAutoRepay']),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
      loanApplicationCode: AppMapper.safeString(json['loanApplicationCode']),
      applicationFeeTransaction: json['applicationFeeTransaction'] != null
          ? ApplicationFeeTransactionModel.fromJson(
              json['applicationFeeTransaction'] as Map<String, dynamic>,
            )
          : null,
      upfrontPaymentTransaction: json['upfrontPaymentTransaction'] != null
          ? const UpfrontPaymentTransactionModel()
          : null,
      lastPaymentDate: json['lastPaymentDate'] != null
          ? AppMapper.safeString(json['lastPaymentDate'])
          : null,
      completedDate: json['completedDate'] != null
          ? DateTime.parse(json['completedDate'] as String)
          : null,
      approvedAt: json['approvedAt'] != null
          ? DateTime.parse(json['approvedAt'] as String)
          : null,
      documents: json['documents'] != null
          ? AppMapper.safeString(json['documents'])
          : null,
      approvedBy: json['approvedBy'] != null
          ? AppMapper.safeString(json['approvedBy'])
          : null,
      rejectedBy: json['rejectedBy'] != null
          ? AppMapper.safeString(json['rejectedBy'])
          : null,
      rejectedAt: json['rejectedAt'] != null
          ? DateTime.parse(json['rejectedAt'] as String)
          : null,
      rejectionReason: json['rejectionReason'] != null
          ? AppMapper.safeString(json['rejectionReason'])
          : null,
      lastModifiedAt: json['lastModifiedAt'] != null
          ? DateTime.parse(json['lastModifiedAt'] as String)
          : null,
      chassisNumber: json['chassisNumber'] != null
          ? AppMapper.safeString(json['chassisNumber'])
          : null,
      plateNumber: json['plateNumber'] != null
          ? AppMapper.safeString(json['plateNumber'])
          : null,
      libreNumber: json['libreNumber'] != null
          ? AppMapper.safeString(json['libreNumber'])
          : null,
      proformaNumber: json['proformaNumber'] != null
          ? AppMapper.safeString(json['proformaNumber'])
          : null,
      lienNumber: json['lienNumber'] != null
          ? AppMapper.safeString(json['lienNumber'])
          : null,
      housePlanNumber: json['housePlanNumber'] != null
          ? AppMapper.safeString(json['housePlanNumber'])
          : null,
      houseLienNumber: json['houseLienNumber'] != null
          ? AppMapper.safeString(json['houseLienNumber'])
          : null,
      contractNumber: json['contractNumber'] != null
          ? AppMapper.safeString(json['contractNumber'])
          : null,
      estimationAmount: json['estimationAmount'] != null
          ? AppMapper.safeString(json['estimationAmount'])
          : null,
      estimationDate: json['estimationDate'] != null
          ? AppMapper.safeString(json['estimationDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'loanId': loanId,
      'productId': productId,
      'memberId': memberId,
      'status': status,
      'upfrontPaymentPercentage': upfrontPaymentPercentage,
      'interestRate': interestRate,
      'upfrontPaymentAmount': upfrontPaymentAmount,
      'loanAmount': loanAmount,
      'monthlyPayment': monthlyPayment,
      'startDate': startDate,
      'endDate': endDate,
      'totalPaid': totalPaid,
      'totalPrincipalPaid': totalPrincipalPaid,
      'totalInterestPaid': totalInterestPaid,
      'totalPenaltiesPaid': totalPenaltiesPaid,
      'remainingBalance': remainingBalance,
      'totalPayment': totalPayment,
      'nextPaymentDate': nextPaymentDate,
      'isOverdue': isOverdue,
      'totalInstallments': totalInstallments,
      'paidInstallments': paidInstallments,
      'missedInstallments': missedInstallments,
      'isAutoRepay': isAutoRepay,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'loanApplicationCode': loanApplicationCode,
      if (applicationFeeTransaction != null)
        'applicationFeeTransaction':
            (applicationFeeTransaction as ApplicationFeeTransactionModel)
                .toJson(),
      if (upfrontPaymentTransaction != null)
        'upfrontPaymentTransaction':
            (upfrontPaymentTransaction as UpfrontPaymentTransactionModel)
                .toJson(),
      if (lastPaymentDate != null) 'lastPaymentDate': lastPaymentDate,
      if (completedDate != null)
        'completedDate': completedDate!.toIso8601String(),
      if (approvedAt != null) 'approvedAt': approvedAt!.toIso8601String(),
      if (documents != null) 'documents': documents,
      if (approvedBy != null) 'approvedBy': approvedBy,
      if (rejectedBy != null) 'rejectedBy': rejectedBy,
      if (rejectedAt != null) 'rejectedAt': rejectedAt!.toIso8601String(),
      if (rejectionReason != null) 'rejectionReason': rejectionReason,
      if (lastModifiedAt != null)
        'lastModifiedAt': lastModifiedAt!.toIso8601String(),
      if (chassisNumber != null) 'chassisNumber': chassisNumber,
      if (plateNumber != null) 'plateNumber': plateNumber,
      if (libreNumber != null) 'libreNumber': libreNumber,
      if (proformaNumber != null) 'proformaNumber': proformaNumber,
      if (lienNumber != null) 'lienNumber': lienNumber,
      if (housePlanNumber != null) 'housePlanNumber': housePlanNumber,
      if (houseLienNumber != null) 'houseLienNumber': houseLienNumber,
      if (contractNumber != null) 'contractNumber': contractNumber,
      if (estimationAmount != null) 'estimationAmount': estimationAmount,
      if (estimationDate != null) 'estimationDate': estimationDate,
    };
  }
}

/// Model for application fee transaction
class ApplicationFeeTransactionModel extends ApplicationFeeTransaction {
  const ApplicationFeeTransactionModel({
    required super.vat,
    required super.loanId,
    required super.status,
    required super.elstRef,
    required super.bankCode,
    required super.bankName,
    required super.loanType,
    required super.paidDate,
    required super.senderId,
    required super.billRefNo,
    required super.createdAt,
    required super.billAmount,
    required super.paidAmount,
    required super.senderName,
    required super.senderEmail,
    required super.senderPhone,
    required super.beneficiaryId,
    required super.paymentMethod,
    required super.serviceCharge,
    required super.lastModifiedAt,
    required super.paymentDetails,
    required super.productDetails,
    required super.beneficiaryName,
    required super.transactionType,
    required super.transactionOwner,
    required super.originalCurrency,
    required super.paymentReference,
    required super.authorizationType,
    required super.walletFTNumber,
    required super.lastModified,
  });

  factory ApplicationFeeTransactionModel.fromJson(Map<String, dynamic> json) {
    return ApplicationFeeTransactionModel(
      vat: AppMapper.safeDouble(json['VAT']),
      loanId: AppMapper.safeString(json['loanId']),
      status: AppMapper.safeString(json['status']),
      elstRef: AppMapper.safeString(json['ELSTRef']),
      bankCode: AppMapper.safeString(json['bankCode']),
      bankName: AppMapper.safeString(json['bankName']),
      loanType: AppMapper.safeString(json['loanType']),
      paidDate: json['paidDate'] != null
          ? DateTime.parse(json['paidDate'] as String)
          : DateTime.now(),
      senderId: AppMapper.safeString(json['senderId']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      paidAmount: AppMapper.safeDouble(json['paidAmount']),
      senderName: AppMapper.safeString(json['senderName']),
      senderEmail: AppMapper.safeString(json['senderEmail']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      paymentMethod: AppMapper.safeString(json['paymentMethod']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      lastModifiedAt: json['lastModifiedAt'] != null
          ? DateTime.parse(json['lastModifiedAt'] as String)
          : DateTime.now(),
      paymentDetails: json['paymentDetails'] != null
          ? ConfirmationPaymentDetailsModel.fromJson(
              json['paymentDetails'] as Map<String, dynamic>,
            )
          : const ConfirmationPaymentDetailsModel(
              currency: 'USD',
              walletId: '',
            ),
      productDetails: json['productDetails'] != null
          ? ConfirmationProductDetailsModel.fromJson(
              json['productDetails'] as Map<String, dynamic>,
            )
          : ConfirmationProductDetailsModel(
              productName: '',
              productPrice: 0,
              productDetails: ConfirmationProductInfoModel.fromJson(const {}),
            ),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      transactionType: AppMapper.safeString(json['transactionType']),
      transactionOwner: AppMapper.safeString(json['transactionOwner']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      paymentReference: AppMapper.safeString(json['paymentReference']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      walletFTNumber: AppMapper.safeString(json['walletFTNumber']),
      lastModified: json['lastModified'] != null
          ? DateTime.parse(json['lastModified'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'VAT': vat,
      'loanId': loanId,
      'status': status,
      'ELSTRef': elstRef,
      'bankCode': bankCode,
      'bankName': bankName,
      'loanType': loanType,
      'paidDate': paidDate.toIso8601String(),
      'senderId': senderId,
      'billRefNo': billRefNo,
      'createdAt': createdAt.toIso8601String(),
      'billAmount': billAmount,
      'paidAmount': paidAmount,
      'senderName': senderName,
      'senderEmail': senderEmail,
      'senderPhone': senderPhone,
      'beneficiaryId': beneficiaryId,
      'paymentMethod': paymentMethod,
      'serviceCharge': serviceCharge,
      'lastModifiedAt': lastModifiedAt.toIso8601String(),
      'paymentDetails':
          (paymentDetails as ConfirmationPaymentDetailsModel).toJson(),
      'productDetails':
          (productDetails as ConfirmationProductDetailsModel).toJson(),
      'beneficiaryName': beneficiaryName,
      'transactionType': transactionType,
      'transactionOwner': transactionOwner,
      'originalCurrency': originalCurrency,
      'paymentReference': paymentReference,
      'authorization_type': authorizationType,
      'walletFTNumber': walletFTNumber,
      'lastModified': lastModified.toIso8601String(),
    };
  }
}

class UpfrontPaymentTransactionModel extends UpfrontPaymentTransaction {
  const UpfrontPaymentTransactionModel();

  factory UpfrontPaymentTransactionModel.fromJson(Map<String, dynamic> _) {
    return const UpfrontPaymentTransactionModel();
  }

  Map<String, dynamic> toJson() {
    return {};
  }
}

/// Model for confirmation payment details
class ConfirmationPaymentDetailsModel extends ConfirmationPaymentDetails {
  const ConfirmationPaymentDetailsModel({
    required super.currency,
    required super.walletId,
  });

  factory ConfirmationPaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return ConfirmationPaymentDetailsModel(
      currency: AppMapper.safeString(json['currency']),
      walletId: AppMapper.safeString(json['walletId']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currency': currency,
      'walletId': walletId,
    };
  }
}

/// Model for confirmation product details
class ConfirmationProductDetailsModel extends ConfirmationProductDetails {
  const ConfirmationProductDetailsModel({
    required super.productName,
    required super.productPrice,
    required super.productDetails,
  });

  factory ConfirmationProductDetailsModel.fromJson(Map<String, dynamic> json) {
    return ConfirmationProductDetailsModel(
      productName: AppMapper.safeString(json['productName']),
      productPrice: AppMapper.safeDouble(json['productPrice']),
      productDetails: json['productDetails'] != null
          ? ConfirmationProductInfoModel.fromJson(
              json['productDetails'] as Map<String, dynamic>,
            )
          : ConfirmationProductInfoModel.fromJson(const {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productName': productName,
      'productPrice': productPrice,
      'productDetails':
          (productDetails as ConfirmationProductInfoModel).toJson(),
    };
  }
}

/// Model for confirmation product info
class ConfirmationProductInfoModel extends ConfirmationProductInfo {
  const ConfirmationProductInfoModel({
    required super.id,
    required super.make,
    required super.name,
    required super.color,
    required super.model,
    required super.price,
    required super.mileage,
    required super.bodyType,
    required super.fuelType,
    required super.createdAt,
    required super.isDeleted,
    required super.updatedAt,
    required super.driveTrain,
    required super.engineSize,
    required super.horsePower,
    required super.featureImage,
    required super.transmission,
    required super.fuelEfficency,
    required super.galleryImages,
    required super.numberOfSeats,
    required super.engineCapacity,
    required super.manufactureYear,
  });

  factory ConfirmationProductInfoModel.fromJson(Map<String, dynamic> json) {
    return ConfirmationProductInfoModel(
      id: AppMapper.safeString(json['_id']),
      make: AppMapper.safeString(json['make']),
      name: AppMapper.safeString(json['name']),
      color: AppMapper.safeString(json['color']),
      model: AppMapper.safeString(json['model']),
      price: AppMapper.safeDouble(json['price']),
      mileage: AppMapper.safeInt(json['mileage']),
      bodyType: json['bodyType'] != null
          ? ConfirmationBodyTypeModel.fromJson(
              json['bodyType'] as Map<String, dynamic>,
            )
          : const ConfirmationBodyTypeModel(
              id: '',
              name: '',
              description: '',
            ),
      fuelType: AppMapper.safeString(json['fuelType']),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      isDeleted: AppMapper.safeBool(json['isDeleted']),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
      driveTrain: AppMapper.safeString(json['driveTrain']),
      engineSize: AppMapper.safeDouble(json['engineSize']),
      horsePower: AppMapper.safeInt(json['horsePower']),
      featureImage: AppMapper.safeString(json['featureImage']),
      transmission: AppMapper.safeString(json['transmission']),
      fuelEfficency: AppMapper.safeString(json['fuelEfficency']),
      galleryImages: (json['galleryImages'] as List<dynamic>?)
              ?.map(
                (image) => ConfirmationGalleryImageModel.fromJson(
                  image as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      numberOfSeats: AppMapper.safeInt(json['numberOfSeats']),
      engineCapacity: AppMapper.safeString(json['engineCapacity']),
      manufactureYear: AppMapper.safeInt(json['manufactureYear']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'make': make,
      'name': name,
      'color': color,
      'model': model,
      'price': price,
      'mileage': mileage,
      'bodyType': (bodyType as ConfirmationBodyTypeModel).toJson(),
      'fuelType': fuelType,
      'createdAt': createdAt.toIso8601String(),
      'isDeleted': isDeleted,
      'updatedAt': updatedAt.toIso8601String(),
      'driveTrain': driveTrain,
      'engineSize': engineSize,
      'horsePower': horsePower,
      'featureImage': featureImage,
      'transmission': transmission,
      'fuelEfficency': fuelEfficency,
      'galleryImages': galleryImages
          .map((image) => (image as ConfirmationGalleryImageModel).toJson())
          .toList(),
      'numberOfSeats': numberOfSeats,
      'engineCapacity': engineCapacity,
      'manufactureYear': manufactureYear,
    };
  }
}

/// Model for confirmation body type
class ConfirmationBodyTypeModel extends ConfirmationBodyType {
  const ConfirmationBodyTypeModel({
    required super.id,
    required super.name,
    required super.description,
  });

  factory ConfirmationBodyTypeModel.fromJson(Map<String, dynamic> json) {
    return ConfirmationBodyTypeModel(
      id: AppMapper.safeString(json['_id']),
      name: AppMapper.safeString(json['name']),
      description: AppMapper.safeString(json['description']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'description': description,
    };
  }
}

/// Model for confirmation gallery image
class ConfirmationGalleryImageModel extends ConfirmationGalleryImage {
  const ConfirmationGalleryImageModel({
    required super.url,
    required super.type,
  });

  factory ConfirmationGalleryImageModel.fromJson(Map<String, dynamic> json) {
    return ConfirmationGalleryImageModel(
      url: AppMapper.safeString(json['url']),
      type: AppMapper.safeString(json['type']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'type': type,
    };
  }
}
