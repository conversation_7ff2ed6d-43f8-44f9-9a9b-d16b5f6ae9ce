import 'package:cbrs/features/loans/data/models/loan_application_fee_transaction_model.dart';
import 'package:cbrs/features/loans/data/models/loan_application_model.dart';
import 'package:cbrs/features/loans/data/models/loan_bank_model.dart';
import 'package:cbrs/features/loans/data/models/loan_category_model.dart';
import 'package:cbrs/features/loans/data/models/loan_confirm_otp_response_model.dart';
import 'package:cbrs/features/loans/data/models/loan_confirm_pin_response_model.dart';
import 'package:cbrs/features/loans/data/models/loan_item_model.dart';
import 'package:cbrs/features/loans/data/models/loan_payment_confirmation_model.dart';
import 'package:cbrs/features/loans/data/models/loan_payment_info_model.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';

abstract class LoanRemoteDataSource {
  Future<PaginatedLoanItemsResult> getPaginatedLoanItems({
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
    String? categoryId,
    String? productId,
    String? searchQuery,
  });

  /// Get a single loan item by ID
  Future<LoanItemModel> getLoanItemById({
    required String itemId,
    required LoanItemType loanType,
  });

  /// Get loan banks for a specific loan type
  Future<List<LoanBankModel>> getLoanBanks({
    required String productId,
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
  });

  /// Get loan payment information
  Future<LoanPaymentInfoModel> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
  });

  /// Calculate loan payment details
  Future<Map<String, dynamic>> calculateLoanPayment({
    required String bankId,
    required String loanProductId,
    required String upfrontPaymentPercentage,
    required String productId,
    required double interestRate,
    required LoanItemType loanType,
  });

  /// Apply for a loan
  Future<LoanApplicationModel> applyForLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
    bool isAutoRepay = false,
  });

  /// Generate application fee transaction
  Future<LoanApplicationFeeTransactionModel> generateApplicationTransaction({
    required String loanApplicationId,
    required LoanItemType loanType,
  });

  /// Get loan terms
  Future<String> getLoanTerms({
    required String bankId,
    required LoanItemType loanType,
  });

  /// Confirm with PIN
  Future<LoanConfirmPinResponseModel> confirmWithPin({
    required String billRefNo,
    required String pin,
    required String transactionType,
  });

  /// Confirm with OTP
  Future<LoanConfirmOtpResponseModel> confirmWithOtp({
    required String billRefNo,
    required String otpCode,
    required String transactionType,
  });

  /// Resend OTP
  Future<String> resendOtp({
    required String otpFor,
    required String billRefNo,
  });

  /// Confirm loan payment with PIN
  Future<LoanPaymentConfirmationModel> confirmLoanPayment({
    required String transactionType,
    required String billRefNo,
    required String pin,
  });

  /// Get loan categories
  Future<List<LoanCategoryModel>> getLoanCategories({
    int page = 1,
    int limit = 10,
    String? loanType,
  });

  /// Get loan category by ID
  Future<LoanCategoryModel> getLoanCategoryById({
    required String categoryId,
  });
}
