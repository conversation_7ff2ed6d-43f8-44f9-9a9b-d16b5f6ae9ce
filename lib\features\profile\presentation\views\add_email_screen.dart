import 'dart:async';

import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/form_validation.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class AddEmailScreen extends StatefulWidget {
  const AddEmailScreen({required this.email, super.key});

  final String email;

  @override
  State<AddEmailScreen> createState() => _AddEmailScreenState();
}

class _AddEmailScreenState extends State<AddEmailScreen> {
  final _formKey = GlobalKey<FormState>();

  late final TextEditingController _emailController;

  final bool _isLoading = false;
  String emailAddress = '';

  @override
  void initState() {
    super.initState();
    _fetchLocalUserData();
    _emailController = TextEditingController(text: widget.email);
    setState(() {});
  }

  // user local data

  Future<void> _fetchLocalUserData() async {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();

    setState(() {
      _emailController.text = user?.email ?? '';
      //  TextEditingController(text: user?.email ?? '');
      emailAddress = user?.email ?? '';
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocConsumer<ProfileBloc, ProfileState>(
      listener: (context, state) {
        if (state is PhoneAddError) {
          // ScaffoldMessenger.of(context).showSnackBar(
          //   SnackBar(content: Text(state.message)),
          // );
          CustomToastification(context, message: state.message);
        } else if (state is EmailAdded) {
          debugPrint(
            'Email verified successfully, navigating to create PIN screen',
          );

          if (state.emailAddResponseModel.success) {
            context.pushNamed(
              AppRouteName.verifyAddedEmail,
              extra: {
                'email': _emailController.text,
                'isPhoneVerify': false,
                'source': 'add_email',
                'otp': state.emailAddResponseModel.otp,
              },
            );
          } else {
            CustomToastification(
              context,
              message: state.emailAddResponseModel.message,
            );
          }
        } else if (state is EmailVerificationSentState) {
          CustomToastification(
            context,
            message: 'New verification code sent to your email',
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: AppBar(
              title: const Text(
                'Add Email',
              ),
            ),
            body: GestureDetector(
              onTap: () {
                debugPrint('Keyboard hide');
                FocusScope.of(context).unfocus(); // Hide the keyboard
              },
              child: SafeArea(
                // bottom: false,
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(
                            vertical: 16.h, horizontal: 16.w),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              const CustomPageHeader(
                                pageTitle: 'Add Email',
                                description:
                                    'Enter your email and verify it to start receiving money directly to your email.',
                              ),
                              const SizedBox(
                                height: 16,
                              ),
                              CustomTextInput(
                                inputLabel: 'Email Address',
                                hintText: 'Enter email address',
                                controller: _emailController,
                                validator:
                                    FormValidation.validateEmailWithoutPhone,
                                keyboardType: TextInputType.emailAddress,
                              ),
                              SizedBox(height: 36.h),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 16, right: 16, bottom: 20),
                      child: CustomRoundedBtn(
                        btnText: emailAddress.isNotEmpty ? 'Verify' : 'Add',
                        isLoading: state is EmailAddLoad,
                        onTap: () {
                          if (_formKey.currentState?.validate() ?? false) {
                            context
                                .read<ProfileBloc>()
                                .add(AddEmailEvent(_emailController.text));
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ));
      },
    );
  }

  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    required FormFieldValidator<String> validator,
    required ThemeData theme,
    bool isRequired = false,
    bool isReadOnly = false,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: label,
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.grey.shade600,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: GoogleFonts.plusJakartaSans(
                    color: Colors.red,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
        SizedBox(height: 4.h),
        CustomTextFormField(
          controller: controller,
          validator: validator,
          textInputType: keyboardType,
          readOnly: false,
          style: GoogleFonts.plusJakartaSans(
            color: theme.colorScheme.onSurface,
          ),
          fillColor: theme.colorScheme.onTertiary,
          overrideValidator: true,
          borderRadius: 15.r,
          maxLines: 1,
          decoration: InputDecoration(
            hintText: 'Enter $label',
            hintStyle: GoogleFonts.outfit(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: theme.colorScheme.onTertiary,
            contentPadding: EdgeInsets.symmetric(
              vertical: 15.h,
              horizontal: 20.w,
            ),
          ),
        ),
      ],
    );
  }

  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }
    // Regular expression for email validation
    final emailRegExp = RegExp(r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$');
    if (!emailRegExp.hasMatch(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  }
}
