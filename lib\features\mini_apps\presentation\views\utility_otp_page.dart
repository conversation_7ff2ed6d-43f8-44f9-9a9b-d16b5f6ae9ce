import 'dart:async';

import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_toast.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/top_up/presentations/views/top_up_success_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pinput/pinput.dart';
import 'package:flutter/services.dart';

class UtilityConfirmOtpScreen extends StatefulWidget {
  const UtilityConfirmOtpScreen({
    required this.operatorCode,
    required this.totalAmount,
    required this.phoneNumber,
    super.key,
  });

  final String operatorCode;
  final String totalAmount;
  final String phoneNumber;

  @override
  State<UtilityConfirmOtpScreen> createState() => _TopUpConfirmOtpScreenState();
}

class _TopUpConfirmOtpScreenState extends State<UtilityConfirmOtpScreen> {
  final _otpController = TextEditingController();
  bool _isLoading = false;
  Timer? _timer;
  int _timeLeft = 59;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft == 0) {
        timer.cancel();
      } else {
        setState(() => _timeLeft--);
      }
    });
  }

  void _handleResendOtp() {
    if (_timeLeft > 0) return;

    setState(() => _timeLeft = 59);
    startTimer();
  }

  Future<void> _handleVerify() async {
    if (_otpController.text.length != 6) {
      CustomToastification(
        context,
        message: 'Please enter a valid 6-digit OTP code',
      );
      return;
    }

    setState(() => _isLoading = true);

    await Future.delayed(const Duration(seconds: 3));
    setState(() => _isLoading = false);
    // Navigator.of(context).push(
    //   MaterialPageRoute(
    //     builder: (context) => TopUpSuccessScreen(
    //       operatorCode: widget.operatorCode,
    //       phoneNumber: widget.phoneNumber,
    //       totalAmount: widget.totalAmount,

    //       // operatorName: 'Safaricom',
    //       // operatorLogo: MediaRes.safaricomLogo,
    //       // phoneNumber: '23332323',
    //     ),
    //   ),
    // );
/*
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => TopUpConfirmPinBottomSheet(
        onPinSubmitted: (pin) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const TopUpSuccessScreen(
                  // operatorName: 'Safaricom',
                  // operatorLogo: MediaRes.safaricomLogo,
                  // phoneNumber: '23332323',
                  ),
            ),
          );
        },
        onBiometricAuth: () async {
          // Biometric implementation,
        },
        isLoading: false,
        hasBiometricSupport: true,
        preferredBiometric: BiometricType.fingerprint,
      ),
    );
    */
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Confirm Top-up',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          children: [
            const CustomPageHeader(
              pageTitle: 'Confirm Top-up',
              description:
                  'Review the amount and details, then confirm your mobile top-up.',
            ),
            SizedBox(height: 32.h),
            Pinput(
              length: 6,
              controller: _otpController,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              defaultPinTheme: PinTheme(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                textStyle: GoogleFonts.outfit(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: 24.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Didn't receive code? ",
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    color: const Color(0xFFAAAAAA),
                  ),
                ),
                TextButton(
                  onPressed: _timeLeft == 0 ? _handleResendOtp : null,
                  child: Text(
                    _timeLeft > 0 ? '$_timeLeft s' : 'Resend OTP',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const Spacer(),
            CustomButton(
              text: _isLoading ? 'Confriming...' : 'Confrim',
              onPressed: _isLoading ? null : _handleVerify,
              options: CustomButtonOptions(
                height: 56.h,
                color: Theme.of(context).primaryColor,
                textStyle: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                borderRadius: BorderRadius.circular(32.r),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _otpController.dispose();
    super.dispose();
  }
}
