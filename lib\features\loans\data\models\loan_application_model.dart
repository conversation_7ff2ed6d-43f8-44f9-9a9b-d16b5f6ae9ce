import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application.dart';

/// Model for unified loan application
class LoanApplicationModel extends LoanApplication {
  const LoanApplicationModel({
    required super.id,
    required super.memberId,
    required super.loanDetails,
    required super.paymentDetails,
    required super.productDetails,
    required super.dates,
    required super.type,
  });

  factory LoanApplicationModel.fromJson(
      Map<String, dynamic> json, LoanApplicationType type) {
    return LoanApplicationModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      memberId: AppMapper.safeString(json['memberId']),
      loanDetails: LoanDetailsModel.fromJson(
        json['loanDetails'] as Map<String, dynamic>,
      ),
      paymentDetails: PaymentDetailsModel.fromJson(
        json['paymentDetails'] as Map<String, dynamic>,
      ),
      productDetails: ProductDetailsModel.fromJson(
        json['productDetails'] as Map<String, dynamic>,
      ),
      dates: DatesModel.fromJson(
        json['dates'] as Map<String, dynamic>,
      ),
      type: type,
    );
  }

  /// Factory for car loan application
  factory LoanApplicationModel.car(Map<String, dynamic> json) {
    return LoanApplicationModel.fromJson(json, LoanApplicationType.car);
  }

  /// Factory for mortgage loan application
  factory LoanApplicationModel.mortgage(Map<String, dynamic> json) {
    return LoanApplicationModel.fromJson(json, LoanApplicationType.mortgage);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'memberId': memberId,
      'loanDetails': (loanDetails as LoanDetailsModel).toJson(),
      'paymentDetails': (paymentDetails as PaymentDetailsModel).toJson(),
      'productDetails': (productDetails as ProductDetailsModel).toJson(),
      'dates': (dates as DatesModel).toJson(),
      'type': type.name,
    };
  }
}

/// Model for loan details in application
class LoanDetailsModel extends LoanDetails {
  const LoanDetailsModel({
    required super.id,
    required super.bankId,
    required super.loanType,
    required super.status,
    required super.applicationFee,
    required super.facilitationRate,
    required super.penaltyStructure,
    required super.gracePeriodInDays,
    required super.loanPeriod,
    required super.amortizationType,
  });

  factory LoanDetailsModel.fromJson(Map<String, dynamic> json) {
    return LoanDetailsModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      bankId: AppMapper.safeString(json['bankId']),
      loanType: AppMapper.safeString(json['loanType']),
      status: AppMapper.safeString(json['status']),
      applicationFee: json['applicationFee'] as num,
      facilitationRate: json['facilitationRate'] as num,
      penaltyStructure: json['penaltyStructure'] as Map<String, dynamic>? ?? {},
      gracePeriodInDays: AppMapper.safeInt(json['gracePeriodInDays']),
      loanPeriod: AppMapper.safeInt(json['loanPeriod']),
      amortizationType: AppMapper.safeString(json['amortizationType']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bankId': bankId,
      'loanType': loanType,
      'status': status,
      'applicationFee': applicationFee,
      'facilitationRate': facilitationRate,
      'penaltyStructure': penaltyStructure,
      'gracePeriodInDays': gracePeriodInDays,
      'loanPeriod': loanPeriod,
      'amortizationType': amortizationType,
    };
  }
}

/// Model for payment details in application
class PaymentDetailsModel extends PaymentDetails {
  const PaymentDetailsModel({
    required super.upfrontPaymentAmount,
    required super.upfrontPaymentPercentage,
    required super.upfrontFacilitationFee,
    required super.loanAmount,
    required super.monthlyPayment,
    required super.monthlyFacilitationFee,
    required super.totalMonthlyPayment,
    required super.totalPayment,
    required super.totalInterest,
    required super.interestRate,
    required super.isAutoRepay,
  });

  factory PaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return PaymentDetailsModel(
      upfrontPaymentAmount: AppMapper.safeDouble(json['upfrontPaymentAmount']),
      upfrontPaymentPercentage:
          AppMapper.safeString(json['upfrontPaymentPercentage']),
      upfrontFacilitationFee:
          AppMapper.safeDouble(json['upfrontFacilitationFee']),
      loanAmount: AppMapper.safeDouble(json['loanAmount']),
      monthlyPayment: AppMapper.safeDouble(json['monthlyPayment']),
      monthlyFacilitationFee:
          AppMapper.safeDouble(json['monthlyFacilitationFee']),
      totalMonthlyPayment: AppMapper.safeDouble(json['totalMonthlyPayment']),
      totalPayment: AppMapper.safeString(json['totalPayment']),
      totalInterest: AppMapper.safeString(json['totalInterest']),
      interestRate: AppMapper.safeDouble(json['interestRate']),
      isAutoRepay: json['isAutoRepay'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'upfrontPaymentAmount': upfrontPaymentAmount,
      'upfrontPaymentPercentage': upfrontPaymentPercentage,
      'upfrontFacilitationFee': upfrontFacilitationFee,
      'loanAmount': loanAmount,
      'monthlyPayment': monthlyPayment,
      'monthlyFacilitationFee': monthlyFacilitationFee,
      'totalMonthlyPayment': totalMonthlyPayment,
      'totalPayment': totalPayment,
      'totalInterest': totalInterest,
      'interestRate': interestRate,
      'isAutoRepay': isAutoRepay,
    };
  }
}

/// Model for product details in application
class ProductDetailsModel extends ProductDetails {
  const ProductDetailsModel({
    required super.id,
    required super.name,
    required super.type,
    required super.price,
    required super.images,
    required super.specifications,
  });

  factory ProductDetailsModel.fromJson(Map<String, dynamic> json) {
    return ProductDetailsModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      name: AppMapper.safeString(json['name']),
      type: AppMapper.safeString(json['type']),
      price: AppMapper.safeString(json['price']),
      images: (json['images'] as List<dynamic>?)
              ?.map((img) => img.toString())
              .toList() ??
          [],
      specifications: json['specifications'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'price': price,
      'images': images,
      'specifications': specifications,
    };
  }
}

/// Model for dates in application
class DatesModel extends Dates {
  const DatesModel({
    required super.startDate,
    required super.endDate,
    required super.nextPaymentDate,
  });

  factory DatesModel.fromJson(Map<String, dynamic> json) {
    return DatesModel(
      startDate: DateTime.tryParse(json['startDate']?.toString() ?? '') ??
          DateTime.now(),
      endDate: DateTime.tryParse(json['endDate']?.toString() ?? '') ??
          DateTime.now(),
      nextPaymentDate:
          DateTime.tryParse(json['nextPaymentDate']?.toString() ?? '') ??
              DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'nextPaymentDate': nextPaymentDate.toIso8601String(),
    };
  }
}
