import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TopUpMenuCards extends StatelessWidget {
  const TopUpMenuCards({
    required this.operatorName,
    required this.operatorLogo,
    super.key,
    this.onTap,
  });

  final String operatorName;
  final String operatorLogo;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.h, 16.h),
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x0A000000),
                    blurRadius: 24,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomCachedImage(url:operatorLogo, height: 90, boxFit: BoxFit.contain,),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        operatorName,
                        textAlign: TextAlign.center,
                        style: GoogleFonts.outfit(
                          color: Colors.black,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      SizedBox(
                        width: double.infinity,
                        child: SizedBox(
                          width: double.infinity,
                          child: Text(
                            // 
                            "Top up $operatorName registered SIM cards",
                            style: GoogleFonts.outfit(
                              color: Colors.black.withOpacity(0.4),
                              fontSize: 12.sp,
                              letterSpacing: 0,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
