import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:equatable/equatable.dart';

class VerifyTransactionOtp extends UsecaseWithParams<bool, VerifyOtpParams> {
  const VerifyTransactionOtp(this._repository);
  final TransactionRepository _repository;

  @override
  ResultFuture<bool> call(VerifyOtpParams params) async {
    return _repository.verifyOtp(
      billRefNo: params.billRefNo,
      otpFor: params.otpFor,
      otpCode: params.otpCode,
    );
  }
}

class VerifyOtpParams extends Equatable {
  const VerifyOtpParams({
    required this.billRefNo,
    required this.otpFor,
    required this.otpCode,
  });
  final String billRefNo;
  final String otpFor;
  final int otpCode;

  @override
  List<Object?> get props => [billRefNo, otpFor, otpCode];
}
