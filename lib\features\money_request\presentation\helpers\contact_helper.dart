import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:get/get.dart';

class ContactHelper {
  static bool checkInContact(
    String phoneNumber,
    void Function(MemberLookupEntity, String, String, String, bool)
        onContactFound,
  ) {
    var returnData = false;
    final contact = Get.find<ContactSyncService>();
    contact.filterWithPhone(phoneNumber);

    if (contact.searchContacts.isNotEmpty) {
      final contactData = contact.searchContacts.first;

      final memberInfo = MemberLookupEntity(
        id: contactData.id,
        avatar: contactData.avatar,
        lastName: contactData.lastName,
        firstName: contactData.firstName,
        middleName: contactData.middleName,
        phoneNumber: contactData.phoneNumber,
        emailAddress: contactData.email,
      );

      onContactFound(
        memberInfo,
        '${contactData.firstName} ${contactData.lastName}',
        contactData.phoneNumber,
        contactData.avatar,
        true,
      );

      returnData = true;
    }

    return returnData;
  }
}
