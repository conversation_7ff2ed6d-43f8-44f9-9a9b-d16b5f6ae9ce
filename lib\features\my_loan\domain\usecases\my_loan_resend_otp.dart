import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:equatable/equatable.dart';

class MyLoanResendOtpUsecase
    extends UsecaseWithParams<bool, ResendUpfrontOtpParams> {
  const MyLoanResendOtpUsecase(this._repository);
  final LoanRepaymentRepository _repository;

  @override
  ResultFuture<bool> call(ResendUpfrontOtpParams params) async {
    return _repository.resendOtp(
      billRefNo: params.billRefNo,
      otpFor: params.otpFor,
    );
  }
}

class ResendUpfrontOtpParams extends Equatable {
  const ResendUpfrontOtpParams({
    required this.billRefNo,
    required this.otpFor,
  });
  final String billRefNo;
  final String otpFor;

  @override
  List<Object?> get props => [billRefNo, otpFor];
}
