import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/apply_for_loan.dart';
import 'package:cbrs/features/loans/domain/usecases/confirm_loan_payment.dart';
import 'package:cbrs/features/loans/domain/usecases/generate_application_transaction.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:cbrs/features/loans/domain/usecases/loan_confirm_resend_otp_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/loan_confirm_with_otp_usecase.dart';
import 'package:cbrs/features/loans/domain/usecases/loan_confirm_with_pin_usecase.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_event.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_state.dart';

class LoanItemsBloc extends Bloc<LoanEvent, LoanState> {
  LoanItemsBloc({
    required GetPaginatedLoanItems getPaginatedLoanItems,
    required LoanConfirmWithOtpUsecase loanConfirmWithOtpUsecase,
    required LoanConfirmResendOtpUsecase loanConfirmResendOtpUsecase,
    required LoanConfirmWithPinUsecase loanConfirmWithPinUsecase,
    required ApplyForLoan applyForLoan,
    required GenerateApplicationTransaction generateApplicationTransaction,
    required ConfirmLoanPayment confirmLoanPayment,
  })  : _getPaginatedLoanItems = getPaginatedLoanItems,
        _loanConfirmWithOtpUsecase = loanConfirmWithOtpUsecase,
        _loanConfirmResendOtpUsecase = loanConfirmResendOtpUsecase,
        _loanConfirmWithPinUsecase = loanConfirmWithPinUsecase,
        _applyForLoan = applyForLoan,
        _generateApplicationTransaction = generateApplicationTransaction,
        _confirmLoanPayment = confirmLoanPayment,
        super(const LoanInitial()) {
    on<FetchLoanItemsEvent>(_onFetchLoanItems);
    on<LoadMoreLoanItemsEvent>(_onLoadMoreLoanItems);
    on<RefreshLoanItemsEvent>(_onRefreshLoanItems);
    on<FilterLoanItemsByCategoryEvent>(_onFilterLoanItemsByCategory);
    on<SearchLoanItemsEvent>(_onSearchLoanItems);
    on<ClearLoanItemsEvent>(_onClearLoanItems);
    on<ResetLoanStateEvent>(_onResetLoanState);

    on<ApplyLoanEvent>(_onApplyLoan);
    on<GenerateApplicationTransactionEvent>(_onGenerateApplicationTransaction);
    on<ConfirmLoanPaymentEvent>(_onConfirmLoanPayment);
    on<LoanConfirmWithOtpEvent>(_onConfirmWithOtp);
    on<LoanConfirmWithPinEvent>(_onConfirmWithPin);
    on<LoanResendOtpEvent>(_onResendOtp);
  }

  final GetPaginatedLoanItems _getPaginatedLoanItems;
  final LoanConfirmWithPinUsecase _loanConfirmWithPinUsecase;
  final LoanConfirmWithOtpUsecase _loanConfirmWithOtpUsecase;
  final LoanConfirmResendOtpUsecase _loanConfirmResendOtpUsecase;
  final ApplyForLoan _applyForLoan;
  final GenerateApplicationTransaction _generateApplicationTransaction;
  final ConfirmLoanPayment _confirmLoanPayment;

  /// Handle fetching loan items
  Future<void> _onFetchLoanItems(
    FetchLoanItemsEvent event,
    Emitter<LoanState> emit,
  ) async {
    if (event.isRefresh && state is LoanLoaded) {
      emit(LoanRefreshing(currentItems: (state as LoanLoaded).items));
    } else {
      emit(const LoanLoading());
    }

    final result = await _getPaginatedLoanItems(event.params);

    result.fold(
      (failure) => emit(LoanError(message: _mapFailureToMessage(failure))),
      (paginatedResult) {
        if (paginatedResult.items.isEmpty) {
          emit(const LoanEmpty());
        } else {
          emit(LoanLoaded(
            items: paginatedResult.items,
            currentPage: paginatedResult.currentPage,
            hasReachedMax: !paginatedResult.hasNextPage,
            totalItems: paginatedResult.totalItems,
          ));
        }
      },
    );
  }

  /// Handle loading more loan items (pagination)
  Future<void> _onLoadMoreLoanItems(
    LoadMoreLoanItemsEvent event,
    Emitter<LoanState> emit,
  ) async {
    final currentState = state;
    if (currentState is! LoanLoaded || currentState.hasReachedMax) {
      return;
    }

    emit(LoanLoadingMore(
      currentItems: currentState.items,
      currentPage: currentState.currentPage,
      hasReachedMax: currentState.hasReachedMax,
    ));

    final result = await _getPaginatedLoanItems(event.params);

    result.fold(
      (failure) => emit(LoanError(
        message: _mapFailureToMessage(failure),
        currentItems: currentState.items,
        isLoadMoreError: true,
      )),
      (paginatedResult) {
        final allItems = List<LoanItem>.from(currentState.items)
          ..addAll(paginatedResult.items);

        emit(LoanLoaded(
          items: allItems,
          currentPage: paginatedResult.currentPage,
          hasReachedMax: !paginatedResult.hasNextPage,
          totalItems: paginatedResult.totalItems,
          isFiltered: currentState.isFiltered,
          currentCategoryId: currentState.currentCategoryId,
          searchQuery: currentState.searchQuery,
        ));
      },
    );
  }

  /// Handle refreshing loan items
  Future<void> _onRefreshLoanItems(
    RefreshLoanItemsEvent event,
    Emitter<LoanState> emit,
  ) async {
    add(FetchLoanItemsEvent(params: event.params, isRefresh: true));
  }

  /// Handle filtering loan items by category
  Future<void> _onFilterLoanItemsByCategory(
    FilterLoanItemsByCategoryEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const LoanLoading());

    final params = GetPaginatedLoanItemsParams(
      loanType: event.loanType,
      page: event.page,
      limit: event.limit,
      categoryId: event.categoryId.isEmpty ? null : event.categoryId,
    );

    final result = await _getPaginatedLoanItems(params);

    result.fold(
      (failure) => emit(LoanError(message: _mapFailureToMessage(failure))),
      (paginatedResult) {
        if (paginatedResult.items.isEmpty) {
          emit(const LoanEmpty(
            message: 'No items found in this category',
            isFiltered: true,
          ));
        } else {
          emit(LoanLoaded(
            items: paginatedResult.items,
            currentPage: paginatedResult.currentPage,
            totalItems: paginatedResult.totalItems,
            hasReachedMax: !paginatedResult.hasNextPage,
            isFiltered: event.categoryId.isNotEmpty,
            currentCategoryId:
                event.categoryId.isEmpty ? null : event.categoryId,
          ));
        }
      },
    );
  }

  /// Handle searching loan items
  Future<void> _onSearchLoanItems(
    SearchLoanItemsEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const LoanLoading());

    final params = GetPaginatedLoanItemsParams(
      loanType: event.loanType,
      page: event.page,
      limit: event.limit,
      categoryId: event.categoryId,
      searchQuery: event.query,
    );

    final result = await _getPaginatedLoanItems(params);

    result.fold(
      (failure) => emit(LoanError(message: _mapFailureToMessage(failure))),
      (paginatedResult) {
        if (paginatedResult.items.isEmpty) {
          emit(const LoanEmpty(
            message: 'No items found for your search',
            isFiltered: true,
          ));
        } else {
          emit(LoanLoaded(
            items: paginatedResult.items,
            currentPage: paginatedResult.currentPage,
            hasReachedMax: !paginatedResult.hasNextPage,
            totalItems: paginatedResult.totalItems,
            isFiltered: true,
            currentCategoryId: event.categoryId,
            searchQuery: event.query,
          ));
        }
      },
    );
  }

  /// Handle clearing loan items
  void _onClearLoanItems(
    ClearLoanItemsEvent event,
    Emitter<LoanState> emit,
  ) {
    emit(const LoanEmpty());
  }

  /// Handle resetting loan state
  void _onResetLoanState(
    ResetLoanStateEvent event,
    Emitter<LoanState> emit,
  ) {
    emit(const LoanInitial());
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is CacheFailure) {
      return 'Failed to load cached data';
    } else if (failure is NetworkFailure) {
      return 'Please check your internet connection';
    }
    return 'An unexpected error occurred';
  }

  // ============================================================================
  // LOAN APPLICATION EVENT HANDLERS
  // ============================================================================

  /// Handle applying for a loan
  Future<void> _onApplyLoan(
    ApplyLoanEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const LoanApplicationLoading());

    final result = await _applyForLoan(
      ApplyForLoanParams(
        loanId: event.loanId,
        productId: event.productId,
        upfrontPaymentPercentage: event.upfrontPaymentPercentage,
        loanType: event.loanType,
        isAutoRepay: event.isAutoRepay,
      ),
    );

    result.fold(
      (failure) => emit(LoanError(message: _mapFailureToMessage(failure))),
      (application) => emit(LoanApplicationSuccess(application: application)),
    );
  }

  /// Handle generating application fee transaction
  Future<void> _onGenerateApplicationTransaction(
    GenerateApplicationTransactionEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const GeneratingApplicationTransaction());

    final result = await _generateApplicationTransaction(
      GenerateApplicationTransactionParams(
        loanApplicationId: event.loanApplicationId,
        loanType: event.loanType,
      ),
    );

    result.fold(
      (failure) => emit(
        ApplicationTransactionError(message: _mapFailureToMessage(failure)),
      ),
      (transaction) => emit(
        ApplicationTransactionGenerated(transaction: transaction),
      ),
    );
  }

  /// Handle confirming loan payment with PIN
  Future<void> _onConfirmLoanPayment(
    ConfirmLoanPaymentEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const ConfirmingLoanPayment());

    final result = await _confirmLoanPayment(
      ConfirmLoanPaymentParams(
        transactionType: event.transactionType,
        billRefNo: event.billRefNo,
        pin: event.pin,
      ),
    );

    result.fold(
      (failure) => emit(
        LoanPaymentConfirmationError(message: _mapFailureToMessage(failure)),
      ),
      (confirmation) => emit(
        LoanPaymentConfirmed(confirmation: confirmation),
      ),
    );
  }

  /// Handle confirming loan application with PIN
  Future<void> _onConfirmWithPin(
    LoanConfirmWithPinEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const LoanApplicationLoading());

    final result = await _loanConfirmWithPinUsecase(
      LoanConfirPinTransferParams(
        billRefNo: event.billRefNo,
        pin: event.pin,
        transactionType: event.transactionType,
      ),
    );

    result.fold(
      (failure) => emit(LoanError(message: _mapFailureToMessage(failure))),
      (success) => emit(const LoanApplicationInitial()),
    );
  }

  /// Handle confirming loan application with OTP
  Future<void> _onConfirmWithOtp(
    LoanConfirmWithOtpEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const LoanApplicationLoading());

    final result = await _loanConfirmWithOtpUsecase(
      LoanConfirOtpTransferParams(
        billRefNo: event.billRefNo,
        otpCode: event.otpCode,
        transactionType: event.transactionType,
      ),
    );

    result.fold(
      (failure) => emit(LoanError(message: _mapFailureToMessage(failure))),
      (success) => emit(const LoanApplicationInitial()),
    );
  }

  /// Handle resending OTP for loan application
  Future<void> _onResendOtp(
    LoanResendOtpEvent event,
    Emitter<LoanState> emit,
  ) async {
    emit(const LoanApplicationLoading());

    final result = await _loanConfirmResendOtpUsecase(
      LoanConfirResendTransferParams(
        billRefNo: event.billRefNo,
        otpFor: event.billRefNo,
      ),
    );

    result.fold(
      (failure) => emit(LoanError(message: _mapFailureToMessage(failure))),
      (success) => emit(const LoanApplicationInitial()),
    );
  }
}
