import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_confirm_pin_response_entity.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

class LoanConfirmWithPinUsecase extends UsecaseWithParams<
    LoanConfirmPinResponseEntity, LoanConfirPinTransferParams> {
  const LoanConfirmWithPinUsecase(this._repository);
  final LoanRepository _repository;

  @override
  ResultFuture<LoanConfirmPinResponseEntity> call(
    LoanConfirPinTransferParams params,
  ) async {
    return _repository.confirmWithPin(
      pin: params.pin,
      billRefNo: params.billRefNo,
      transactionType: params.transactionType,
    );
  }
}

class LoanConfirPinTransferParams extends Equatable {
  const LoanConfirPinTransferParams({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
    this.otp,
  });

  final String pin;
  final String billRefNo;
  final String transactionType;
  final String? otp;

  @override
  List<Object?> get props => [pin, billRefNo, transactionType, otp];
}
