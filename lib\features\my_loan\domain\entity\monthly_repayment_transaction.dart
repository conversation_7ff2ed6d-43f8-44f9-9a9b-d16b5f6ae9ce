import 'package:equatable/equatable.dart';


// this class is for success repayment response
class MonthlyRepaymentTransactionEntity extends Equatable {
  final String id;
  final String loanId;
  final String loanPaymentId;
  final int installmentNumber;
  final String principalAmount;
  final String interestAmount;
  final String totalAmount;
  final String paidAmount;
  final String remainingAmount;
  final String dueDate;
  final String status;
  final PenaltyConfigEntity penaltyConfig;
  final String referenceNumber;
  final String? sessionId;
  final String? paymentMethod;
  final String? redirectURL;
  final String facilitationFee;
  final bool isInPenalty;
  final String penaltyAmount;
  final String penaltyStartDate;
  final String lastPaidDate;
  final String? lastPenaltyCalculationDate;
  final String totalPenaltyPaid;
  final String? invoiceURL;
  final String createdAt;
  final String updatedAt;

  const MonthlyRepaymentTransactionEntity({
    required this.id,
    required this.loanId,
    required this.loanPaymentId,
    required this.installmentNumber,
    required this.principalAmount,
    required this.interestAmount,
    required this.totalAmount,
    required this.paidAmount,
    required this.remainingAmount,
    required this.dueDate,
    required this.status,
    required this.penaltyConfig,
    required this.referenceNumber,
    this.sessionId,
    this.paymentMethod,
    this.redirectURL,
    required this.facilitationFee,
    required this.isInPenalty,
    required this.penaltyAmount,
    required this.penaltyStartDate,
    required this.lastPaidDate,
    this.lastPenaltyCalculationDate,
    required this.totalPenaltyPaid,
    this.invoiceURL,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        loanId,
        loanPaymentId,
        installmentNumber,
        principalAmount,
        interestAmount,
        totalAmount,
        paidAmount,
        remainingAmount,
        dueDate,
        status,
        penaltyConfig,
        referenceNumber,
        sessionId,
        paymentMethod,
        redirectURL,
        facilitationFee,
        isInPenalty,
        penaltyAmount,
        penaltyStartDate,
        lastPaidDate,
        lastPenaltyCalculationDate,
        totalPenaltyPaid,
        invoiceURL,
        createdAt,
        updatedAt,
      ];
}



class PenaltyConfigEntity extends Equatable {
  final double originalBalance;
  final int gracePeriodInDays;

  const PenaltyConfigEntity({
    required this.originalBalance,
    required this.gracePeriodInDays,
  });

  @override
  List<Object?> get props => [
        originalBalance,
        gracePeriodInDays,
      ];
}



