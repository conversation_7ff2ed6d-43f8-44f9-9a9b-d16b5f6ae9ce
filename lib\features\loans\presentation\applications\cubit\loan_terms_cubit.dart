import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_state.dart';

/// Cubit for managing loan terms and conditions
class LoanTermsCubit extends Cubit<LoanTermsState> {
  LoanTermsCubit({
    required LoanRepository loanRepository,
  })  : _loanRepository = loanRepository,
        super(const LoanTermsInitial());

  final LoanRepository _loanRepository;

  /// Fetch loan terms from API
  Future<void> fetchLoanTerms({
    required String bankId,
    required String loanType,
  }) async {
    emit(const LoanTermsLoading());

    try {
      debugPrint('🔍 Fetching loan terms for bank: $bankId, type: $loanType');

      // Convert string loanType to LoanItemType enum
      final loanItemType = loanType.toLowerCase() == 'car' 
          ? LoanItemType.car 
          : LoanItemType.house;

      final result = await _loanRepository.getLoanTerms(
        bankId: bankId,
        loanType: loanItemType,
      );

      result.fold(
        (failure) {
          debugPrint('❌ Failed to fetch loan terms: ${failure.message}');
          emit(LoanTermsError(
            message: _mapFailureToMessage(failure),
            bankId: bankId,
            loanType: loanType,
          ));
        },
        (content) {
          debugPrint('✅ Successfully fetched loan terms');
          emit(LoanTermsLoaded(
            content: content,
            bankId: bankId,
            loanType: loanType,
          ));
        },
      );
    } catch (e) {
      debugPrint('❌ Unexpected error fetching loan terms: $e');
      emit(LoanTermsError(
        message: 'An unexpected error occurred while fetching terms',
        bankId: bankId,
        loanType: loanType,
      ));
    }
  }

  /// Refresh loan terms
  Future<void> refreshLoanTerms({
    required String bankId,
    required String loanType,
  }) async {
    await fetchLoanTerms(bankId: bankId, loanType: loanType);
  }

  /// Clear terms and reset to initial state
  void clearTerms() {
    emit(const LoanTermsInitial());
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is CacheFailure) {
      return 'Failed to load cached terms';
    } else if (failure is NetworkFailure) {
      return 'Please check your internet connection and try again';
    }
    return 'An unexpected error occurred';
  }
}
