import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

/// Use case for calculating loan payment information
class CalculateLoanPayment extends UsecaseWithParams<Map<String, dynamic>,
    CalculateLoanPaymentParams> {
  const CalculateLoanPayment(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<Map<String, dynamic>> call(
    CalculateLoanPaymentParams params,
  ) async {
    return repository.calculateLoanPayment(
      bankId: params.bankId,
      loanProductId: params.loanProductId,
      upfrontPaymentPercentage: params.upfrontPaymentPercentage,
      productId: params.productId,
      interestRate: params.interestRate,
      loanType: params.loanType,
    );
  }
}

/// Parameters for calculating loan payment
class CalculateLoanPaymentParams extends Equatable {
  const CalculateLoanPaymentParams({
    required this.bankId,
    required this.loanProductId,
    required this.upfrontPaymentPercentage,
    required this.productId,
    required this.interestRate,
    required this.loanType,
  });

  final String bankId;
  final String loanProductId;
  final String upfrontPaymentPercentage;
  final String productId;
  final double interestRate;
  final LoanItemType loanType;

  @override
  List<Object?> get props => [
        bankId,
        loanProductId,
        upfrontPaymentPercentage,
        productId,
        interestRate,
        loanType,
      ];
}
