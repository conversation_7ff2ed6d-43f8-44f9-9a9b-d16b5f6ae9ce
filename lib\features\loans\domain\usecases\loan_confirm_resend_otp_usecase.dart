import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

class LoanConfirmResendOtpUsecase
    extends UsecaseWithParams<String, LoanConfirResendTransferParams> {
  const LoanConfirmResendOtpUsecase(this._repository);
  final LoanRepository _repository;

  @override
  ResultFuture<String> call(LoanConfirResendTransferParams params) async {
    return _repository.resendOtp(
        otpFor: params.otpFor, billRefNo: params.billRefNo);
  }
}

class LoanConfirResendTransferParams extends Equatable {
  const LoanConfirResendTransferParams({
    required this.otpFor,
    required this.billRefNo,
  });

  final String otpFor;
  final String billRefNo;

  @override
  List<Object?> get props => [
        otpFor,
        billRefNo,
      ];
}
