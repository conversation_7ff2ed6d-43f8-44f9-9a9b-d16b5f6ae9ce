import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_entity.dart';

class MonthlyRepaymentModel extends MonthlyRepaymentEntity {
  const MonthlyRepaymentModel({
    required super.elstRef,
    required super.billRefNo,
    required super.beneficiaryId,
    required super.beneficiaryName,
    required super.senderName,
    required super.senderPhone,
    required super.bankName,
    required super.bankCode,
    required super.senderId,
    required super.transactionOwner,
    required super.authorizationType,
    required super.status,
    required super.vat,
    required super.serviceCharge,
    required super.originalCurrency,
    required super.billAmount,
    required super.createdAt,
    required super.lastModifiedAt,
    required super.transactionType,
  });

  factory MonthlyRepaymentModel.fromJson(Map<String, dynamic> json) {
    return MonthlyRepaymentModel(
      elstRef: AppMapper.safeString(json['ELSTRef']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      bankName: AppMapper.safeString(json['bankName']),
      bankCode: AppMapper.safeString(json['bankCode']),
      senderId: AppMapper.safeString(json['senderId']),
      transactionOwner: AppMapper.safeString(json['transactionOwner']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      status: AppMapper.safeString(json['status']),
      vat: AppMapper.safeDouble(json['VAT']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      createdAt: AppMapper.safeString(json['createdAt']),
      lastModifiedAt: AppMapper.safeString(json['lastModifiedAt']),
      transactionType: AppMapper.safeString(json['transactionType']),
    );
  }
}
