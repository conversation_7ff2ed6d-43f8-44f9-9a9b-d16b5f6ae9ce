import 'package:cbrs/core/common/widgets/custom_menu_screen_cards.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/build_car_details_item.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';

class PendingLoanInfo extends StatelessWidget {
  const PendingLoanInfo({required this.pendedLoan, super.key});
  final LoanRepaymentDataEntity pendedLoan;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Loan Payment Information',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(
            height: 2,
          ),
          CustomBuildText(
            text: 'Details information about car loan Application',
            color: Colors.black.withOpacity(0.3),
            fontSize: 14.sp,
          ),
          SizedBox(
            height: 12.h,
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 12.h),
            decoration: BoxDecoration(
              color: const Color(0xFF2C2B34).withOpacity(0.04),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                _buildLoanInfoItem(
                  title: 'Bank Name:',
                  value: '${pendedLoan.bankName}',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Down Payment:',
                  value: '${pendedLoan.loanInfo?.downPayment?.percentage}%',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Upfront Payment Amount:',
                  value: '\$${pendedLoan.loanInfo?.downPayment?.amount}',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Payment Term:',
                  value: '1 Month',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Loan Period:',
                  value:
                      '${((pendedLoan.loanInfo?.loanPeriod ?? 0) / 12).toStringAsFixed(0)} years',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Payment Amount:',
                  value: '\$${pendedLoan.loanInfo?.monthlyRepayment}/Month',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Interest rate:',
                  value: '${pendedLoan.loanInfo?.interestRate}%',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Daily Penalty Fee:',
                  value: '${pendedLoan.loanInfo?.dailyPenaltyFee}',
                  showDivider: true,
                ),
                _buildLoanInfoItem(
                  title: 'Facilitation Fee:',
                  value: '${pendedLoan.loanInfo?.facilitationFee}',
                  showDivider: false,
                ),
              ],
            ),
          ),
          CustomMenuScreenCards(
            containerIcon:          MediaRes.listIcon,
            iconColor: Theme.of(context).primaryColor,
            iconHeight: 32,
            iconwidth: 32,
            
              // 'assets/images/empty_transaction_screen_img.png',
            title: 'Applications Payment',
            description:
                'Details of the applications your loan application payment.',
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildLoanInfoItem({
    required String title,
    required String value,
    required bool showDivider,
  }) {
    return BuildCarDetailsItem(
      title: title,
      value: value,
      showDivider: showDivider,
    );
  }
}
