import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/entities/loan_payment_info.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

/// Use case for getting loan payment information
class GetLoanPaymentInfo
    extends UsecaseWithParams<LoanPaymentInfo, GetLoanPaymentInfoParams> {
  const GetLoanPaymentInfo(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<LoanPaymentInfo> call(GetLoanPaymentInfoParams params) async {
    return repository.getLoanPaymentInfo(
      productId: params.productId,
      bankId: params.bankId,
      upfrontPaymentPercentage: params.upfrontPaymentPercentage,
      loanType: params.loanType,
    );
  }
}

/// Parameters for getting loan payment info
class GetLoanPaymentInfoParams extends Equatable {
  const GetLoanPaymentInfoParams({
    required this.productId,
    required this.bankId,
    required this.upfrontPaymentPercentage,
    required this.loanType,
  });

  final String productId;
  final String bankId;
  final String upfrontPaymentPercentage;
  final LoanItemType loanType;

  @override
  List<Object?> get props =>
      [productId, bankId, upfrontPaymentPercentage, loanType];
}
