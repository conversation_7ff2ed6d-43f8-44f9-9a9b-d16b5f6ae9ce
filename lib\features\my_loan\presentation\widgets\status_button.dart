import 'package:cbrs/features/my_loan/presentation/widgets/_rounded_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class StatusButton extends StatelessWidget {
  const StatusButton({
    required this.status,
    super.key,
    this.fontSize = 12.0,
    this.fontWeight = FontWeight.w500,
  });
  final String status;
  final double fontSize;
  final FontWeight fontWeight;

  @override
  Widget build(BuildContext context) {
    final displayText = status[0].toUpperCase() + status.substring(1);
    Color textColor;
    Color bgColor;

    if (status.toLowerCase() == 'active') {
      textColor = const Color(0xFF00A63A);
      bgColor = const Color(0xFF00A63A);
    } else if (status.toLowerCase() == 'pending') {
      textColor = const Color(0xFFDD9000);
      bgColor = const Color(0xFFFFF1C2);
    } else if (status.toLowerCase() == 'rejected') {
      textColor = const Color(0xFFFF0000);
      bgColor = const Color(0xFFFFEEEE);
    } else if (status.toLowerCase() == 'preparing') {
      textColor = const Color(0xFFD355E9);
      bgColor = const Color(0xFFF1E3F9);
    } else if (status.toLowerCase() == 'approved') {
      textColor = const Color(0xFF00A1B8);
      bgColor = const Color(0xFFCFF9FF);
    } else {
      textColor = const Color(0xFF00A1B8);
      bgColor = const Color(0xFFCFF9FF);
    }

    return RoundedButton(
      text: displayText,
      textColor: textColor,
      bgColor: bgColor,
      fontWeight: fontWeight,
      fontSize: fontSize.sp,
      // padding: EdgeInsets.symmetric(horizontal: 10, vertical: 2),
    );
  }
}
