import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildPaymentOptionsRow extends StatelessWidget {
  const BuildPaymentOptionsRow({
    required this.onTap,
    required this.paymentTerm,
    super.key,
  });

  final VoidCallback onTap;
  final int paymentTerm;
  @override
  // Widget build(BuildContext context) {
  //   return const Placeholder();
  // }

  Widget build(
    BuildContext context,
    // LoanRepaymentDataEntity loan,
  ) {
    return GestureDetector(
      onTap: onTap,
      //  () {
      //     showPaymentOptions(context, loan, isPaymentTermOption: true);
      // },
      child: Container(
        // height: 56.h,
        margin: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0.w),
        padding: EdgeInsets.all(16.h),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: const Color(0x0A2C2B34),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Container(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 10.w),
                    CustomBuildText(
                      text: paymentTerm == 1
                          ? '$paymentTerm Month'
                          : '$paymentTerm months',
                      color: Theme.of(context).primaryColor,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 10),
            Container(
              clipBehavior: Clip.antiAlias,
              decoration: const BoxDecoration(),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 16.h,
                color: Theme.of(context).primaryColor.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
