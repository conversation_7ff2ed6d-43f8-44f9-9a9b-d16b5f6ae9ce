import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_category.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

class GetLoanCategories
    extends UsecaseWithParams<List<LoanCategory>, GetLoanCategoriesParams> {
  GetLoanCategories(this._repository);

  final LoanRepository _repository;

  @override
  ResultFuture<List<LoanCategory>> call(
    GetLoanCategoriesParams params,
  ) async {
    try {
      final result = await _repository.getLoanCategories(
        page: params.page,
        limit: params.limit,
        loanType: params.loanType,
      );
      return result;
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

class GetLoanCategoriesParams extends Equatable {
  const GetLoanCategoriesParams({
    this.page = 1,
    this.limit = 10,
    this.loanType,
  });

  final int page;
  final int limit;
  final String? loanType;

  @override
  List<Object?> get props => [page, limit, loanType];
}
