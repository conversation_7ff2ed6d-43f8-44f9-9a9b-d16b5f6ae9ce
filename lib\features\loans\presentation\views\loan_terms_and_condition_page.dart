import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_bloc.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_event.dart';
import 'package:cbrs/features/loans/presentation/applications/bloc/loan_state.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_cubit.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_terms_state.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_terms_and_conditions_container.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class LoanTermsAndConditionsPage extends StatefulWidget {
  const LoanTermsAndConditionsPage({
    required this.bank,
    required this.loanItem,
    required this.selectedUpfrontPayment,
    super.key,
  });

  final LoanBank bank;
  final LoanItem loanItem;
  final String selectedUpfrontPayment;

  @override
  State<LoanTermsAndConditionsPage> createState() =>
      _LoanTermsAndConditionsPageState();
}

class _LoanTermsAndConditionsPageState
    extends State<LoanTermsAndConditionsPage> {
  @override
  void initState() {
    super.initState();
    _fetchLoanTerms();
  }

  void _fetchLoanTerms() {
    debugPrint(
      '🔍 Loan terms will be fetched by container for bank: ${widget.bank.id}',
    );
  }

  String appliedLoanPeriod = '';
  bool isLoanTermsFetched = false;
  bool _isBottomSheetShowing = false;
  bool _hasApplied = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Loan Terms & Conditions',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<LoanItemsBloc, LoanState>(
            listener: (context, state) {
              if (state is LoanApplicationSuccess) {
                _handleLoanApplicationSuccess(state.application);
              } else if (state is ApplicationTransactionGenerated) {
                _handleApplicationTransactionGenerated(state.transaction);
              } else if (state is LoanPaymentConfirmed) {
                _handleLoanPaymentConfirmed(state.confirmation);
              } else if (state is LoanError ||
                  state is ApplicationTransactionError ||
                  state is LoanPaymentConfirmationError) {
                // Reset flags on any error to allow retry
                setState(() {
                  _isBottomSheetShowing = false;
                  _hasApplied = false;
                });

                final message = state is LoanError
                    ? state.message
                    : state is ApplicationTransactionError
                        ? (state as ApplicationTransactionError).message
                        : (state as LoanPaymentConfirmationError).message;
                CustomToastification(
                  context,
                  message: message,
                );
              }
            },
          ),
          BlocListener<LoanTermsCubit, LoanTermsState>(
            listener: (context, state) {
              if (state is LoanTermsLoaded) {
                setState(() {
                  isLoanTermsFetched = true;
                });
              } else if (state is LoanTermsError) {
                setState(() {
                  isLoanTermsFetched = false;
                });
              }
            },
          ),
        ],
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const CustomPageHeader(
                      pageTitle: 'Our Terms & Condition',
                      description:
                          'Read our terms and conditions carefully before your '
                          'continue with your application.',
                    ),
                    SizedBox(height: 16.h),
                    _buildBankCard(context),
                    SizedBox(height: 12.h),
                    Expanded(
                      child: LoanTermsAndConditionsContainer(
                        bankId: widget.bank.id,
                        loanType: widget.loanItem.type == LoanItemType.car
                            ? 'car'
                            : 'mortgage',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            BlocConsumer<LoanItemsBloc, LoanState>(
              listener: (context, state) {
                if (state is LoanError) {
                  CustomToastification(
                    context,
                    message: state.message,
                  );
                }
              },
              builder: (context, state) {
                final isLoading = state is LoanApplicationLoading;
                final isProcessing = isLoading || _isBottomSheetShowing;
                final canApply =
                    isLoanTermsFetched && !isProcessing && !_hasApplied;

                String buttonText;
                if (_hasApplied) {
                  buttonText = 'Application Submitted';
                } else if (isProcessing) {
                  buttonText = 'Processing...';
                } else {
                  buttonText = 'Apply Now';
                }

                return Padding(
                  padding:
                      const EdgeInsets.only(bottom: 24, left: 16, right: 16),
                  child: CustomRoundedBtn(
                    btnText: buttonText,
                    onTap: canApply ? _handleApplyNow : null,
                    isLoading: isProcessing,
                    isBtnActive: canApply,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleApplyNow() {
    // Mark as applied to prevent multiple applications
    setState(() {
      _hasApplied = true;
    });

    final loanBloc = BlocProvider.of<LoanItemsBloc>(context);

    final upfrontPaymentPercentage = widget.selectedUpfrontPayment;

    loanBloc.add(
      ApplyLoanEvent(
        loanId: widget.bank.loans.first.id,
        productId: widget.loanItem.id,
        upfrontPaymentPercentage: upfrontPaymentPercentage,
        loanType: widget.loanItem.type,
      ),
    );
  }

  void _handleLoanApplicationSuccess(LoanApplication application) {
    final loanBloc = BlocProvider.of<LoanItemsBloc>(context);

    debugPrint('Generating application transaction for ID: ${application.id}');

    loanBloc.add(
      GenerateApplicationTransactionEvent(
        loanApplicationId: application.id,
        loanType: widget.loanItem.type,
      ),
    );
  }

  void _handleApplicationTransactionGenerated(dynamic transaction) {
    _showPaymentConfirmationBottomSheet(transaction);
  }

  void _handleLoanPaymentConfirmed(dynamic confirmation) {
    // Close any open bottom sheets first
    Navigator.of(context).popUntil((route) => route.isFirst);

    // Reset only the bottom sheet flag, keep _hasApplied as true
    // since payment was successful
    setState(() {
      _isBottomSheetShowing = false;
    });

    // Show success bottom sheet with loan payment details
    _showLoanPaymentSuccessBottomSheet(confirmation);
  }

  void _showPaymentConfirmationBottomSheet(dynamic transaction) {
    // Prevent multiple bottom sheets from being shown
    if (_isBottomSheetShowing) {
      debugPrint('⚠️ Bottom sheet already showing, ignoring request');
      return;
    }

    setState(() {
      _isBottomSheetShowing = true;
    });

    // Extract transaction details
    final billRefNo = transaction.billRefNo as String;
    final amount = transaction.billAmount.toString();

    final itemPrice = widget.loanItem.type == LoanItemType.car
        ? widget.loanItem.price
        : widget.loanItem.price;
    final upfrontPercentage =
        double.tryParse(widget.selectedUpfrontPayment) ?? 0;
    final upfrontAmount = itemPrice * (upfrontPercentage / 100);
    final loanAmount = itemPrice - upfrontAmount;
    final loanPeriod = widget.bank.loans.first.loanPeriod;

    // Create transaction bottom sheets manager
    final pinController = TextEditingController();
    final bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: TransactionType.loanRepayment,
      pinController: pinController,
      onPinSubmitted: (pin) => _confirmLoanPayment(
        transactionType: 'application_fee',
        billRefNo: billRefNo,
        pin: pin,
      ),
      onTransactionSuccess: (response) {
        // This won't be called for loan payments
        // Success is handled by LoanPaymentConfirmed state in BlocListener
      },
      onTransactionComplete: () {
        // Reset the bottom sheet flag when transaction is complete
        setState(() {
          _isBottomSheetShowing = false;
        });
      },
    );

    bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Loan Application Fee',
        'Loan Type': widget.loanItem.type == LoanItemType.car
            ? 'Car Loan'
            : 'Mortgage Loan',
        'Product Name': widget.loanItem.name,
        'Bank': widget.bank.name,
        'Upfront Payment': '${widget.selectedUpfrontPayment}%',
        'Application Fee': '$amount USD',
        'Bill Reference': billRefNo,
        'Status': 'Pending Payment',
      },
      requiresOtp: false,
      billRefNo: billRefNo,
      billAmount: (transaction.billAmount as num).toDouble(),
      totalAmount: (transaction.billAmount as num).toDouble(),
      originalCurrency: 'USD',
      confirmButtonText: 'Confirm Payment',
    );
  }

  void _confirmLoanPayment({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) {
    context.read<LoanItemsBloc>().add(
          ConfirmLoanPaymentEvent(
            transactionType: transactionType,
            billRefNo: billRefNo,
            pin: pin,
          ),
        );
  }

  void _showLoanPaymentSuccessBottomSheet(dynamic confirmation) {
    // Create a transaction bottom sheets manager for showing success
    final pinController = TextEditingController();
    final bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: TransactionType.loanRepayment,
      pinController: pinController,
      onPinSubmitted: (pin) {},
      onTransactionSuccess: (response) {},
      onTransactionComplete: () {},
    );

    // Extract loan payment confirmation details
    final applicationFeeTransaction = confirmation.applicationFeeTransaction;
    final loanAmount = confirmation.loanAmount ?? '0';
    final monthlyPayment = confirmation.monthlyPayment ?? '0';
    final totalPayment = confirmation.totalPayment ?? '0';
    final loanApplicationCode = confirmation.loanApplicationCode ?? '';

    // Show success bottom sheet with loan payment details
    bottomSheetsManager.showSuccessScreenBottomSheet(
      {
        'Transaction Type': 'Loan Application Fee Payment',
        'Loan Application Code': loanApplicationCode,
        'Loan Amount': '$loanAmount USD',
        'Monthly Payment': '$monthlyPayment USD',
        'Total Payment': '$totalPayment USD',
        'Application Fee': '${applicationFeeTransaction?.billAmount ?? 0} USD',
        'Payment Method': applicationFeeTransaction?.paymentMethod ?? 'WALLET',
        'Transaction Status': 'COMPLETED',
        'Bill Reference': applicationFeeTransaction?.billRefNo ?? '',
        'Date': AppMapper.safeFormattedDate(
          applicationFeeTransaction?.paidDate ?? DateTime.now(),
        ),
      },
      totalAmount:
          (applicationFeeTransaction?.billAmount as num? ?? 0).toDouble(),
      billAmount:
          (applicationFeeTransaction?.billAmount as num? ?? 0).toDouble(),
      originalCurrency: 'USD',
      transactionId: (confirmation.id ?? '') as String,
      billRefNo: (applicationFeeTransaction?.billRefNo ?? '') as String,
      status: 'Paid',
      title: 'Your loan application fee payment was completed successfully',
    );
  }

  Widget _buildBankCard(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Image.network(
            widget.bank.logo ?? '',
            width: 32.w,
            height: 32.h,
            errorBuilder: (context, error, stackTrace) =>
                Icon(Icons.account_balance, size: 32.w),
          ),
          SizedBox(width: 12.w),
          Text(
            widget.bank.name,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
