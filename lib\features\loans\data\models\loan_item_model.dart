import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';

/// Model for unified loan items (cars, houses, etc.)
class LoanItemModel extends LoanItem {
  const LoanItemModel({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.featureImage,
    required super.galleryImages,
    required super.type,
    required super.category,
    required super.loanPeriod,
    required super.isActive,
    required super.isDeleted,
    required super.createdAt,
    required super.updatedAt,
    // Car-specific
    super.model,
    super.transmission,
    super.numberOfSeats,
    super.engineCapacity,
    super.color,
    super.make,
    super.driveTrain,
    super.engineSize,
    super.fuelType,
    super.mileage,
    super.fuelEfficiency,
    super.horsePower,
    super.manufactureYear,
    // House-specific
    super.location,
    super.amenities,
    super.condition,
    super.bathroom,
    super.bedroom,
    super.area,
    super.virtualTour,
  });

  /// Factory for creating car loan item from JSON
  factory LoanItemModel.carFromJson(Map<String, dynamic> json) {
    return LoanItemModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      name: AppMapper.safeString(json['name']),
      description: AppMapper.safeString(json['description']),
      price: AppMapper.safeDouble(json['price']),
      featureImage: AppMapper.safeString(json['featureImage']),
      galleryImages: _parseGalleryImages(json['galleryImages']),
      type: LoanItemType.car,
      category: _parseLoanItemCategory(json['bodyType']),
      loanPeriod: AppMapper.safeInt(json['loanPeriod']),
      isActive: json['isActive'] == true,
      isDeleted: json['isDeleted'] == true,
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
          DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt']?.toString() ?? '') ??
          DateTime.now(),
      // Car-specific fields
      model: AppMapper.safeString(json['model']),
      transmission: AppMapper.safeString(json['transmission']),
      numberOfSeats: AppMapper.safeInt(json['numberOfSeats']),
      engineCapacity: AppMapper.safeString(json['engineCapacity']),
      color: AppMapper.safeString(json['color']),
      make: AppMapper.safeString(json['make']),
      driveTrain: AppMapper.safeString(json['driveTrain']),
      engineSize: AppMapper.safeInt(json['engineSize']),
      fuelType: AppMapper.safeString(json['fuelType']),
      mileage: AppMapper.safeInt(json['mileage']),
      fuelEfficiency: AppMapper.safeString(json['fuelEfficiency']),
      horsePower: AppMapper.safeInt(json['horsePower']),
      manufactureYear: AppMapper.safeInt(json['manufactureYear']),
    );
  }

  /// Factory for creating house loan item from JSON
  factory LoanItemModel.houseFromJson(Map<String, dynamic> json) {
    return LoanItemModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      name: AppMapper.safeString(json['name']),
      description: AppMapper.safeString(json['description']),
      price: AppMapper.safeDouble(json['price']),
      featureImage: AppMapper.safeString(json['featureImage']),
      galleryImages: _parseGalleryImages(json['galleryImages']),
      type: LoanItemType.house,
      category: _parseLoanItemCategory(json['type']),
      loanPeriod:
          AppMapper.safeInt(json['loanPeriod'] ?? 12), // Default loan period
      isActive: json['isActive'] as bool ?? true,
      isDeleted: json['isDeleted'] == true,
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
          DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt']?.toString() ?? '') ??
          DateTime.now(),
      // House-specific fields
      location: _parseLocation(json['location']),
      amenities: _parseAmenities(json['amenities']),
      condition: AppMapper.safeString(json['condition']),
      bathroom: AppMapper.safeInt(json['bathroom']),
      bedroom: AppMapper.safeInt(json['bedroom']),
      area: AppMapper.safeDouble(json['area']),
      virtualTour: json['virtualTour']?.toString(),
    );
  }

  /// Parse gallery images from JSON
  static List<GalleryImage> _parseGalleryImages(dynamic galleryJson) {
    if (galleryJson is List) {
      return galleryJson
          .map((item) =>
              GalleryImageModel.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  /// Parse loan item category from JSON
  static LoanItemCategory _parseLoanItemCategory(dynamic categoryJson) {
    if (categoryJson is Map<String, dynamic>) {
      return LoanItemCategoryModel.fromJson(categoryJson);
    }
    return const LoanItemCategory(
      id: '',
      name: 'Unknown',
      description: '',
      icon: '',
    );
  }

  /// Parse location from JSON
  static Location? _parseLocation(dynamic locationJson) {
    if (locationJson is Map<String, dynamic>) {
      return LocationModel.fromJson(locationJson);
    }
    return null;
  }

  /// Parse amenities from JSON
  static List<Amenity>? _parseAmenities(dynamic amenitiesJson) {
    if (amenitiesJson is List) {
      return amenitiesJson
          .map((item) => AmenityModel.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    return null;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'featureImage': featureImage,
      'galleryImages': galleryImages
          .map((img) => {
                'type': img.type,
                'url': img.url,
              })
          .toList(),
      'type': type.name,
      'category': {
        'id': category.id,
        'name': category.name,
        'description': category.description,
        'icon': category.icon,
      },
      'loanPeriod': loanPeriod,
      'isActive': isActive,
      'isDeleted': isDeleted,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };

    // Add car-specific fields if this is a car
    if (isCar) {
      json.addAll({
        'model': model,
        'transmission': transmission,
        'numberOfSeats': numberOfSeats,
        'engineCapacity': engineCapacity,
        'color': color,
        'make': make,
        'driveTrain': driveTrain,
        'engineSize': engineSize,
        'fuelType': fuelType,
        'mileage': mileage,
        'fuelEfficiency': fuelEfficiency,
        'horsePower': horsePower,
        'manufactureYear': manufactureYear,
      });
    }

    // Add house-specific fields if this is a house
    if (isHouse) {
      json.addAll({
        'location': location != null
            ? {
                'fieldName': location!.fieldName,
                'lng': location!.lng,
                'lat': location!.lat,
              }
            : null,
        'amenities': amenities?.map((amenity) => amenity.toJson()).toList(),
        'condition': condition,
        'bathroom': bathroom,
        'bedroom': bedroom,
        'area': area,
        'virtualTour': virtualTour,
      });
    }

    return json;
  }
}

/// Model for loan item category
class LoanItemCategoryModel extends LoanItemCategory {
  const LoanItemCategoryModel({
    required super.id,
    required super.name,
    required super.description,
    required super.icon,
    super.isDeleted,
    super.createdAt,
    super.updatedAt,
  });

  factory LoanItemCategoryModel.fromJson(Map<String, dynamic> json) {
    return LoanItemCategoryModel(
      id: AppMapper.safeString(json['id'] ?? json['_id']),
      name: AppMapper.safeString(json['name']),
      description: AppMapper.safeString(json['description']),
      icon: AppMapper.safeString(json['icon']),
      isDeleted: json['isDeleted'] == true,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'].toString())
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'].toString())
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'description': description,
        'icon': icon,
        'isDeleted': isDeleted,
        if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
        if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      };
}

/// Model for gallery image
class GalleryImageModel extends GalleryImage {
  const GalleryImageModel({
    required super.type,
    required super.url,
  });

  factory GalleryImageModel.fromJson(Map<String, dynamic> json) {
    return GalleryImageModel(
      type: AppMapper.safeString(json['type']),
      url: AppMapper.safeString(json['url']),
    );
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'url': url,
      };
}

/// Model for location
class LocationModel extends Location {
  const LocationModel({
    required super.fieldName,
    required super.lng,
    required super.lat,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      fieldName: AppMapper.safeString(json['fieldName']),
      lng: AppMapper.safeString(json['lng']),
      lat: AppMapper.safeString(json['lat']),
    );
  }

  Map<String, dynamic> toJson() => {
        'fieldName': fieldName,
        'lng': lng,
        'lat': lat,
      };
}

/// Model for amenity
class AmenityModel extends Amenity {
  const AmenityModel({
    required super.amenity,
    required super.detail,
  });

  factory AmenityModel.fromJson(Map<String, dynamic> json) {
    return AmenityModel(
      amenity:
          AmenityDetailModel.fromJson(json['amenity'] as Map<String, dynamic>),
      detail: AppMapper.safeString(json['detail']),
    );
  }
}

/// Model for amenity detail
class AmenityDetailModel extends AmenityDetail {
  const AmenityDetailModel({
    required super.name,
    required super.icon,
  });

  factory AmenityDetailModel.fromJson(Map<String, dynamic> json) {
    return AmenityDetailModel(
      name: AppMapper.safeString(json['name']),
      icon: AppMapper.safeString(json['icon']),
    );
  }
}
