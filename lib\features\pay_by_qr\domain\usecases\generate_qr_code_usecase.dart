import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:cbrs/features/pay_by_qr/domain/entities/qr_generate_response.dart';
import 'package:cbrs/features/pay_by_qr/domain/repositories/qr_repository.dart';
import 'package:equatable/equatable.dart';

class GenerateQrCodeUsecase extends UsecaseWithoutParams<QrGenerateResponse> {
  const GenerateQrCodeUsecase(this._repository);
  final QrRepository _repository;

  @override
  ResultFuture<QrGenerateResponse> call() async {
    return _repository.generateQrCode();
  }
}
