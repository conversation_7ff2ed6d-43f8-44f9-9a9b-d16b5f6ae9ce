// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_transaction.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_loan_entity.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:cbrs/features/transactions/data/models/confirm_transfer_response_model.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';

class ConfirmMonthlyRepaymentWithPinUsecase

    extends UsecaseWithParams<MonthlyRepaymentTransactionEntity, ConfirmMonthlyRepaymentrParams> {
  const ConfirmMonthlyRepaymentWithPinUsecase(this._repository);
  final LoanRepaymentRepository _repository;

  @override
  ResultFuture<MonthlyRepaymentTransactionEntity> call(
    ConfirmMonthlyRepaymentrParams params,
  ) async {
    return _repository.confirmMonthlyRepaymentWithPin(
      pin: params.pin,
      billRefNo: params.billRefNo,
      transactionType: params.transactionType.value,
      loanId: params.loanId,
      months: params.months
    );
  }
}

class ConfirmMonthlyRepaymentrParams extends Equatable {
  const ConfirmMonthlyRepaymentrParams({
    required this.pin,
    required this.billRefNo,
    required this.loanId,
    required this.months,
    required this.transactionType,
  });

  final String pin;
  final String billRefNo;
  final String loanId;
  final String months;


  final TransactionType transactionType;

  @override
  List<Object?> get props => [pin, billRefNo, transactionType];
}
