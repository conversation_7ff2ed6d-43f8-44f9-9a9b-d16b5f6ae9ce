import 'dart:io';

import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/profile/domain/repositories/profile_repositories.dart';

class UpdateAvatarUsecase extends UsecaseWithParams<void, UpdateAvatarsParams> {
  final ProfileRepository repository;

  UpdateAvatarUsecase(this.repository);

  @override
  ResultFuture<String> call(UpdateAvatarsParams params) {
    return repository.updateAvatar(avatar: params.avatar);
  }
}

class UpdateAvatarsParams {
  UpdateAvatarsParams({required this.avatar});
  final File avatar;
}
