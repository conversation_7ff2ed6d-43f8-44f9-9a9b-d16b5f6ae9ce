import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FAQScreen extends StatelessWidget {
  const FAQScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'FAQs',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.fromLTRB(16.w, 22.h, 16.w, 16.h),
                child: Text(
                  'FAQs',
                  style: GoogleFonts.outfit(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
            SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildFAQItem(
                    'How do I load money into my wallet from my card?',
                    'You can load money by tapping "Load Wallet" on the home screen, entering the amount, and following the secure payment process.',
                  ),
                  _buildFAQItem(
                    'Can I send money directly from my wallet to a bank account?',
                    'Yes, select the "Send to Money" option, enter the recipient\'s bank details, and confirm the amount. The transfer will be processed and sent to the bank.',
                  ),
                  _buildFAQItem(
                    'How do I transfer money from my wallet to another user\'s wallet?',
                    'Choose "Transfer to Wallet", enter the recipient\'s details, specify the amount, and confirm the transfer.',
                  ),
                  _buildFAQItem(
                    'Is there a fee for loading my wallet from my card?',
                    'Loading fees may apply depending on your card type and transaction amount. You\'ll see any applicable fees before confirming the transaction.',
                  ),
                  _buildFAQItem(
                    'How can I check my transaction history?',
                    'Access your complete transaction history in the "Transactions" tab on the bottom navigation bar.',
                  ),
                  _buildFAQItem(
                    'What should I do if my card payment to load the wallet fails?',
                    'If a payment fails, verify your card details and try again. If the issue persists, contact your bank or our support team.',
                  ),
                  _buildFAQItem(
                    'How long does it take for the money to transfer to a bank account?',
                    'Bank transfers typically process within 1-3 business days, depending on the receiving bank.',
                  ),
                  _buildFAQItem(
                    'Can I link multiple cards to load my wallet?',
                    'Yes, you can save multiple cards in your account for convenient wallet loading.',
                  ),
                  _buildFAQItem(
                    'Is there a limit to how much I can send from my wallet?',
                    'Transfer limits may apply based on your account type and verification status. Check the app settings for your specific limits.',
                  ),
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Theme(
        data: ThemeData(
          dividerColor: Colors.transparent,
          shadowColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
        child: ExpansionTile(
          tilePadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
          title: Text(
            question,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          iconColor: Colors.black54,
          backgroundColor: Colors.white,
          collapsedBackgroundColor: Colors.white,
          childrenPadding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 16.h),
          shape: const RoundedRectangleBorder(
            side: BorderSide.none,
          ),
          collapsedShape: const RoundedRectangleBorder(
            side: BorderSide.none,
          ),
          children: [
            Text(
              answer,
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: Colors.black54,
                height: 1.6,
                letterSpacing: 0.2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
