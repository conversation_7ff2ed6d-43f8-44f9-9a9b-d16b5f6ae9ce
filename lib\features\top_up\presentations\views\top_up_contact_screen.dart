import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/airtime_shortcode.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/top_up/domain/entitities/top_up_providers_entity.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';
import 'package:flutter_native_contact_picker/model/contact.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class TopUpContactScreen extends StatefulWidget {
  const TopUpContactScreen({
    required this.provider,
    super.key,
  });

  final ProvidersEntity provider;

  @override
  State<TopUpContactScreen> createState() => _TopUpContactScreenState();
}

class _TopUpContactScreenState extends State<TopUpContactScreen> {
  TextEditingController phoneNumberController = TextEditingController();

  final FlutterNativeContactPicker _contactPicker =
      FlutterNativeContactPicker();
  List<Contact>? _contacts;
  String? _selectedPhoneNumber;
  String operatorShortCode = '';

  void handleContactChoosed(String phoneNumber) {
    debugPrint('1. pjon $phoneNumber');
    FocusScope.of(context).unfocus(); // Hide the keyboard

    // Remove spaces and '+' sign
    var trimPhone = phoneNumber.replaceAll(' ', '').replaceAll('+', '').trim();

    debugPrint('hmm trim $trimPhone and phone number $phoneNumber');

    // Remove country code and leading prefixes
    if (trimPhone.startsWith('2519') && operatorShortCode == '9') {
      trimPhone = trimPhone.replaceFirst('2519', '');
    } else if (trimPhone.startsWith('2517') && operatorShortCode == '7') {
      trimPhone = trimPhone.replaceFirst('2517', '');
    } else if (trimPhone.startsWith('09') && operatorShortCode == '9') {
      trimPhone = trimPhone.replaceFirst('09', '');
    } else if (trimPhone.startsWith('07') && operatorShortCode == '7') {
      trimPhone = trimPhone.replaceFirst('07', '');
    } else {
      trimPhone = '';
    }

    setState(() {
      phoneNumberController.text = trimPhone;
    });

    FocusScope.of(context).unfocus(); // Hide the keyboard
  }

  bool _userHasPhone = false;

  Future<void> getUserPhone() async {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();

    final userPhone = user?.phoneNumber ?? '';

    if (userPhone.isNotEmpty &&
        userPhone.substring(0, 5).contains(operatorShortCode)) {
      handleContactChoosed(userPhone);
      setState(() {
        _userHasPhone = true;
      });
    }

    setState(() {
      selectedTab = _userHasPhone == true ? 'Self' : 'Others';
    });
  }

  Future<void> openNativePhone() async {
    final contact = await _contactPicker.selectPhoneNumber();
    setState(() {
      _contacts = contact == null ? null : [contact];
      _selectedPhoneNumber = contact?.selectedPhoneNumber;
    });

    debugPrint(
      'native contact opened $_contacts phone number ${contact?.selectedPhoneNumber}',
    );
    handleContactChoosed(_selectedPhoneNumber ?? '');
    if (_contacts != null) {
      _contacts!.map(
        (contact) => handleContactChoosed(
          _selectedPhoneNumber != null ? _selectedPhoneNumber! : '',
        ),
      );
    }
  }

/*
getWalletBalance()
1. I check phone number and if it is correct
2. I will fetch ETB user wallet.
3. If wallet is loaded i navigate to add amount screen


*/
  void checkPhoneNumberLength() {
    if (phoneNumberController.text.length != 8) {
      CustomToastification(
        context,
        message:
            'Please enter a valid ${widget.provider.name} registered phone number. ',
      );
      return;
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getUserPhone();
    operatorShortCode = airTimeShortCode(widget.provider.name);
  }

  String selectedTab = 'Self';
  final List<String> tabList = ['Self', 'Others'];

  void onTap(String tabName) {
    if (tabName != selectedTab) {
      setState(() {
        selectedTab = tabName;
      });
      if (tabName == 'Self')
        getUserPhone();
      else
        setState(() {
          phoneNumberController.text = '';
        });
      FocusScope.of(context).unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mobile Top-up '),
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: BlocConsumer<WalletTransferBloc, WalletTransferState>(
          listener: (context, state) {
            if (state is WalletDetailsLoaded) {
              context.pushNamed(
                AppRouteName.topUpAddAmount,
                extra: {
                  'operatorName': widget.provider.name,
                  'operatorLogo': widget.provider.logo,
                  'operatorCode': operatorShortCode,
                  'phoneNumber': phoneNumberController.text,
                  'walletBalance': state.wallet.balance ?? 0.0,
                  'provider': widget.provider,
                },
              );
              debugPrint('my wallet balance ${state.wallet.balance}');
            } else if (state is WalletTransferError) {
              context.pop(true);
              CustomToastification(context, message: 'Please try again later');
            }
          },
          builder: (context, state) => Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // tabs section

                      // const ContactNative(),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          BuildOperatorLogo(
                            operatorLogo: widget.provider.logo,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      if (_userHasPhone) ...[
                        CustomRoundedTabs(
                          onTap: onTap,
                          selectedTab: selectedTab,
                          tabList: tabList,
                        ),
                        // TopUpContactTabs(
                        //   selectedTab: selectedTab,
                        //   onTap: handleTab,
                        //   userHasPhone: _userHasPhone,
                        // ),
                        SizedBox(
                          height: 24.h,
                        ),
                      ],
                      BuildPhoneForm(
                        shortCode: operatorShortCode,
                        // handleContactChoosed: handleContactChoosed,
                        handleContactChoosed: openNativePhone,

                        phoneController: phoneNumberController,
                        isReadOnly: selectedTab == 'Self',
                      ),

                      // phone form section
                    ],
                  ),
                ),
              ),
              CustomRoundedBtn(
                isLoading: state is WalletTransferLoading,
                btnText: 'Continue',
                onTap: () {
                  debugPrint('where are we');
                  checkPhoneNumberLength();
                  FocusScope.of(context).unfocus();

                  context.pushNamed(
                    AppRouteName.topUpAddAmount,
                    extra: {
                      'phoneNumber': phoneNumberController.text,
                      'provider': widget.provider,
                    },
                  );

                  // Navigator.of(context).push(
                  //   MaterialPageRoute(
                  //     builder: (context) => TopUpAddAmounScreen(
                  //       operatorName: 'Safaricom',
                  //       operatorLogo: operatorShortCode == '9'
                  //           ? MediaRes.ethMiniIcon
                  //           : MediaRes.safariMiniIcon,
                  //       phoneNumber: phoneNumberController.text,
                  //       operatorCode: operatorShortCode,
                  //     ),
                  //   ),
                  // );
                },
              ),
              SizedBox(
                height: 10.h,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BuildOperatorLogo extends StatelessWidget {
  const BuildOperatorLogo({required this.operatorLogo, super.key});
  final String operatorLogo;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomCachedImage(
          url: operatorLogo,
          width: 160.w,
          height: 47.h,
          boxFit: BoxFit.cover,
        ),
      ],
    );
  }
}

class BuildPhoneForm extends StatelessWidget {
  const BuildPhoneForm({
    required this.shortCode,
    required this.handleContactChoosed,
    required this.phoneController,
    required this.isReadOnly,
    super.key,
  });
  final String shortCode;
  final void Function() handleContactChoosed;
  final TextEditingController phoneController;
  final bool isReadOnly;
  @override
  Widget build(BuildContext context) {
    // final phoneController = TextEditingController();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomBuildText(
          text: 'Phone Number',
          caseType: '',
          fontSize: 14.sp,
          color: const Color(0xFFAAAAAA),
        ),
        SizedBox(
          height: 8.h,
        ),
        SizedBox(
          // color: const Color(0xFFF8F000),
          height: 56.h,
          child: Row(
            children: [
              /*
               Container(
                                // alignment: Alignment.centerLeft,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image.asset(
                                      MediaRes.ethFlagIcon,
                                      width: 24.w,
                                      height: 24.h,
                                    ),
                                    SizedBox(width: 10.w),
                                    Text(
                                      '(+251)',
                                      style: GoogleFonts.outfit(
                                        fontSize: 18.sp,
                                        fontWeight: FontWeight.normal,
                                        backgroundColor: Colors.amber
                                        
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      '$shortCode-',
                                      style: GoogleFonts.outfit(
                                        fontSize: 18.sp,
                                        fontWeight: FontWeight.normal,
                                        backgroundColor: Colors.amber
                                      ),
                                    ),
                                    // const SizedBox(width: 2),
                                        
                                    // const Icon(
                                    //   Icons.remove,
                                    //   size: 16,
                                    // ),
                                  ],
                                ),
                              ),
            
            */

              IntrinsicHeight(
                child: GestureDetector(
                  onTap: () {
                    FocusScope.of(context).requestFocus();
                  },
                  child: Container(
                    // alignment: Alignment.centerLeft,
                    height: 56.h,
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    decoration: const BoxDecoration(
                      color: Color(0xFFF8F8F8),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        bottomLeft: Radius.circular(12),
                      ),
                    ),

                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          MediaRes.ethFlagIcon,
                          width: 24.w,
                          height: 24.h,
                        ),
                        SizedBox(width: 10.w),
                        Text(
                          '(+251)',
                          style: GoogleFonts.outfit(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.normal,
                            // backgroundColor: Colors.amber
                          ),
                        ),
                        const SizedBox(width: 10),
                        Text(
                          '$shortCode-',
                          style: GoogleFonts.outfit(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.normal,
                            // backgroundColor: Colors.amber
                          ),
                        ),
                        // const SizedBox(width: 2),

                        // const Icon(
                        //   Icons.remove,
                        //   size: 16,
                        // ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  height: 56.h,
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: phoneController,
                            readOnly: isReadOnly,

                            onChanged: (value) {
                              if (value.length == 8) {
                                FocusScope.of(context).unfocus();
                              }
                            },
                            // textAlign: TextAlign.center,

                            style: GoogleFonts.outfit(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.normal,
                              // backgroundColor: Colors.yellow
                            ),

                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                vertical: 0.h,
                                horizontal: 0.w,
                              ),
                              hintText: '00000000',
                              border: InputBorder.none,
                              hintStyle: GoogleFonts.outfit(
                                color: const Color(0xFFAAAAAA),
                                fontSize: 18.sp,
                                fontWeight: FontWeight.normal,
                                // style: const TextStyle(fontSize: 18.sp),
                              ),
                            ),

                            keyboardType: TextInputType.phone,
                            // textInputType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(8),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (!isReadOnly) ...[
                const SizedBox(
                  width: 8,
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 8.h),
                  height: 56.h,
                  width: 56.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Center(
                    child: GestureDetector(
                      onTap: handleContactChoosed,
                      child: Image.asset(
                        'assets/vectors/contact_field_image.png',
                        color: Theme.of(context).primaryColor,
                        width: 36.w,
                        height: 36.h,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
