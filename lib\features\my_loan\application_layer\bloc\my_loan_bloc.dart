import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_event.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_state.dart';
import 'package:cbrs/features/my_loan/domain/usecases/generate_upfront_payment_usecase.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RepaymentBloc extends Bloc<RepaymentLoanEvent, RepaymentLoanState> {
  RepaymentBloc({
    required LoanRepaymentRepository repository,
  })  : _repository = repository,
        super(LoanInitialState()) {
    on<FetchLoansEvent>(handleFetchLoans);
    on<FetchLoanInfoEventEvent>(_handleFetchLoanInfo);

    on<FetchRepaymentHistoryEvent>(_handleFetchRepaymentHistory);
    on<ClearLoansEvent>((event, emit) {
      emit(RepaymentLoadingState());
    });
  }
  final LoanRepaymentRepository _repository;

  Future<void> handleFetchLoans(
    FetchLoansEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    try {
      final currentState = state;

      var nextPage = (currentState is RepaymentLoadedState)
          ? currentState.currentPage + 1
          : 1;

      final currentLoanState = (currentState is RepaymentLoadedState)
          ? currentState.loanState
          : 'active';

      if (currentState is RepaymentLoadedState &&
          !currentState.hasNextPage &&
          currentState.loanState == event.loanStatus) {
        return;
      }

      if (nextPage == 1 || currentState is! RepaymentLoadedState) {
        debugPrint('emit(RepaymentLoadingState());');
        emit(RepaymentLoadingState());
      }

      if (currentState is RepaymentLoadedState &&
          currentState.loanState != event.loanStatus) {
        nextPage = 1;
        emit(RepaymentLoadingState());
      }

      final result = await _repository.fetchLoanRepayments(
        status: event.loanStatus,
        page: nextPage,
      );
      result.fold(
        (failure) => emit(RepaymentErrorState(failure.message)),
        (success) {
          final loansWrapper = success.loanRepaymentsDataWrapper;
          final newLoanRepayments = loansWrapper.loanRepayments;
          final pagination = loansWrapper.pagination;

          if (currentState is RepaymentLoadedState &&
              currentState.loanState == event.loanStatus) {
            final updatedLoans = [...currentState.loans, ...newLoanRepayments];
            emit(
              currentState.copyWith(
                loans: updatedLoans,
                currentPage: currentState.currentPage + 1,
                hasNextPage: pagination?.hasNextPage ?? false,
              ),
            );
          } else {
            emit(
              RepaymentLoadedState(
                loans: newLoanRepayments,
                currentPage: 1,
                hasNextPage: pagination?.hasNextPage ?? false,
                loanState: event.loanStatus,
              ),
            );
          }
        },
      );
    } catch (e) {
      debugPrint('Error in fetching repayment lists $e');
      emit(RepaymentErrorState('Failed to fetch loans'));
    }
  }

  Future<void> _handleFetchLoanInfo(
    FetchLoanInfoEventEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('fetch laons');

    try {
      emit(RepaymentLoadingState());
      final result = await _repository.fetchLoanInfoDetail(
        loanId: event.loanId,
        months: event.months,
      );
      result.fold(
        (failure) => emit(RepaymentErrorState(failure.message)),
        (success) => emit(RepaymentInfoLoadedState(success)),
      );
    } catch (e) {
      emit(RepaymentErrorState('Failed to fetch loans'));
    }
  }

// Helper method to clean up error messages
  String _cleanErrorMessage(String error) {
    // Remove generic "Exception: " prefix
    var cleanedError = error.replaceAll('Exception: ', '');

    // for specifc classes like network error, apiexception, serverexception
    if (cleanedError.contains('ApiException(')) {
      final regex = RegExp(r'ApiException\((.*?),');
      final match = regex.firstMatch(cleanedError);
      if (match != null && match.groupCount > 0) {
        cleanedError = match.group(1) ?? cleanedError;
      }
    }

    return cleanedError;
  }

  Future<void> _handleFetchRepaymentHistory(
    FetchRepaymentHistoryEvent event,
    Emitter<RepaymentLoanState> emit,
  ) async {
    print('fetch laons');

    try {
      emit(RepaymentHistroyLoadingState());
      final result = await _repository.fetchPaymentHistory(
        loanPaymentId: event.loanPaymentId,
      );
      result.fold(
        (failure) => emit(RepaymentErrorState(failure.message)),
        (success) => emit(LoanHistoryRepaymentState(success)),
      );
    } catch (e) {
      emit(RepaymentErrorState('Failed to fetch loan payment history'));
    }
  }
}
