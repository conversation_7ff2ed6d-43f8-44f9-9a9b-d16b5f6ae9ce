import 'package:cbrs/features/mortgage_loan/data/models/application_fee_transaction_model.dart';
import 'package:cbrs/features/mortgage_loan/data/models/loan_payment_info_model.dart';
import 'package:cbrs/features/mortgage_loan/data/models/mortgage_bank_model.dart';
import 'package:cbrs/features/mortgage_loan/data/models/mortgage_loan_application_model.dart';

abstract class MortgageBankRemoteDataSource {
  Future<List<MortgageBankModel>> getMortgageBanks({
    required String productId,
    int page = 1,
    int limit = 10,
  });

  Future<LoanPaymentInfoModel> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
  });

  Future<MortgageLoanApplicationModel> applyMortgageLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    bool isAutoRepay = false,
  });

  Future<ApplicationFeeTransactionModel> generateApplicationTransaction({
    required String loanApplicationId,
  });

  Future<String> getLoanTerms({required String bankId});
}
