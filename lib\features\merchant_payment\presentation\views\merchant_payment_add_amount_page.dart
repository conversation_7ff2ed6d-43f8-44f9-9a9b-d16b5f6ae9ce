import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_recipient_preview.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/merchant_payment/application/bloc/merchant_payment_bloc.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/user/domain/entities/user.dart';
import 'package:cbrs/features/user/presentation/bloc/user_bloc.dart';
import 'package:cbrs/features/user/presentation/bloc/user_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class MerchantPaymentAddAmountPage extends StatefulWidget {
  const MerchantPaymentAddAmountPage({
    required this.merchantId,
    required this.merchantCode,
    required this.merchantName,
    this.amount,
    super.key,
  });
  final String merchantId;
  final String merchantCode;
  final String merchantName;
  final String? amount;

  @override
  State<MerchantPaymentAddAmountPage> createState() =>
      _MerchantPaymentAddAmountPageState();
}

class _MerchantPaymentAddAmountPageState
    extends State<MerchantPaymentAddAmountPage> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  bool _isLoading = false;
  String _senderName = '';

  late CurrencyInputController _currencyController;
  late TransactionBottomSheetsManager _bottomSheetsManager;

  @override
  void initState() {
    super.initState();
    // Validate required parameters
    assert(widget.merchantId.isNotEmpty, 'merchantId cannot be empty');
    assert(widget.merchantName.isNotEmpty, 'merchantName cannot be empty');

    context.read<WalletBalanceBloc>().add(
          const FetchWalletEvent(
            isUsdWallet: false,
            forceTheme: false,
          ),
        );

    _currencyController = CurrencyInputController(
      currencyType: CurrencyType.etb,
      maxBalance: 0,
      // maxBalance: _getWalletBalance(context),
    );

    // Set initial amount if provided in QR code
    if (widget.amount != null && widget.amount!.isNotEmpty) {
      try {
        final initialAmount = double.parse(widget.amount!);
        _currencyController.setAmount(initialAmount);
      } catch (e) {
        // Ignore parsing errors, just don't set initial amount
        debugPrint('Error parsing amount from QR: $e');
      }
    }

    _amountController.addListener(_updateButtonState);

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.merchantPayment,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: (context.read<MerchantPaymentBloc>().state
                        as WalletTransferPinRequired)
                    .billRefNo,
                transactionType: tx_type.TransactionType.merchantPayment,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _amountController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  void _updateButtonState() {
    // This method is required by the CurrencyInputController
    // but we don't need to do anything here since the CurrencyInputWidget
    // handles the button state internally
  }

  void _onContinuePressed() {
    if (_isLoading) return;

    try {
      final cleanAmount = _currencyController.numericAmount
          .toString()
          .replaceAll(RegExp(r'[^\d.]'), '');

      // _amountController.text.replaceAll(RegExp(r'[^\d.]'), '');
      final amount = double.parse(cleanAmount);

      if (amount <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Amount must be greater than 0',
              style: GoogleFonts.outfit(),
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Check transfer rules before proceeding
      context.read<MerchantPaymentBloc>().add(
            CheckMerchantPaymentRulesRequested(
              amount: amount,
              currency: 'ETB',
            ),
          );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Invalid amount format',
            style: GoogleFonts.outfit(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showConfirmScreenBottomSheet(WalletTransferResponse response) {
    // Calculate the total amount if it's not already set
    final calculatedTotalAmount = response.data.billAmount +
        response.data.vat +
        response.data.serviceCharge;

    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Merchant payment',
        'Merchant Name': widget.merchantName,
        'Merchant Code': widget.merchantCode,
        'Sender Name': _senderName,
        'Amount': response.data.billAmount,
        'serviceCharge': response.data.serviceCharge,
        'VAT': response.data.vat,
        'Date': AppMapper.safeFormattedDate(response.data.createdAt),
        'billRefNo': response.data.billRefNo,
      },
      originalCurrency: response.data.originalCurrency,
      totalAmount: response.data.totalAmount,
      billAmount: response.data.billAmount,
      confirmButtonText: 'Confirm Payment',
      requiresOtp: (context.read<MerchantPaymentBloc>().state
              as WalletTransferPinRequired)
          .requiresOtp,
      billRefNo: response.data.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;

    final calculatedTotalAmount =
        transaction.billAmount + transaction.serviceCharge + transaction.vat;

    _bottomSheetsManager.showSuccessScreenBottomSheet(
      {
        'Merchant Name': widget.merchantName,
        'Merchant Code': widget.merchantCode,
        'Sender Name': _senderName,
        'billRefNo': transaction.billRefNo,
        'Date': AppMapper.safeFormattedDate(transaction.createdAt),
        'Service Charge':
            '${transaction.serviceCharge} ${response.transaction.originalCurrency}',
        'Vat': '${transaction.vat} ${response.transaction.originalCurrency}',
        'Amount':
            '${transaction.billAmount} ${response.transaction.originalCurrency}',
      },
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,
      status: response.transaction.status,
      totalAmount: response.transaction.totalAmount,
      billAmount: response.transaction.billAmount,
      originalCurrency: response.transaction.originalCurrency,
    );
  }

  double balance = 0;
  @override
  Widget build(BuildContext context) {
    final userState = context.read<UserBloc>().state;
    if (userState is UserLoaded) {
      _senderName = '${userState.user.firstName} ${userState.user.lastName}';
    }

    return BlocConsumer<MerchantPaymentBloc, MerchantPaymentState>(
      listenWhen: (previous, current) =>
          current is CheckingMerchantPaymentRules ||
          current is MerchantPaymentRulesChecked ||
          current is WalletTransferLoading ||
          current is WalletTransferPinRequired ||
          current is WalletTransferFailure ||
          current is WalletTransferError,
      listener: (context, state) {
        if (state is CheckingMerchantPaymentRules ||
            state is WalletTransferLoading) {
          setState(() => _isLoading = true);
        } else {
          setState(() => _isLoading = false);
        }

        if (state is MerchantPaymentRulesChecked) {
          // Initiate merchant payment after rules are checked
          context.read<MerchantPaymentBloc>().add(
                MerchantPaymentEventt(
                  beneficiaryId: widget.merchantId,
                  amount: _currencyController.numericAmount,
                  currency: 'ETB',
                ),
              );
        } else if (state is WalletTransferPinRequired) {
          // Get the response from the bloc
          final merchantBloc = context.read<MerchantPaymentBloc>();
          if (merchantBloc.lastResponse != null) {
            _showConfirmScreenBottomSheet(merchantBloc.lastResponse!);
          }
        } else if (state is WalletTransferFailure ||
            state is WalletTransferError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state is WalletTransferFailure
                    ? state.message
                    : (state as WalletTransferError).message,
                style: GoogleFonts.outfit(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      buildWhen: (previous, current) =>
          current is WalletTransferInitial ||
          current is WalletTransferLoading ||
          current is WalletDetailsLoaded,
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Merchant Payment',
              style: GoogleFonts.outfit(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          body: SafeArea(
            child: BlocListener<TransactionBloc, TransactionState>(
              listenWhen: (previous, current) =>
                  current is ConfirmTransferSuccess ||
                  current is ConfirmTransferError,
              listener: (context, state) {
                if (state is ConfirmTransferError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        state.message,
                        style: GoogleFonts.outfit(),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: BlocConsumer<WalletBalanceBloc, HomeState>(
                listener: (context, state) {
                  if (state is WalletLoadedState) {
                    setState(() {
                      _currencyController = CurrencyInputController(
                        currencyType: CurrencyType.etb,
                        maxBalance: state.etbBalance,
                        // maxBalance: _getWalletBalance(context),
                      );

                      balance = state.etbBalance;
                    });
                  }
                },
                builder: (context, state) {
                  ///_getWalletBalance(context)
                  /// Nagaatti
                  /// hayimaloo

                  ///

                  return BlocProvider(
                    create: (context) => GetIt.I<TransactionBloc>(),
                    child: BlocListener<TransactionBloc, TransactionState>(
                      listenWhen: (previous, current) =>
                          current is ConfirmTransferSuccess,
                      listener: (context, state) {
                        if (state is ConfirmTransferSuccess) {
                          _showSuccessScreenBottomSheet(state.transaction);
                        }
                      },
                      child: CurrencyInputWidget(
                        controller: _currencyController,
                        title: '',
                        subtitle: ''
                            '${widget.merchantName}.',
                        header: Column(
                          children: [
                            const CustomPageHeader(
                              pageTitle: 'Add Amount',
                              description:
                                  'Enter the amount you want to transfer to the merchant, then submit to complete the transaction.',
                            ),
                            RecipientCard(
                              avatar: '_recipentAvatar',
                              name: widget.merchantName,
                              accountNumber: widget.merchantCode,
                              isBirrTransfer: true,
                              // recipientEmail: _emailController.text,
                              onTap: () {},
                            ),
                          ],
                        ),
                        balanceLabel: CurrencyFormatter.formatWalletBalance(
                          balance,
                          'ETB',
                        ),
                        onContinue: _onContinuePressed,
                        isLoading: _isLoading,
                        transactionType: 'merchant_payment',
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
