import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:flutter/foundation.dart';

class WelcomeRepaymentLoanInfoModel extends LoanRepaymentInfoEntity {
  WelcomeRepaymentLoanInfoModel({
    required bool success,
    required int status,
    required LoanRepaymentDataEntity data,
  }) : super(
          success: success,
          status: status,
          loanRepayments: data,
        );

  factory WelcomeRepaymentLoanInfoModel.fromJson(Map<String, dynamic> json) {
    return WelcomeRepaymentLoanInfoModel(
      success: AppMapper.safeBool(json['success']),
      status: AppMapper.safeInt(json['status']),
      data: json['data'] != null
          ? LoanRepaymentData.fromJson(AppMapper.safeMap(json['data']))
          : throw Exception("Missing 'data' field"),
    );
  }
}

class LoanRepaymentData extends LoanRepaymentDataEntity {
  LoanRepaymentData({
    final String? bankName,
    final String? nextPaymentDate,
    ProductDetails? productDetails,
    LoanInfo? loanInfo,
    CurrentBill? currentBill,
    PaymentHistory? paymentHistory,
  }) : super(
          bankName: bankName,
          nextPaymentDate: nextPaymentDate,
          productDetails: productDetails,
          loanInfo: loanInfo,
          currentBill: currentBill,
          paymentHistory: paymentHistory,
        );

  factory LoanRepaymentData.fromJson(Map<String, dynamic> json) {
    return LoanRepaymentData(
      bankName: AppMapper.safeString(json['bankName']),
      nextPaymentDate: AppMapper.safeFormattedDate(json['nextPaymentDate']),
      productDetails:
       json['productDetails'] != null
          ? ProductDetails.fromJson(AppMapper.safeMap(json['productDetails']))
          : null,
      loanInfo: json['loanInfo'] != null
          ? LoanInfo.fromJson(AppMapper.safeMap(json['loanInfo']))
          : null,
      currentBill: json['currentBill'] != null
          ? CurrentBill.fromJson(AppMapper.safeMap(json['currentBill']))
          : null,
      paymentHistory: json['paymentHistory'] != null
          ? PaymentHistory.fromJson(AppMapper.safeMap(json['paymentHistory']))
          : null,
    );
  }
}

class ProductDetails extends ProductDetailsEntity {
  ProductDetails({
    String? sId,
    String? name,
    String? model,
    String? transmission,
    String? maxSpeed,
    String? price,
    String? numberOfSeats,
    String? engineCapacity,
    String? featureImage,
    List<GalleryImage>? galleryImages,
    List<AmenityData>? amenityData,
    String? color,
    String? make,
    String? driveTrain,
    String? mileAge,
    BodyType? bodyType,
    MortgageLocation? location,
    String? engineSize,
    String? fuelType,
    String? fuelEfficency,
    String? horsePower,
    String? manufactureYear,
    bool? isDeleted,
    String? createdAt,
    String? updatedAt,
    String? description,
    String? amount,
    String? condition,
    String? bathroom,
    String? bedRoom,
    String? area,
  }) : super(
          id: sId,
          name: name,
          model: model,
          transmission: transmission,
          maxSpeed: maxSpeed,
          price: price,
          numberOfSeats: numberOfSeats,
          engineCapacity: engineCapacity,
          featureImage: featureImage,
          galleryImages: galleryImages,
          amenityData: amenityData,
          color: color,
          make: make,
          driveTrain: driveTrain,
          mileAge: mileAge,
          bodyType: bodyType,
          engineSize: engineSize,
          fuelType: fuelType,
          fuelEfficiency: fuelEfficency,
          horsePower: horsePower,
          manufactureYear: manufactureYear,
          isDeleted: isDeleted,
          createdAt: createdAt,
          updatedAt: updatedAt,
          location: location,
          description: description,
          condition: condition,
          amount: amount,
          bathroom: bathroom,
          bedRoom: bedRoom,
          area: area,
        );

  /// Factory constructor for JSON deserialization
  factory ProductDetails.fromJson(Map<String, dynamic> json) {
    debugPrint("⛑️The ProductDetails:  ${json} \n");

    return ProductDetails(
      sId: AppMapper.safeString(json['_id']),
      name: AppMapper.safeString(json['name']),
      model: AppMapper.safeString(json['model']),

      transmission: AppMapper.safeString(json['transmission']),
      maxSpeed: AppMapper.safeString(json['maxSpeed']),

      // AppMapper.safeString(json['price']),
      price: AppMapper.safeFormattedNumberWithDecimal(json['price']),

      amount: AppMapper.safeFormattedNumberWithDecimal(json['price']),

      numberOfSeats: AppMapper.safeString(json['numberOfSeats']),
      engineCapacity: AppMapper.safeString(json['engineCapacity']),
      featureImage: AppMapper.safeString(json['featureImage']),

      galleryImages: AppMapper.safeList<dynamic>(json['galleryImages'])
          .map((item) => GalleryImage.fromJson(AppMapper.safeMap(item)))
          .toList(),
      amenityData: AppMapper.safeList<dynamic>(json['amenities'])
          .map((item) => AmenityData.fromJson(AppMapper.safeMap(item)))
          .toList(),

      // galleryImages: AppMapper.safeList<GalleryImage>(json['galleryImages']),
      color: AppMapper.safeString(json['color']),
      make: AppMapper.safeString(json['make']),
      driveTrain: AppMapper.safeString(json['driveTrain']),
      mileAge: AppMapper.safeString(json['mileage']),

      bodyType: json['bodyType'] != null
          ? BodyType.fromJson(AppMapper.safeMap(json['bodyType']))
          : null,

      location: json['location'] != null
          ? MortgageLocation.fromJson(AppMapper.safeMap(json['location']))
          : null,
      engineSize: AppMapper.safeString(json['engineSize']),
      fuelType: AppMapper.safeString(json['fuelType']),
      fuelEfficency: AppMapper.safeString(json['fuelEfficency']),
      horsePower: AppMapper.safeString(json['horsePower']),
      manufactureYear: AppMapper.safeString(json['manufactureYear']),
      isDeleted: AppMapper.safeBool(json['isDeleted']),
      createdAt: AppMapper.safeString(json['createdAt']),
      updatedAt: AppMapper.safeString(json['updatedAt']),
      description: AppMapper.safeString(json['description']),
      condition: AppMapper.safeString(json['condition']),
      bathroom: AppMapper.safeString(json['bathroom']),
      bedRoom: AppMapper.safeString(json['bedroom']),
      area: AppMapper.safeString(json['area']),
    );
  }
}

class GalleryImage extends GalleryImagesEntity {
  GalleryImage({
    String? type,
    required String imageUrl,
  }) : super(imageUrl: imageUrl, type: type);

  /// Factory constructor for JSON deserialization
  factory GalleryImage.fromJson(Map<String, dynamic> json) {
    return GalleryImage(
      type: AppMapper.safeString(json['type']),
      imageUrl: AppMapper.safeString(json['url']),
    );
  }
}

class AmenityData extends AmenityDataEntity {
  AmenityData({
    Amenity? amenity,
    String? detail,
  }) : super(amenity: amenity, detail: detail);

  /// Factory constructor for JSON deserialization
  factory AmenityData.fromJson(Map<String, dynamic> json) {
    return AmenityData(
      amenity: json['amenity'] != null
          ? Amenity.fromJson(AppMapper.safeMap(json['amenity']))
          : null,
      detail: AppMapper.safeString(json['detail']),
    );
  }
}

class Amenity extends AmenityEntity {
  Amenity({
    String? name,
    String? icon,
  }) : super(name: name, icon: icon);

  /// Factory constructor for JSON deserialization
  factory Amenity.fromJson(Map<String, dynamic> json) {
    return Amenity(
      name: AppMapper.safeString(json['name']),
      icon: AppMapper.safeString(json['icon']),
    );
  }
}

class BodyType extends BodyTypeEntity {
  BodyType({String? sId, String? name, String? description})
      : super(id: sId, name: name, description: description);

  /// Factory constructor for JSON deserialization
  factory BodyType.fromJson(Map<String, dynamic> json) {
    return BodyType(
      sId: AppMapper.safeString(json['_id']),
      name: AppMapper.safeString(json['name']),
      description: AppMapper.safeString(json['description']),
    );
  }
}

class MortgageLocation extends MortgageLocationEntity {
  MortgageLocation({String? fieldName, String? lng, String? lat})
      : super(fieldName: fieldName, lng: lng, lat: lat);

  /// Factory constructor for JSON deserialization
  factory MortgageLocation.fromJson(Map<String, dynamic> json) {
    // debugPrint("⛑️The MortgageLocation: json data ${json} \n");

    return MortgageLocation(
      fieldName: AppMapper.safeString(json['fieldName']),
      lng: AppMapper.safeString(json['lng']),
      lat: AppMapper.safeString(json['lat']),
    );
  }
}

class LoanInfo extends LoanInfoEntity {
  LoanInfo({
    DownPayment? downPayment,
    PaidAmount? paidAmount,
    String? monthlyRepayment,
    String? remainingAmount,
    String? interestRate,
    String? expectedEndDate,
    String? status,
    double? loanPeriod,
    String? facilitationFee,
    String? dailyPenaltyFee,
  }) : super(
          downPayment: downPayment,
          paidAmount: paidAmount,
          monthlyRepayment: monthlyRepayment,
          remainingAmount: remainingAmount,
          interestRate: interestRate,
          expectedEndDate: expectedEndDate,
          status: status,
          loanPeriod: loanPeriod,
          facilitationFee: facilitationFee,
          dailyPenaltyFee: dailyPenaltyFee,
        );

  // Factory constructor to create a LoanDetails instance from JSON
  factory LoanInfo.fromJson(Map<String, dynamic> json) {
    return LoanInfo(
      downPayment: json['downPayment'] != null
          ? DownPayment.fromJson(AppMapper.safeMap(json['downPayment']))
          : null,
      paidAmount: json['paidAmount'] != null
          ? PaidAmount.fromJson(AppMapper.safeMap(json['paidAmount']))
          : null,
      monthlyRepayment:
          AppMapper.safeFormattedNumberWithDecimal(json['monthlyRepayment']),
      remainingAmount: AppMapper.safeString(json['remainingAmount']),
      interestRate: AppMapper.safeString(json['interestRate']),
      expectedEndDate: AppMapper.safeFormattedDate(json['expectedEndDate']),
      status: AppMapper.safeString(json['status']),
      loanPeriod: AppMapper.safeDouble(json['loanPeriod']),
      facilitationFee:
          AppMapper.safeFormattedNumberWithDecimal(json['facilitationFee']),
      dailyPenaltyFee:
          AppMapper.safeFormattedNumberWithDecimal(json['dailyPenaltyFee']),
    );
  }
}

class DownPayment extends DownPaymentEntity {
  DownPayment(
      {String? percentage,
      String? amount,
      String? total,
      String? facilitationFee})
      : super(
            percentage: percentage,
            amount: amount,
            total: total,
            facilitationFee: facilitationFee);

  /// Factory constructor for JSON deserialization
  factory DownPayment.fromJson(Map<String, dynamic> json) {
    // debugPrint("⛑️The downPayment: json data ${json} \n");
    return DownPayment(
      percentage: AppMapper.safeString(json['percentage']),
      amount: AppMapper.safeFormattedNumberWithDecimal(json['amount']),
      total: AppMapper.safeFormattedNumberWithDecimal(json['total']),
      facilitationFee:
          AppMapper.safeFormattedNumberWithDecimal(json['facilitationFee']),
    );
  }
}

class PaidAmount extends PaidAmountEntity {
  PaidAmount({String? amount, double? percentage, String? total})
      : super(
          amount: amount,
          percentage: percentage,
          total: total,
        );

  /// Factory constructor for JSON deserialization
  factory PaidAmount.fromJson(Map<String, dynamic> json) {
    // debugPrint("⛑️The paid amount: json data ${json} \n");

    return PaidAmount(
      amount: AppMapper.safeFormattedNumberWithDecimal(json['amount']),
      percentage: AppMapper.safeDouble(json['percentage']),
      total: AppMapper.safeFormattedNumberWithDecimal(json['total']),
    );
  }
}

class CurrentBill extends CurrentBillEntity {
  CurrentBill({
    String? monthlyRepayment,
    String? repaymentAmount,
    double? penaltyAmount,
    String? totalAmount,
    String? dueDate,
    String? repaymentCode,
    String? facilitationFee,
    String? principalAmount,
    int? daysPastDue,
    bool? isActive,
    bool? isInPenalty,
    PaymentPeriod? paymentPeriod,
  }) : super(
            monthlyRepayment: monthlyRepayment,
            repaymentAmount: repaymentAmount,
            penaltyAmount: penaltyAmount,
            totalAmount: totalAmount,
            dueDate: dueDate,
            repaymentCode: repaymentCode,
            daysPastDue: daysPastDue,
            isActive: isActive,
            isInPenalty: isInPenalty,
            paymentPeriod: paymentPeriod,
            facilitationFee: facilitationFee,
            principalAmount: principalAmount);

  // Factory constructor to create a LoanDetails instance from JSON
  factory CurrentBill.fromJson(Map<String, dynamic> json) {
    debugPrint(" \n\n⛑️The Current bill json: $json \n\n");
    return CurrentBill(
      monthlyRepayment:
          AppMapper.safeFormattedNumberWithDecimal(json['monthlyRepayment']),
      repaymentAmount:
          AppMapper.safeFormattedNumberWithDecimal(json['repaymentAmount']),
      penaltyAmount: AppMapper.safeDouble(json['penaltyAmount']),
      principalAmount:
          AppMapper.safeFormattedNumberWithDecimal(json['principalAmount']),
      totalAmount:
          AppMapper.safeFormattedNumberWithDecimal(json['totalAmount']),
      dueDate: AppMapper.safeFormattedDate(json['dueDate']),
      repaymentCode: AppMapper.safeString(json['repaymentCode']),
      daysPastDue: AppMapper.safeInt(json['daysPastDue']),
      isActive:  
      // true, /// TODO -- uncomment below code
      AppMapper.safeBool(json['isActive']),
      isInPenalty: AppMapper.safeBool(json['isInPenalty']),
      paymentPeriod: json['paymentPeriod'] != null
          ? PaymentPeriod.fromJson(AppMapper.safeMap(json['paymentPeriod']))
          : null,
      facilitationFee: AppMapper.safeString(json['facilitationFee']),
    );
  }
}

class PaymentPeriod extends PaymentPeriodEntity {
  PaymentPeriod({
    int? days,
    String? startDate,
  }) : super(
          days: days,
          startDate: startDate,
        );
  factory PaymentPeriod.fromJson(Map<String, dynamic> json) {
    return PaymentPeriod(
      days: AppMapper.safeInt(json['days']),
      startDate: AppMapper.safeFormattedDate(json['startDate']),
    );
  }
}

class PaymentHistory extends PaymentHistoryEntity {
  PaymentHistory({
    Stats? stats,
    List<Timeline>? timeline,
  }) : super(
          stats: stats,
          timeline: timeline,
        );

  factory PaymentHistory.fromJson(Map<String, dynamic> json) {
    return PaymentHistory(
      stats: json['stats'] != null
          ? Stats.fromJson(AppMapper.safeMap(json['stats']))
          : null,
      timeline: AppMapper.safeList<dynamic>(json['timeline'])
          .map((item) => Timeline.fromJson(AppMapper.safeMap(item)))
          .toList(),
    );
  }
}

class Stats extends StatsEntity {
  Stats({
    int? early,
    int? onTime,
    int? late,
  }) : super(
          early: early,
          onTime: onTime,
          late: late,
        );

  /// Factory constructor to create a `Stats` instance from JSON
  factory Stats.fromJson(Map<String, dynamic> json) {
    return Stats(
      early: AppMapper.safeInt(json['early']),
      onTime: AppMapper.safeInt(json['onTime']),
      late: AppMapper.safeInt(json['late']),
    );
  }
}

class Timeline extends TimelineEntity {
  Timeline({
    String? status,
  }) : super(
          status: status,
        );

  factory Timeline.fromJson(Map<String, dynamic> json) {
    return Timeline(
      status: AppMapper.safeString(json['status']),
    );
  }
}
