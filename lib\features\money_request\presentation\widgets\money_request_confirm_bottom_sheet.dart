import 'package:cbrs/core/common/models/transaction_display_model.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MoneyRequestConfirmBottomSheet extends StatefulWidget {
  /// Creates a customizable money request confirmation bottom sheet with reason field
  ///
  /// [data] contains the transaction details
  /// [transactionType] defines the type of transaction using the string value from TransactionType enum
  /// [onContinue] is called when the user confirms the transaction
  /// [confirmButtonText] allows customizing the confirm button text
  const MoneyRequestConfirmBottomSheet({
    required this.onContinue,
    required this.data,
    required this.reasonController,
    this.transactionType = 'money_request',
    this.confirmButtonText = 'Send Request',
    this.isLoading = false,
    super.key,
  });

  final VoidCallback onContinue;
  final Map<String, dynamic> data;
  final TextEditingController reasonController;
  final String transactionType;
  final String confirmButtonText;
  final bool isLoading;

  @override
  State<MoneyRequestConfirmBottomSheet> createState() =>
      _MoneyRequestConfirmBottomSheetState();
}

class _MoneyRequestConfirmBottomSheetState
    extends State<MoneyRequestConfirmBottomSheet> {
  late TransactionDisplayModel displayModel;

  @override
  void initState() {
    super.initState();
    // Create display model based on transaction type
    displayModel = TransactionDisplayModel.fromData(
      data: widget.data,
      transactionType: widget.transactionType,
    );
  }

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: const ShapeDecoration(
          color: Color(0xFFFCFCFC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(36),
              topRight: Radius.circular(36),
            ),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 64,
                height: 6,
                margin: EdgeInsets.only(
                  top: 16.h,
                  left: 16.w,
                  right: 16.w,
                  bottom: 8,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFBCBCBC),
                  borderRadius: BorderRadius.circular(40),
                ),
              ),
            ),
            // Main content
            Flexible(
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(
                    left: 16.w,
                    right: 16.w,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 8),
                      // Amount and title section
                      _buildHeaderSection(),
                      SizedBox(height: 12.h),
                      // Transaction details section with reason field inside
                      _buildDetailsSection(),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),
            ),
            // Bottom button section
            _buildBottomButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    final amount = (widget.data['totalAmount'] as num?)?.toDouble() ?? 0.0;
    final currency = widget.data['currency'] as String? ?? 'ETB';

    return Container(
      padding: EdgeInsets.only(top: 11.h, bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 100,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                children: [
                  Image.asset(
                    displayModel.imageAsset!,
                    height: 100.h,
                  ),
                  CustomBuildText(
                    text: '$amount $currency',
                    textAlign: TextAlign.center,
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w700,
                  ),
                  SizedBox(height: 8.h),
                  CustomBuildText(
                    text: displayModel.title,
                    textAlign: TextAlign.center,
                    fontSize: 14.sp,
                    caseType: 'default',
                    color: const Color(0xFF6D6D6D),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 10.w,
        vertical: 16.h,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CustomBuildText(
                text: 'Transaction Detail',
                textAlign: TextAlign.center,
                fontSize: 14.sp,
                fontWeight: FontWeight.w700,
                caseType: 'default',
              ),
            ],
          ),
          SizedBox(height: 10.h),
          // Transaction details container
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              spacing: 10,
              children: [
                // All transaction details
                ...displayModel.details.map(
                  (detail) => _buildTransactionList(
                    label: detail.label,
                    value: detail.value,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 14.h),
          // Reason field inside transaction details
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomBuildText(
                  text: 'Request Reason (Optional)',
                  textAlign: TextAlign.left,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  caseType: 'default',
                ),
                SizedBox(height: 8.h),
                TextField(
                  controller: widget.reasonController,
                  decoration: InputDecoration(
                    hintText: 'Enter reason for money request',
                    hintStyle: TextStyle(
                      color: const Color(0xFFAAAAAA),
                      fontSize: 14.sp,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.grey.shade300,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.grey.shade300,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Colors.grey.shade400,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                  ),
                  maxLines: 3,
                  minLines: 1,
                  textCapitalization: TextCapitalization.sentences,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
         
         
          SizedBox(height: 14.h),
          // Total amount summary
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(14),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomBuildText(
                  text: displayModel.summary.label,
                  textAlign: TextAlign.center,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w700,
                  caseType: 'default',
                ),
                CustomBuildText(
                  text: displayModel.summary.value,
                  textAlign: TextAlign.center,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w700,
                  caseType: 'default',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Removed unused _buildReasonField method

  Widget _buildBottomButton() {
    return Container(
      padding: EdgeInsets.only(
        top: 18.h,
        left: 16.w,
        right: 16.w,
        bottom: 20.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 8,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: CustomRoundedBtn(
        btnText: widget.confirmButtonText,
        isLoading: widget.isLoading,
        onTap: widget.onContinue,
      ),
    );
  }

  Widget _buildTransactionList({
    required String label,
    required String value,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomBuildText(
          text: label,
          textAlign: TextAlign.center,
          fontSize: 14.sp,
          caseType: 'default',
        ),
        const SizedBox(width: 20),
        Flexible(
          child: CustomBuildText(
            text: value,
            textAlign: TextAlign.right,
            fontSize: 14.sp,
            caseType: 'default',
          ),
        ),
      ],
    );
  }
}
