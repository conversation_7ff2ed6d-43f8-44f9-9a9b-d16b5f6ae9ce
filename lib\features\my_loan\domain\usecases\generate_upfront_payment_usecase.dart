import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';

class GenerateUpfrontPaymentUsecase
    extends UsecaseWithParams<UpfrontTransactionEntity, String> {
  GenerateUpfrontPaymentUsecase(this._repositiory);
  final LoanRepaymentRepository _repositiory;

  @override
  ResultFuture<UpfrontTransactionEntity> call(String loanId) async {
    return _repositiory.generateUpfrontPayment(loanId: loanId);
  }
}
