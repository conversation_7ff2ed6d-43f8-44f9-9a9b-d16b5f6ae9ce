import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:dartz/dartz.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';

class ValidateTransactionUsecase
    extends UsecaseWithParams<Transaction, String> {
  ValidateTransactionUsecase(this.repository);
  final TransactionRepository repository;

  @override
  ResultFuture<Transaction> call(String id) async {
    return repository.validateTransactions(id);
  }
}
