import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/widgets/loan_document_timeline.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class DocumentForLoanPage extends StatelessWidget {
  const DocumentForLoanPage({
    required this.bank,
    required this.selectedUpfrontPayment,
    required this.loanItem,
    super.key,
  });
  final LoanBank bank;
  final String selectedUpfrontPayment;
  final LoanItem loanItem;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Required Documents',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 4.h),
              const CustomPageHeader(
                pageTitle: 'Required Documents for Loan',
                description:
                    'Please prepare the following documents before applying for a loan',
              ),
              SizedBox(height: 20.h),
              LoanDocumentTimeline(),
              SizedBox(height: 16.h),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
            ),
          ],
        ),
        child: CustomButton(
          text: 'Continue',
          onPressed: () {
            context.pushNamed(
              AppRouteName.loanTermsAndConditions,
              extra: {
                'bank': bank,
                'selectedUpfrontPayment': selectedUpfrontPayment,
                'loanItem': loanItem,
              },
            );
          },
          options: CustomButtonOptions(
            color: theme.primaryColor,
            textStyle: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
