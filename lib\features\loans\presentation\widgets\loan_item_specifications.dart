import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class LoanItemSpecifications extends StatelessWidget {
  const LoanItemSpecifications({
    required this.loanItem,
    super.key,
  });

  final LoanItem loanItem;

  @override
  Widget build(BuildContext context) {
    if (loanItem.specifications.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2C2B34).withValues(alpha: 0.04),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: _buildSpecificationItems(),
      ),
    );
  }

  List<Widget> _buildSpecificationItems() {
    final items = <Widget>[];
    final specs = _getRelevantSpecifications();

    for (var i = 0; i < specs.length; i++) {
      final spec = specs[i];
      items.add(
        _buildSpecItem(
          spec['label']!,
          spec['value']!,
          hasBorder: i < specs.length - 1,
        ),
      );
    }

    return items;
  }

  List<Map<String, String>> _getRelevantSpecifications() {
    final specs = <Map<String, String>>[];

    switch (loanItem.type) {
      case LoanItemType.car:
        specs.addAll(_getCarSpecifications());
        break;
      case LoanItemType.house:
        specs.addAll(_getHouseSpecifications());
        break;
      case LoanItemType.general:
        specs.addAll(_getGeneralSpecifications());
        break;
    }

    return specs;
  }

  List<Map<String, String>> _getCarSpecifications() {
    final specs = <Map<String, String>>[];
    final specifications = loanItem.specifications;

    // Car-specific specifications in order of importance
    final carSpecs = [
      {'key': 'make', 'label': 'MAKE'},
      {'key': 'model', 'label': 'MODEL'},
      {'key': 'year', 'label': 'YEAR'},
      {'key': 'color', 'label': 'COLOR'},
      {'key': 'transmission', 'label': 'TRANSMISSION'},
      {'key': 'engine', 'label': 'ENGINE'},
      {'key': 'fuelType', 'label': 'FUEL TYPE'},
      {'key': 'seats', 'label': 'SEATS'},
      {'key': 'driveTrain', 'label': 'DRIVE TRAIN'},
      {'key': 'horsePower', 'label': 'HORSEPOWER'},
      {'key': 'mileage', 'label': 'MILEAGE'},
      {'key': 'condition', 'label': 'CONDITION'},
    ];

    for (final spec in carSpecs) {
      final value = specifications[spec['key']];
      if (value != null && value.toString().isNotEmpty) {
        specs.add({
          'label': spec['label']!,
          'value': _formatSpecValue(value),
        });
      }
    }

    return specs;
  }

  List<Map<String, String>> _getHouseSpecifications() {
    final specs = <Map<String, String>>[];
    final specifications = loanItem.specifications;

    // House-specific specifications in order of importance
    final houseSpecs = [
      {'key': 'propertyType', 'label': 'PROPERTY TYPE'},
      {'key': 'bedrooms', 'label': 'BEDROOMS'},
      {'key': 'bathrooms', 'label': 'BATHROOMS'},
      {'key': 'area', 'label': 'AREA'},
      {'key': 'parkingLot', 'label': 'PARKING'},
      {'key': 'condition', 'label': 'CONDITION'},
      {'key': 'yearBuilt', 'label': 'YEAR BUILT'},
      {'key': 'floors', 'label': 'FLOORS'},
      {'key': 'furnished', 'label': 'FURNISHED'},
      {'key': 'balcony', 'label': 'BALCONY'},
      {'key': 'garden', 'label': 'GARDEN'},
      {'key': 'security', 'label': 'SECURITY'},
    ];

    for (final spec in houseSpecs) {
      final value = specifications[spec['key']];
      if (value != null && value.toString().isNotEmpty) {
        specs.add({
          'label': spec['label']!,
          'value': _formatSpecValue(value),
        });
      }
    }

    return specs;
  }

  List<Map<String, String>> _getGeneralSpecifications() {
    final specs = <Map<String, String>>[];
    final specifications = loanItem.specifications;

    // For general items, show all available specifications
    specifications.forEach((key, value) {
      if (value != null && value.toString().isNotEmpty) {
        specs.add({
          'label': _formatSpecLabel(key),
          'value': _formatSpecValue(value),
        });
      }
    });

    return specs;
  }

  String _formatSpecLabel(String key) {
    // Convert camelCase to UPPER CASE with spaces
    return key
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .trim()
        .toUpperCase();
  }

  String _formatSpecValue(dynamic value) {
    if (value == null) return 'N/A';

    final stringValue = value.toString();

    // Handle boolean values
    if (value is bool) {
      return value ? 'Yes' : 'No';
    }

    // Handle numeric values with units
    if (value is num) {
      // Add appropriate units based on context
      if (stringValue.contains('.') && value < 100) {
        return '${value.toStringAsFixed(1)}L'; // Likely engine size
      } else if (value > 1000 && value < 10000) {
        return '${value.toInt()} cc'; // Likely engine displacement
      } else if (value > 10000) {
        return '${(value / 1000).toStringAsFixed(0)}k km'; // Likely mileage
      }
    }

    // Capitalize first letter for string values
    if (stringValue.isNotEmpty) {
      return stringValue[0].toUpperCase() + stringValue.substring(1);
    }

    return stringValue;
  }

  Widget _buildSpecItem(String label, String value, {bool hasBorder = true}) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label.toUpperCase(),
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            CustomBuildText(
              text: value,
              fontSize: 14.sp,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ],
        ),
        SizedBox(height: 8.h),
        if (hasBorder)
          Divider(
            color: const Color(0xFF2C2B34).withValues(alpha: 0.4),
          ),
        if (hasBorder) SizedBox(height: 10.h),
      ],
    );
  }
}
