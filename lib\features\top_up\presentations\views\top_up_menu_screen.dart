import 'package:cbrs/core/common/widgets/custom_error_retry.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/top_up/applications/top_up_bloc.dart';
import 'package:cbrs/features/top_up/applications/top_up_event.dart';
import 'package:cbrs/features/top_up/applications/top_up_state.dart';
import 'package:cbrs/features/top_up/presentations/views/top_up_contact_screen.dart';
import 'package:cbrs/features/top_up/presentations/widgets/menu/top_up_menu_cards.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';

class TopUpMenuScreen extends StatefulWidget {
  const TopUpMenuScreen({super.key});

  @override
  State<TopUpMenuScreen> createState() => _TopUpMenuScreenState();
}

class _TopUpMenuScreenState extends State<TopUpMenuScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<TopUpBloc>().add(GetTopUpProviderEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mobile Top-up'),
        titleSpacing: 0,
      ),
      body: BlocBuilder<TopUpBloc, TopUpState>(
        builder: (context, state) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              children: [
                // page title
                const CustomPageHeader(
                  pageTitle: 'Select Operator',
                  description:
                      'Select from the listed network operators and proceed with the top-up.',
                ),
                SizedBox(height: 24.h),

                Expanded(
                  child: state is TopUpLoadingState
                      ? GridView.builder(
                          shrinkWrap: true,
                          padding: const EdgeInsets.all(16),
                          itemCount: 4,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisSpacing: 12,
                            crossAxisSpacing: 8,
                            childAspectRatio: 0.8,
                          ),
                          itemBuilder: (context, index) {
                            return Shimmer.fromColors(
                              baseColor: Colors.grey[200]!,
                              highlightColor: Colors.grey[100]!,
                              child: Container(
                                // width: width,
                                // height: height,
                                decoration: BoxDecoration(
                                  color: Colors.grey,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            );
                          },
                        )
                      : state is FetchedProviderState
                          ? GridView.builder(
                              shrinkWrap: true,
                              padding: const EdgeInsets.all(16),
                              itemCount: state.providers.providers.length,
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                mainAxisSpacing: 12,
                                crossAxisSpacing: 8,
                                childAspectRatio: 0.8,
                              ),
                              itemBuilder: (context, index) {
                                final provider =
                                    state.providers.providers[index];
                                return TopUpMenuCards(
                                  operatorLogo: provider.logo,
                                  operatorName: provider.name,
                                  onTap: () {
                                    context.pushNamed(
                                      AppRouteName.topUpContact,
                                      extra: {
                                        'provider': provider,
                                      },
                                    );
                                  },
                                );
                              },
                            )
                          : CustomErrorRetry(
                              onTap: () {
                                debugPrint('fetch again');
                                context
                                    .read<TopUpBloc>()
                                    .add(GetTopUpProviderEvent());
                              },
                            ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
