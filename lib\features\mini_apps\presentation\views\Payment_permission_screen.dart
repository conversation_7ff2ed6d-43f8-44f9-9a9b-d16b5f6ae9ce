import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:google_fonts/google_fonts.dart';

class PaymentPermissionModal extends StatelessWidget {
  final String appName;
  final String amount;
  final String walletBalance;
  final VoidCallback onNext;

  const PaymentPermissionModal({
    super.key,
    required this.appName,
    required this.amount,
    required this.walletBalance,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(30.r),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Black Line
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(bottom: 24.h),
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // App Name
            Text(
              appName.toUpperCase(),
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                fontWeight: FontWeight.w800,
                letterSpacing: 1.2,
              ),
            ),
            SizedBox(height: 5.h),

            // Amount
            Text(
              amount,
              style: GoogleFonts.outfit(
                fontSize: 32.sp,
                fontWeight: FontWeight.w800,
                color: const Color(0xFF559948),
              ),
            ),
            SizedBox(height: 15.h),

            // Wallet Balance Container
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 24.w,
                vertical: 16.h,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFE6F7E2),
                borderRadius: BorderRadius.circular(100.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Wallet Balance: ',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    walletBalance,
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20.h),

            // Payment Details Section
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 16.h),
              decoration: BoxDecoration(
                color: const Color(0xFFF8FAF9),
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Payment Details',
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildDetailRow('Payer Name:', 'Solomon Kebede Kassa'),
                  SizedBox(height: 12.h),
                  _buildDottedLine(),
                  SizedBox(height: 12.h),
                  _buildDetailRow(
                    'Payment Date:',
                    'May 12, 2024',
                  ),
                ],
              ),
            ),
            SizedBox(height: 32.h),

            // Next Button
            SizedBox(
              width: double.infinity,
              height: 56.h,
              child: ElevatedButton(
                onPressed: onNext,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF559948),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100.r),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Next',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: GoogleFonts.outfit(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildDottedLine() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      height: 1,
      child: CustomPaint(
        painter: DottedLinePainter(
          color: Colors.grey[400]!,
        ),
        size: const Size(double.infinity, 1),
      ),
    );
  }
}
