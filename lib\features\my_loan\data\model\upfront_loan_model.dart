import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/my_loan/data/model/upfront_transaction_model.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_loan_entity.dart';

class UpfrontLoanModel extends UpfrontLoanEntity {
  const UpfrontLoanModel({
    required super.id,
    required super.loanId,
    required super.productId,
    required super.memberId,
    required super.status,
    required super.upfrontPaymentPercentage,
    required super.interestRate,
    required super.upfrontPaymentAmount,
    required super.loanAmount,
    required super.monthlyPayment,
    required super.upfrontPaymentTransaction,
    required super.startDate,
    required super.endDate,
    required super.totalPaid,
    required super.totalPrincipalPaid,
    required super.totalInterestPaid,
    required super.totalPenaltiesPaid,
    required super.remainingBalance,
    required super.totalPayment,
    required super.lastPaymentDate,
    required super.nextPaymentDate,
    required super.isOverdue,
    required super.totalInstallments,
    required super.paidInstallments,
    required super.missedInstallments,
    required super.isAutoRepay,
    required super.createdAt,
    required super.completedDate,
    required super.updatedAt,
    required super.approvedAt,
    required super.approvedBy,
    required super.rejectedBy,
    required super.rejectedAt,
    required super.rejectionReason,
    required super.lastModifiedAt,
    required super.loanApplicationCode,
  });

  factory UpfrontLoanModel.fromJson(Map<String, dynamic> json) {
    return UpfrontLoanModel(
      id: AppMapper.safeString(json['id']),
      loanId: AppMapper.safeString(json['loanId']),
      productId: AppMapper.safeString(json['productId']),
      memberId: AppMapper.safeString(json['memberId']),
      status: AppMapper.safeString(json['status']),
      upfrontPaymentPercentage:
          AppMapper.safeString(json['upfrontPaymentPercentage']),
      interestRate: AppMapper.safeString(json['interestRate']),
      upfrontPaymentAmount: AppMapper.safeString(json['upfrontPaymentAmount']),
      loanAmount: AppMapper.safeString(json['loanAmount']),
      monthlyPayment: AppMapper.safeString(json['monthlyPayment']),
      upfrontPaymentTransaction: UpfrontTransactionModel.fromJson(
          AppMapper.safeMap(json['upfrontPaymentTransaction'])),
      startDate: AppMapper.safeString(json['startDate']),
      endDate: AppMapper.safeString(json['endDate']),
      totalPaid: AppMapper.safeString(json['totalPaid']),
      totalPrincipalPaid: AppMapper.safeString(json['totalPrincipalPaid']),
      totalInterestPaid: AppMapper.safeString(json['totalInterestPaid']),
      totalPenaltiesPaid: AppMapper.safeString(json['totalPenaltiesPaid']),
      remainingBalance: AppMapper.safeString(json['remainingBalance']),
      totalPayment: AppMapper.safeString(json['totalPayment']),
      lastPaymentDate: AppMapper.safeString(json['lastPaymentDate']),
      nextPaymentDate: AppMapper.safeString(json['nextPaymentDate']),
      isOverdue: AppMapper.safeBool(json['isOverdue']),
      totalInstallments: AppMapper.safeInt(json['totalInstallments']),
      paidInstallments: AppMapper.safeInt(json['paidInstallments']),
      missedInstallments: AppMapper.safeInt(json['missedInstallments']),
      isAutoRepay: AppMapper.safeBool(json['isAutoRepay']),
      createdAt: AppMapper.safeString(json['createdAt']),
      completedDate: AppMapper.safeString(json['completedDate']),
      updatedAt: AppMapper.safeString(json['updatedAt']),
      approvedAt: AppMapper.safeString(json['approvedAt']),
      approvedBy: AppMapper.safeString(json['approvedBy']),
      rejectedBy: AppMapper.safeString(json['rejectedBy']),
      rejectedAt: AppMapper.safeString(json['rejectedAt']),
      rejectionReason: AppMapper.safeString(json['rejectionReason']),
      lastModifiedAt: AppMapper.safeString(json['lastModifiedAt']),
      loanApplicationCode: AppMapper.safeString(json['loanApplicationCode']),
    );
  }
}

/*
class UpfrontPaymentDetailsModel extends PaidUpfrontTransaction {
  const UpfrontPaymentDetailsModel({
    required super.loanId,
    required super.status,
    required super.elstRef,
    required super.ftNumber,
    required super.bankCode,
    required super.bankName,
    required super.loanType,
    required super.paidDate,
    required super.senderId,
    required super.billRefNo,
    required super.createdAt,
    required super.billAmount,
    required super.paidAmount,
    required super.senderName,
    required super.senderPhone,
    required super.paymentMethod,
    required super.lastModifiedAt,
    required super.paymentDetails,
    required super.transactionType,
    required super.transactionOwner,
    required super.originalCurrency,
    required super.paymentReference,
    required super.authorizationType,
    required super.beneficiaryId,
    required super.beneficiaryName,
    required super.percentage,
    required super.vat,
    required super.serviceCharge,
    required super.walletFTNumber,
    required super.lastModified,
  });

  factory UpfrontPaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return UpfrontPaymentDetailsModel(
      loanId: AppMapper.safeString(json['loanId']),
      status: AppMapper.safeString(json['status']),
      elstRef: AppMapper.safeString(json['ELSTRef']),
      ftNumber: AppMapper.safeString(json['FTNumber']),
      bankCode: AppMapper.safeString(json['bankCode']),
      bankName: AppMapper.safeString(json['bankName']),
      loanType: AppMapper.safeString(json['loanType']),
      paidDate: AppMapper.safeString(json['paidDate']),
      senderId: AppMapper.safeString(json['senderId']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      createdAt: AppMapper.safeString(json['createdAt']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      paidAmount: AppMapper.safeDouble(json['paidAmount']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      paymentMethod: AppMapper.safeString(json['paymentMethod']),
      lastModifiedAt: AppMapper.safeString(json['lastModifiedAt']),
      paymentDetails: PaymentDetailsModel.fromJson(
          AppMapper.safeMap(json['paymentDetails'])),
      transactionType: AppMapper.safeString(json['transactionType']),
      transactionOwner: AppMapper.safeString(
        json['transactionOwner'] ?? json['TransactionOwner'],
      ),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      paymentReference: AppMapper.safeString(json['paymentReference']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      percentage: AppMapper.safeString(json['percentage']),
      vat: AppMapper.safeDouble(json['VAT']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      walletFTNumber: AppMapper.safeString(json['walletFTNumber']),
      lastModified: AppMapper.safeString(json['lastModified']),
    );
  }
}

class PaymentDetailsModel extends PaymentDetailsEntity {
  const PaymentDetailsModel({
    required super.walletId,
    required super.currency,
  });

  factory PaymentDetailsModel.fromJson(Map<String, dynamic> json) {
    return PaymentDetailsModel(
      walletId: AppMapper.safeString(json['walletId']),
      currency: AppMapper.safeString(json['currency']),
    );
  }
}
*/