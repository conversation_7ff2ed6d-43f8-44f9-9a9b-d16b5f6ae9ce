import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/presentation/widgets/full_screen_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';
import 'package:video_player/video_player.dart';

class LoanItemImageCarousel extends StatefulWidget {
  const LoanItemImageCarousel({
    required this.galleryImages,
    required this.pageController,
    required this.videoControllers,
    required this.onPageChanged,
    this.virtualTour,
    super.key,
  });

  final List<GalleryImage> galleryImages;
  final PageController pageController;
  final Map<int, VideoPlayerController> videoControllers;
  final ValueChanged<int> onPageChanged;
  final String? virtualTour;

  @override
  State<LoanItemImageCarousel> createState() => _LoanItemImageCarouselState();
}

class _LoanItemImageCarouselState extends State<LoanItemImageCarousel> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Main carousel
        Container(
          height: 250.h,
          color: Colors.grey[200],
          child: Stack(
            children: [
              PageView.builder(
                controller: widget.pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                  widget.onPageChanged(index);
                  _handleVideoPlayback(index);
                },
                itemCount: widget.galleryImages.length,
                itemBuilder: (context, index) {
                  final item = widget.galleryImages[index];
                  return _buildCarouselItem(item, index);
                },
              ),

              // Virtual Tour button
              if (widget.virtualTour != null && widget.virtualTour!.isNotEmpty)
                Positioned(
                  bottom: 16.h,
                  right: 16.w,
                  child: Container(
                    width: 44.w,
                    height: 44.h,
                    padding:
                        EdgeInsets.symmetric(horizontal: 7.w, vertical: 7.h),
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                      border: Border.all(color: const Color(0xFF999999)),
                    ),
                    child: GestureDetector(
                      child: const Image(
                        image: AssetImage('assets/vectors/Group.png'),
                      ),
                      onTap: () {
                        context.pushNamed(
                          AppRouteName.virtualTourWebView,
                          queryParameters: {
                            'url': widget.virtualTour,
                          },
                        );
                      },
                    ),
                  ),
                ),

              // Page indicators
              if (widget.galleryImages.length > 1)
                Positioned(
                  bottom: 16.h,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      widget.galleryImages.length,
                      (index) => Container(
                        width: 8.w,
                        height: 8.h,
                        margin: EdgeInsets.symmetric(horizontal: 2.w),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _currentIndex == index
                              ? Theme.of(context).primaryColor
                              : Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCarouselItem(GalleryImage item, int index) {
    if (item.type == 'video') {
      final controller = widget.videoControllers[index];
      if (controller == null) {
        return const Center(child: CircularProgressIndicator());
      }

      return Stack(
        alignment: Alignment.center,
        children: [
          AspectRatio(
            aspectRatio: controller.value.aspectRatio,
            child: VideoPlayer(controller),
          ),
          // Play/Pause button in center
          GestureDetector(
            onTap: () {
              setState(() {
                controller.value.isPlaying
                    ? controller.pause()
                    : controller.play();
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              padding: EdgeInsets.all(12.w),
              child: Icon(
                controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                size: 32.w,
                color: Colors.white,
              ),
            ),
          ),
          if (controller.value.isInitialized)
            Positioned(
              top: 8.h,
              right: 8.w,
              child: GestureDetector(
                onTap: () => _openFullScreenVideo(controller),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(6.r),
                  ),
                  padding: EdgeInsets.all(8.w),
                  child: Icon(
                    Icons.fullscreen,
                    size: 20.w,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      );
    } else {
      return CachedNetworkImage(
        imageUrl: item.url,
        fit: BoxFit.cover,
        placeholder: (context, url) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(color: Colors.white),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[300],
          child: const Icon(Icons.error, color: Colors.red),
        ),
      );
    }
  }

  void _handleVideoPlayback(int index) {
    // Pause all videos except the current one
    for (var i = 0; i < widget.galleryImages.length; i++) {
      final controller = widget.videoControllers[i];
      if (controller != null) {
        if (i == index && widget.galleryImages[i].type == 'video') {
          controller.play();
        } else {
          controller.pause();
        }
      }
    }
  }

  void _openFullScreenVideo(VideoPlayerController controller) {
    // Pause the current video before opening full screen
    controller.pause();

    Navigator.of(context)
        .push(
      MaterialPageRoute<void>(
        builder: (context) => FullScreenVideoPlayer(
          controller: controller,
        ),
      ),
    )
        .then((_) {
      // Resume playing if it was playing before and still the current view
      if (mounted && controller.value.isInitialized) {
        controller.play();
      }
    });
  }
}
