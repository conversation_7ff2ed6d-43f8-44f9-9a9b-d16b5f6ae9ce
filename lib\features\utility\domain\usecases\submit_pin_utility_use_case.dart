import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/utility/domain/entities/utility_success_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:cbrs/core/error/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/features/utility/domain/entities/utility.dart';
import 'package:cbrs/features/utility/domain/repositories/utility_repository.dart';

class SubmitPinUtilityUseCase
    extends UsecaseWithParams<UtilitySuccessEntity, UtilityPinParams> {
  final UtilityRepository _repository;

  const SubmitPinUtilityUseCase(this._repository);

  @override
  ResultFuture<UtilitySuccessEntity> call(UtilityPinParams params) async {
    return _repository.submitPin(
      transactionType: params.transactionType,
      pin: params.pin,
      billRefNo: params.billRefNo
    
     
    );
  }
}

class UtilityPinParams extends Equatable {
  final String transactionType;
  final String pin;
  final String billRefNo;

  const UtilityPinParams({
    required this.transactionType,
    required this.pin,
    required this.billRefNo,
  });

  @override
  List<Object> get props => [transactionType, pin, billRefNo];
}
