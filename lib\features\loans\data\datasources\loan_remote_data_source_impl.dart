import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/loans/data/datasources/loan_remote_data_source.dart';
import 'package:cbrs/features/loans/data/models/loan_application_fee_transaction_model.dart';
import 'package:cbrs/features/loans/data/models/loan_application_model.dart';
import 'package:cbrs/features/loans/data/models/loan_bank_model.dart';
import 'package:cbrs/features/loans/data/models/loan_item_model.dart';
import 'package:cbrs/features/loans/data/models/loan_category_model.dart';
import 'package:cbrs/features/loans/data/models/loan_confirm_otp_response_model.dart';
import 'package:cbrs/features/loans/data/models/loan_confirm_pin_response_model.dart';
import 'package:cbrs/features/loans/data/models/loan_payment_confirmation_model.dart';
import 'package:cbrs/features/loans/data/models/loan_payment_info_model.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/get_paginated_loan_items.dart';
import 'package:flutter/material.dart';

/// Implementation of loan remote data source
class LoanRemoteDataSourceImpl implements LoanRemoteDataSource {
  const LoanRemoteDataSourceImpl({
    required this.apiService,
  });

  final ApiService apiService;

  Future<T> _handleApiResponse<T>(
    T Function(Map<String, dynamic>) parser,
    Future<Result<Map<String, dynamic>>> resultFuture,
  ) async {
    try {
      final result = await resultFuture;

      return result.fold(
        (data) {
          if (data['success'] == false) {
            throw ApiException(
              message: data['message'] as String? ?? 'Operation failed',
              statusCode: 400,
            );
          }
          return parser(data);
        },
        (error) {
          throw ApiException(
            message: error.message,
            statusCode: error.statusCode ?? 500,
          );
        },
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'An unexpected error occurred: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<PaginatedLoanItemsResult> getPaginatedLoanItems({
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
    String? categoryId,
    String? productId,
    String? searchQuery,
  }) async {
    // Determine endpoint and parameters based on loan type
    String endpoint;
    Map<String, dynamic> queryParams = {
      'page': page,
      'limit': limit,
    };

    if (loanType == LoanItemType.car) {
      endpoint = ApiEndpoints.carsPaginate;
      if (categoryId != null) queryParams['bodyType'] = categoryId;
      if (productId != null) queryParams['productId'] = productId;
    } else if (loanType == LoanItemType.house) {
      endpoint = ApiEndpoints.getHouses;
      if (categoryId != null && categoryId.toLowerCase() != 'all') {
        queryParams['type'] = categoryId;
      }
      if (searchQuery != null) queryParams['name'] = searchQuery;
    } else {
      throw const ApiException(
        message: 'Unsupported loan type',
        statusCode: 400,
      );
    }

    return _handleApiResponse<PaginatedLoanItemsResult>(
      (response) {
        List<LoanItemModel> items = [];
        int totalItems = 0;
        int totalPages = 0;

        if (loanType == LoanItemType.car) {
          // Handle car response structure: response.data.data.docs
          if (response['status'] == 200) {
            final data = response['data']?['data'];
            if (data != null && data['docs'] != null) {
              final itemsData = data['docs'] as List;
              items = itemsData
                  .map((item) =>
                      LoanItemModel.carFromJson(item as Map<String, dynamic>))
                  .toList();

              totalItems = (data['totalDocs'] as int?) ?? items.length;
              totalPages = (data['totalPages'] as int?) ?? 1;
            }
          }
        } else if (loanType == LoanItemType.house) {
          // Handle house response structure: response.data.docs
          if (response['success'] == true) {
            final data = response['data'];
            if (data != null && data['docs'] != null) {
              final itemsData = data['docs'] as List;
              items = itemsData
                  .map((item) =>
                      LoanItemModel.houseFromJson(item as Map<String, dynamic>))
                  .toList();

              totalItems = (data['totalDocs'] as int?) ?? items.length;
              totalPages = (data['totalPages'] as int?) ?? 1;
            }
          }
        }

        return PaginatedLoanItemsResult(
          items: items,
          currentPage: page,
          totalPages: totalPages,
          totalItems: totalItems,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        );
      },
      apiService.get(
        endpoint,
        queryParameters: queryParams,
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<LoanItemModel> getLoanItemById({
    required String itemId,
    required LoanItemType loanType,
  }) async {
    String endpoint;
    Map<String, dynamic> queryParams = {};

    if (loanType == LoanItemType.car) {
      endpoint = ApiEndpoints.getCarById;
      queryParams['id'] = itemId;
    } else if (loanType == LoanItemType.house) {
      // Assuming there's a house by ID endpoint
      endpoint = '${ApiEndpoints.getHouses}/$itemId';
    } else {
      throw const ApiException(
        message: 'Unsupported loan type',
        statusCode: 400,
      );
    }

    return _handleApiResponse<LoanItemModel>(
      (response) {
        if (loanType == LoanItemType.car) {
          if (response['status'] == 200) {
            final data = response['data'];
            if (data != null) {
              return LoanItemModel.carFromJson(data as Map<String, dynamic>);
            }
          }
        } else if (loanType == LoanItemType.house) {
          if (response['success'] == true) {
            final data = response['data'];
            if (data != null) {
              return LoanItemModel.houseFromJson(data as Map<String, dynamic>);
            }
          }
        }

        throw const ApiException(
          message: 'Invalid response format',
          statusCode: 500,
        );
      },
      apiService.get(
        endpoint,
        queryParameters: queryParams,
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<List<LoanBankModel>> getLoanBanks({
    required String productId,
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
  }) async {
    final loanTypeString = _getLoanTypeString(loanType);

    return _handleApiResponse<List<LoanBankModel>>(
      (data) {
        if (data['data'] == null || data['data']['banks'] == null) {
          throw const ApiException(
            message: 'Invalid response format',
            statusCode: 500,
          );
        }

        final banksData = data['data']['banks'] as List;
        return banksData
            .map((bank) => LoanBankModel.fromJson(
                  bank as Map<String, dynamic>,
                  _getLoanBankType(loanType),
                ))
            .toList();
      },
      apiService.get(
        ApiEndpoints.carLoanBanks,
        queryParameters: {
          'page': page,
          'limit': limit,
          'loanType': loanTypeString,
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<LoanPaymentInfoModel> getLoanPaymentInfo({
    required String productId,
    required String bankId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
  }) async {
    final loanTypeString = _getLoanTypeString(loanType);
    final queryParams = {
      'productId': productId,
      'bankId': bankId,
      'upfrontPaymentPercentage': upfrontPaymentPercentage,
      'loanType': loanTypeString,
    };

    return _handleApiResponse<LoanPaymentInfoModel>(
      (data) {
        if (data['success'] == true) {
          return LoanPaymentInfoModel.fromJson(
            data['data'] as Map<String, dynamic>,
          );
        }
        throw ApiException(
          message: (data['message'] as String?) ??
              'Failed to calculate loan payment',
          statusCode: 400,
        );
      },
      apiService.get(
        ApiEndpoints.carLoansPaymentInfo,
        queryParameters: queryParams,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<Map<String, dynamic>> calculateLoanPayment({
    required String bankId,
    required String loanProductId,
    required String upfrontPaymentPercentage,
    required String productId,
    required double interestRate,
    required LoanItemType loanType,
  }) async {
    final loanTypeString = _getLoanTypeString(loanType);
    final queryParams = {
      'productId': productId,
      'bankId': bankId,
      'upfrontPaymentPercentage': upfrontPaymentPercentage,
      'loanType': loanTypeString,
    };

    return _handleApiResponse<Map<String, dynamic>>(
      (data) {
        if (data['success'] == true) {
          final paymentData = data['data'] as Map<String, dynamic>;

          // Extract payment details from the nested structure
          final paymentDetails =
              paymentData['paymentDetails'] as Map<String, dynamic>? ?? {};
          final loanDetails =
              paymentData['loanDetails'] as Map<String, dynamic>? ?? {};
          final productDetails =
              paymentData['productDetails'] as Map<String, dynamic>? ?? {};

          return {
            'monthlyPayment':
                paymentDetails['monthlyPayment']?.toString() ?? 'TBD',
            'totalAmount': paymentDetails['totalPayment']?.toString() ?? 'TBD',
            'interestRate': paymentDetails['interestRate']?.toString() ??
                interestRate.toString(),
            'processingFee':
                paymentDetails['upfrontFacilitationFee']?.toString() ?? 'TBD',
            'downPaymentAmount':
                paymentDetails['upfrontPaymentAmount']?.toString() ?? 'TBD',
            'loanTermMonths': loanDetails['loanPeriod']?.toString() ?? '0',
            'bankName': productDetails['name']?.toString() ?? '',
            'upfrontPercentage': upfrontPaymentPercentage,
          };
        }
        throw ApiException(
          message: (data['message'] as String?) ??
              'Failed to calculate loan payment',
          statusCode: 400,
        );
      },
      apiService.get(
        ApiEndpoints.carLoansPaymentInfo, // Use the working endpoint
        queryParameters: queryParams,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<LoanApplicationModel> applyForLoan({
    required String loanId,
    required String productId,
    required String upfrontPaymentPercentage,
    required LoanItemType loanType,
    bool isAutoRepay = false,
  }) async {
    final loanTypeString = _getLoanTypeString(loanType);

    return _handleApiResponse<LoanApplicationModel>(
      (response) {
        if (response['success'] == true) {
          final data = response['data'];
          if (data == null) {
            throw const ApiException(
              message: 'Invalid response format',
              statusCode: 500,
            );
          }

          return _createLoanApplicationModel(
            data as Map<String, dynamic>,
            loanType,
          );
        }

        throw ApiException(
          message:
              (response['message'] as String?) ?? 'Failed to apply for loan',
          statusCode: 400,
        );
      },
      apiService.post(
        ApiEndpoints.carLoansApply, // Same endpoint for both types
        data: {
          'loanId': loanId,
          'productId': productId,
          'upfrontPaymentPercentage': upfrontPaymentPercentage,
          'isAutoRepay': isAutoRepay,
          'loanType': loanTypeString,
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<LoanApplicationFeeTransactionModel> generateApplicationTransaction({
    required String loanApplicationId,
    required LoanItemType loanType,
  }) async {
    debugPrint('Generating application transaction for ID: $loanApplicationId');

    final result = await apiService.post(
      ApiEndpoints.generateApplicationTransaction(loanApplicationId),
      parser: (data) {
        debugPrint('Raw API response: $data');

        // Extract the data field from the response
        final response = data as Map<String, dynamic>;
        final responseData = response['data'] as Map<String, dynamic>;
        debugPrint('Parsing transaction data: $responseData');

        return LoanApplicationFeeTransactionModel.fromJson(responseData);
      },
    );

    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<String> getLoanTerms({
    required String bankId,
    required LoanItemType loanType,
  }) async {
    return _handleApiResponse<String>(
      (response) {
        if (response['success'] == true) {
          final data = response['data'] as Map<String, dynamic>? ?? {};
          return data['content'] as String? ?? '';
        }
        throw const ApiException(
          message: 'Failed to fetch loan terms',
          statusCode: 400,
        );
      },
      apiService.get(
        ApiEndpoints.getLoanTerms(bankId),
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<LoanConfirmPinResponseModel> confirmWithPin({
    required String billRefNo,
    required String pin,
    required String transactionType,
  }) async {
    final postData = {
      'PIN': pin,
      'billRefNo': billRefNo,
      'transactionType': transactionType,
    };

    final result = await apiService.post(
      ApiEndpoints.acceptOrRejectOrCancelRequest,
      data: postData,
      parser: (data) => data as Map<String, dynamic>,
    );

    return result.fold((success) {
      return LoanConfirmPinResponseModel.fromJson(success);
    }, (error) {
      throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 400,
      );
    });
  }

  @override
  Future<LoanConfirmOtpResponseModel> confirmWithOtp({
    required String billRefNo,
    required String otpCode,
    required String transactionType,
  }) async {
    final postData = {
      'otpCode': otpCode,
      'billRefNo': billRefNo,
      'transactionType': transactionType,
    };

    final result = await apiService.post(
      ApiEndpoints.acceptOrRejectOrCancelRequest,
      data: postData,
      parser: (data) => data as Map<String, dynamic>,
    );

    return result.fold((success) {
      return LoanConfirmOtpResponseModel.fromJson(success);
    }, (error) {
      throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 400,
      );
    });
  }

  @override
  Future<String> resendOtp({
    required String otpFor,
    required String billRefNo,
  }) async {
    final postData = {
      'otpFor': otpFor,
      'billRefNo': billRefNo,
    };
    final result = await apiService.post(
      ApiEndpoints.acceptOrRejectOrCancelRequest,
      data: postData,
      parser: (data) => data,
    );

    return result.fold((onSuccess) {
      return 'confirm';
    }, (error) {
      throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 400,
      );
    });
  }

  @override
  Future<LoanPaymentConfirmationModel> confirmLoanPayment({
    required String transactionType,
    required String billRefNo,
    required String pin,
  }) async {
    debugPrint('Confirming loan payment with PIN for billRefNo: $billRefNo');

    final postData = {
      'transactionType': transactionType,
      'billRefNo': billRefNo,
      'pin': pin,
    };

    final result = await apiService.post(
      ApiEndpoints.confirmLoanPayment,
      data: postData,
      parser: (data) {
        debugPrint('Raw loan payment confirmation response: $data');

        // Extract the data field from the response
        final response = data as Map<String, dynamic>;
        final responseData = response['data'] as Map<String, dynamic>;
        debugPrint('Parsing loan payment confirmation data: $responseData');

        return LoanPaymentConfirmationModel.fromJson(responseData);
      },
    );

    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<List<LoanCategoryModel>> getLoanCategories({
    int page = 1,
    int limit = 10,
    String? loanType,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (loanType != null) 'loanType': loanType,
      };

      // Use appropriate endpoint based on loan type
      String endpoint;
      if (loanType == 'mortgage') {
        endpoint = ApiEndpoints.getHouseTypes;
      } else if (loanType == 'car_loan') {
        endpoint = ApiEndpoints.carBodiesPaginate;
      } else {
        // Default to car bodies for general loans
        endpoint = ApiEndpoints.carBodiesPaginate;
      }

      final result = await apiService.get(
        endpoint,
        queryParameters: queryParams,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        try {
          var categories = <LoanCategoryModel>[];

          // Handle different response structures based on endpoint
          if (loanType == 'mortgage') {
            // House types API response structure: data.docs
            if (success['data'] != null) {
              final dataWrapper = success['data'] as Map<String, dynamic>;

              if (dataWrapper['docs'] != null && dataWrapper['docs'] is List) {
                final docsJson = dataWrapper['docs'] as List<dynamic>;
                categories = docsJson
                    .map(
                      (json) => LoanCategoryModel.fromJson(
                        json as Map<String, dynamic>,
                      ),
                    )
                    .toList();
              }
            }
          } else {
            // Car bodies API structure: data.data.docs
            if (success['data'] != null) {
              final dataWrapper = success['data'] as Map<String, dynamic>;

              if (dataWrapper['data'] != null) {
                final innerData = dataWrapper['data'] as Map<String, dynamic>;

                if (innerData['docs'] != null && innerData['docs'] is List) {
                  final docsJson = innerData['docs'] as List<dynamic>;
                  categories = docsJson
                      .map(
                        (json) => LoanCategoryModel.fromJson(
                          json as Map<String, dynamic>,
                        ),
                      )
                      .toList();
                }
              }
            }
          }

          // Add "All" category at the beginning if we have API data
          if (categories.isNotEmpty) {
            final allCategory = _getAllCategory(loanType);
            return [allCategory, ...categories];
          }

          // If no categories found, throw exception instead of fallback
          throw const ApiException(
            message: 'No categories found',
            statusCode: 404,
          );
        } catch (e) {
          // If parsing fails, throw exception instead of fallback
          throw ApiException(
            message: 'Failed to parse categories data: $e',
            statusCode: 500,
          );
        }
      }, (error) {
        // If API call fails, throw exception instead of fallback
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        );
      });
    } catch (e) {
      // If anything fails, throw exception instead of fallback
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to fetch categories: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<LoanCategoryModel> getLoanCategoryById({
    required String categoryId,
  }) async {
    try {
      final result = await apiService.get(
        '${ApiEndpoints.carBodiesPaginate}/$categoryId',
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        try {
          final data = success['data'];
          if (data is Map<String, dynamic>) {
            return LoanCategoryModel.fromJson(data);
          }
          throw const ApiException(
            message: 'Invalid category data format',
            statusCode: 500,
          );
        } catch (e) {
          if (e is ApiException) {
            rethrow;
          }
          throw ApiException(
            message: 'Failed to parse category data: $e',
            statusCode: 500,
          );
        }
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        );
      });
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to fetch category: $e',
        statusCode: 500,
      );
    }
  }

  LoanCategoryModel _getAllCategory(String? loanType) {
    // Return appropriate "All" category based on loan type with asset icons
    if (loanType == 'car_loan') {
      return LoanCategoryModel.fromCarBodyType(
        id: '',
        name: 'All Cars',
        description: 'All available cars',
        icon: 'assets/images/car.png',
      );
    } else if (loanType == 'mortgage') {
      return LoanCategoryModel.fromHouseType(
        id: '',
        name: 'All Houses',
        icon: 'assets/vectors/all-mortgage-list.png',
        description: 'All available properties',
      );
    } else {
      return const LoanCategoryModel(
        id: '',
        name: 'All Loans',
        description: 'All available loans',
        icon: 'assets/images/car.png', // Default to car icon
      );
    }
  }

  /// Convert LoanItemType enum to string for API calls
  String _getLoanTypeString(LoanItemType loanType) {
    switch (loanType) {
      case LoanItemType.car:
        return 'car';
      case LoanItemType.house:
        return 'mortgage';
      case LoanItemType.general:
        return 'general';
    }
  }

  /// Convert LoanItemType enum to LoanBankType for model creation
  LoanBankType _getLoanBankType(LoanItemType loanType) {
    switch (loanType) {
      case LoanItemType.car:
        return LoanBankType.car;
      case LoanItemType.house:
        return LoanBankType.mortgage;
      case LoanItemType.general:
        return LoanBankType.general;
    }
  }

  /// Create appropriate loan application model based on loan type
  LoanApplicationModel _createLoanApplicationModel(
    Map<String, dynamic> data,
    LoanItemType loanType,
  ) {
    switch (loanType) {
      case LoanItemType.car:
        return LoanApplicationModel.car(data);
      case LoanItemType.house:
        return LoanApplicationModel.mortgage(data);
      case LoanItemType.general:
        // For general loans, default to car model structure
        // This can be updated when general loan model is implemented
        return LoanApplicationModel.car(data);
    }
  }
}
