import 'package:equatable/equatable.dart';

class UpfrontPayment extends Equatable {
  final String percentage;
  final double interestRate;

  const UpfrontPayment({
    required this.percentage,
    required this.interestRate,
  });

  @override
  List<Object> get props => [percentage, interestRate];
}

class LoanProduct extends Equatable {
  final String id;
  final String loanType;
  final int loanPeriod;
  final List<UpfrontPayment> upfrontPaymentOptions;
  final String facilitationRate;
  final String applicationFee;

  const LoanProduct({
    required this.id,
    required this.loanType,
    required this.loanPeriod,
    required this.upfrontPaymentOptions,
    required this.facilitationRate,
    required this.applicationFee,
  });

  @override
  List<Object> get props => [
        id,
        loanType,
        loanPeriod,
        upfrontPaymentOptions,
        facilitationRate,
        applicationFee,
      ];
}

/// Unified bank entity for all loan types
class LoanBank extends Equatable {
  final String id;
  final String name;
  final String? logo;
  final List<LoanProduct> loans;
  final LoanBankType type;

  const LoanBank({
    required this.id,
    required this.name,
    this.logo,
    required this.loans,
    required this.type,
  });

  /// Factory for car loan banks
  factory LoanBank.carLoan({
    required String id,
    required String name,
    String? logo,
    required List<LoanProduct> loans,
  }) {
    return LoanBank(
      id: id,
      name: name,
      logo: logo,
      loans: loans,
      type: LoanBankType.car,
    );
  }

  /// Factory for mortgage loan banks
  factory LoanBank.mortgage({
    required String id,
    required String name,
    String? logo,
    required List<LoanProduct> loans,
  }) {
    return LoanBank(
      id: id,
      name: name,
      logo: logo,
      loans: loans,
      type: LoanBankType.mortgage,
    );
  }

  /// Check if this is a car loan bank
  bool get isCarLoanBank => type == LoanBankType.car;

  /// Check if this is a mortgage loan bank
  bool get isMortgageLoanBank => type == LoanBankType.mortgage;

  @override
  List<Object?> get props => [id, name, logo, loans, type];
}

enum LoanBankType {
  car,
  mortgage,
  general,
}
