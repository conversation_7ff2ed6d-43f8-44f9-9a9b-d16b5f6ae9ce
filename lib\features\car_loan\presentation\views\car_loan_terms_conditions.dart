import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/car_loan/application/bloc/car_loan_bloc.dart';
import 'package:cbrs/features/car_loan/data/models/car_bank_model.dart';
import 'package:cbrs/features/car_loan/data/models/car_loan_application_model.dart';
import 'package:cbrs/features/car_loan/data/models/car_model.dart';
import 'package:cbrs/features/car_loan/domain/entities/application_fee_transaction.dart';
import 'package:cbrs/features/car_loan/presentation/widgets/terms_and_conditions_container.dart';
import 'package:cbrs/features/user/domain/entities/user.dart';
import 'package:cbrs/features/user/presentation/bloc/user_bloc.dart';
import 'package:cbrs/features/user/presentation/bloc/user_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class CarLoanTermsConditions extends StatefulWidget {
  const CarLoanTermsConditions({
    required this.bank,
    required this.selectedUpfrontPayment,
    required this.carData,
    super.key,
  });
  final CarBankModel bank;
  final String selectedUpfrontPayment;
  final CarModel carData;

  @override
  State<CarLoanTermsConditions> createState() => _CarLoanTermsConditionsState();
}

class _CarLoanTermsConditionsState extends State<CarLoanTermsConditions> {
  @override
  void initState() {
    super.initState();
    context.read<CarLoanBloc>().add(
          FetchCarLoanTermsEvent(bankId: widget.bank.id),
        );
  }

  String appliedLoanPeriod = '';
  @override
  void dispose() {
    super.dispose();
  }

  bool isCarLoanFetched = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Loan Terms & Conditions',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: BlocListener<CarLoanBloc, CarLoanState>(
        listener: (context, state) {
          if (state is CarLoanApplied) {
            _showConfirmLoanBottomSheet(application: state.application);
          } else if (state is CarLoanApplicationGenerated) {}
          // Pass the state to _handleLoanSuccess to handle all cases
          // _handleLoanSuccess(state);
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const CustomPageHeader(
                      pageTitle: 'Our Terms & Condition',
                      description:
                          'Read our terms and conditions carefully before your '
                          'continue with your application.',
                    ),
                    SizedBox(height: 16.h),
                    _buildBankCard(context),
                    SizedBox(height: 12.h),
                    const Expanded(child: TermsAndConditionsContainer()),
                  ],
                ),
              ),
            ),
            BlocConsumer<CarLoanBloc, CarLoanState>(
              listener: (context, state) {
                if (state is CarLoanApplyError ||
                    state is CarLoanApplicationError) {
                  final errorMessage = state is CarLoanApplyError
                      ? state.message
                      : (state as CarLoanApplicationError).message;

                  CustomToastification(
                    context,
                    message: errorMessage,
                  );
                }

                if (state is CarLoanTermsFetched) {
                  setState(() {
                    isCarLoanFetched = true;
                  });
                }
              },
              builder: (context, state) {
                final isLoading = state is CarLoanApplying ||
                    state is CarLoanApplicationGenerating;

                return Padding(
                  padding:
                      const EdgeInsets.only(bottom: 24, left: 16, right: 16),
                  child: CustomRoundedBtn(
                    btnText: isLoading ? 'Processing...' : 'Apply Now',
                    onTap:
                        isCarLoanFetched && isLoading ? null : handleApplyNow,
                    isLoading: isLoading,
                    isBtnActive: isCarLoanFetched,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void handleApplyNow() {
    context.read<CarLoanBloc>().add(
          ApplyCarLoanEvent(
            loanId: widget.bank.loans.first.id,
            productId: widget.carData.id,
            upfrontPaymentPercentage: widget.selectedUpfrontPayment,
          ),
        );
  }

  void _handleLoanSuccess(
    CarLoanApplicationFeeTransaction transaction,
    CarLoanApplicationModel application,
  ) {
    // Find the interest rate for the selected upfront payment
    var interestRate = 0.0;
    for (final option in widget.bank.loans.first.upfrontPaymentOptions) {
      if (option.percentage == widget.selectedUpfrontPayment) {
        interestRate = option.interestRate;
        break;
      }
    }

    /*
    Transaction Type 
Loan Type 
Loan Amount 
Car Model 
House Type ( if mortgage )
House Name ( if mortgage )
Loan Period 
Down Payment 
Interest Rate 
Application Fee 
Transaction Ref.
Date
Total Amou
 */

    // Prepare data for the success bottom sheet - use flat structure
    final successData = {
      'Loan Type': 'Car Loan',
      'Loan Amount': '${AppMapper.safeFormattedNumberWithDecimal(
        transaction.transactionDetails.billAmount,
      )} USD',
      'Car Model': widget.carData.model,
      'Car Price':
          '${AppMapper.safeFormattedNumberWithDecimal(transaction.carDetails.price)} USD',
      'Loan Period': '$appliedLoanPeriod Years',
      'Payment Ref.': transaction.transactionDetails.billRefNo,
      'createdAt':
          AppMapper.safeFormattedDate(transaction.transactionDetails.date),
      'Bank Name': widget.bank.name,
      'Down Paymemnt':
          '${AppMapper.safeFormattedNumberWithDecimal(transaction.paymentDetails.upfrontPaymentAmount)} USD',
      'Down Paymemnt Percentage':
          '${transaction.paymentDetails.upfrontPaymentAmount} %',
      'Interest Rate': '${application.paymentDetails.interestRate} %',
      'Application Fee': '${application.loanDetails.applicationFee} USD',
      'Interest Rate': '${application.paymentDetails.interestRate} %',
    };

    // Show custom success bottom sheet
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) {
        return CustomSuccessTransactionBottomSheet(
          data: successData,
          totalAmount: transaction.transactionDetails.billAmount,
          billAmount: transaction.transactionDetails.billAmount,
      transactionId: 'transaction.id', // TODO
      billRefNo: transaction.transactionDetails.billRefNo,

          originalCurrency: 'USD',
          transactionType: 'car_loan',
          onContinue: () {
            // Navigate back to home screen
            context.goNamed(AppRouteName.home);
          },
          isFromChat: false,
        );
      },
    );
  }

  void _showConfirmLoanBottomSheet({
    required CarLoanApplicationModel application,
  }) {
    setState(() {
      appliedLoanPeriod = application.loanDetails.loanPeriod.toString();
    });
    // Find the interest rate for the selected upfront payment
    var interestRate = 0.0;
    for (final option in widget.bank.loans.first.upfrontPaymentOptions) {
      if (option.percentage == widget.selectedUpfrontPayment) {
        interestRate = option.interestRate;
        break;
      }
    }

    final loanAmount = widget.carData.price *
        (1 - (double.parse(widget.selectedUpfrontPayment) / 100));
    final upfrontAmount = widget.carData.price *
        (double.parse(widget.selectedUpfrontPayment) / 100);
    final applicationFee = double.parse(widget.bank.loans.first.applicationFee);

    // Create data map for confirmation
    final data = {
      'Loan type': 'Car',
      'Loan Amount': '${AppMapper.safeFormattedNumberWithDecimal(
        application.paymentDetails.loanAmount,
      )} USD',

      //  '${AppMapper.safeFormattedNumberWithDecimal(loanAmount)} USD',
      'Car Model': widget.carData.model,
      'Car Price':
          '${AppMapper.safeFormattedNumberWithDecimal(application.productDetails.price)} USD',
      //  '${AppMapper.safeFormattedNumberWithDecimal(application.productDetails.price)} USD',
      //'${AppMapper.safeFormattedNumberWithDecimal(widget.carData.price)} USD',
      'Loan Period': '$appliedLoanPeriod Years',
      'Bank Name': widget.bank.name,
      'Down Payment':
          '${AppMapper.safeFormattedNumberWithDecimal(application.paymentDetails.upfrontPaymentAmount)} USD',
      'Upfront Payment Percentage':
          '${application.paymentDetails.upfrontPaymentPercentage} %',
      'Interest Rate': '${application.paymentDetails.interestRate} %',
      'Application Fee': '${application.loanDetails.applicationFee} USD',
    };

    debugPrint('Applicae tion ### ${application.id}');
    final carLoanBloc = BlocProvider.of<CarLoanBloc>(context);

    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      // isDismissible: false, // Prevent dismissal during processing
      // enableDrag: false, // Prevent dragging during processing
      builder: (bottomSheetContext) {
        // Create a new BlocProvider that provides the CarLoanBloc to the bottom sheet
        return BlocProvider.value(
          value: carLoanBloc,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.9,
            ),
            child: BlocConsumer<CarLoanBloc, CarLoanState>(
              listener: (context, state) {
                if (state is CarLoanApplicationGenerationError) {
                  CustomToastification(context, message: state.message);
                }
                if (state is CarLoanApplicationGenerated) {
                  Navigator.pop(context);
                  _handleLoanSuccess(state.transaction, application);
                }
              },
              builder: (context, state) {
                return CustomConfirmTransactionBottomSheet(
                  data: data,
                  totalAmount: applicationFee,
                  billAmount: applicationFee,

                  transactionType: 'car_loan',
                  confirmButtonText: 'Confirm Application',
                  originalCurrency: 'USD', // TODO
                  isLoading: state is CarLoanApplicationGenerating,
                  isBtnActive: state is! CarLoanApplicationGenerating,
                  onContinue: () {
                    if (state is CarLoanApplicationGenerating) return;

                    // Apply for the loan
                    context.read<CarLoanBloc>().add(
                          GenerateCarLoanApplicationEvent(
                            loanApplicationId: application.id,
                          ),
                        );
                  },
                );
              },
            ),
          ),
        );
      },
    );

  }

  Widget _buildBankCard(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Image.network(
            widget.bank.logo ?? '',
            width: 32.w,
            height: 32.h,
            errorBuilder: (context, error, stackTrace) =>
                Icon(Icons.account_balance, size: 32.w),
          ),
          SizedBox(width: 12.w),
          Text(
            widget.bank.name,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// A stateful widget for the confirm bottom sheet content
class _ConfirmBottomSheetContent extends StatefulWidget {
  const _ConfirmBottomSheetContent({
    required this.data,
    required this.bank,
    required this.carData,
    required this.selectedUpfrontPayment,
    required this.getWalletBalance,
    required this.bottomSheetContext,
  });
  final Map<String, dynamic> data;
  final CarBankModel bank;
  final CarModel carData;
  final String selectedUpfrontPayment;
  final double Function(BuildContext) getWalletBalance;
  final BuildContext bottomSheetContext;

  @override
  State<_ConfirmBottomSheetContent> createState() =>
      _ConfirmBottomSheetContentState();
}

class _ConfirmBottomSheetContentState
    extends State<_ConfirmBottomSheetContent> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<CarLoanBloc, CarLoanState>(
      listener: (context, state) {
        if (state is CarLoanApplying) {
          setState(() => isLoading = true);
        } else if (state is CarLoanApplied) {
          setState(() => isLoading = false);
          // Close the bottom sheet after success
          Navigator.pop(widget.bottomSheetContext);
        } else if (state is CarLoanApplyError) {
          setState(() => isLoading = false);
          CustomToastification(
            context,
            message: state.message,
          );
        }
      },
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(),
        /* 
        ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
          ),
          child: CustomConfirmTransactionBottomSheet(
            data: widget.data,
            transactionType: 'car_loan',
            confirmButtonText: 'Confirm Application',
            isLoading: isLoading,
            originalCurrency: 'USD', // TODO
            onContinue: () {
              if (isLoading) return; // Prevent multiple submissions

              // Apply for the loan directly without showing PIN screen
              final walletBalance = widget.getWalletBalance(context);
              final applicationFee =
                  double.parse(widget.bank.loans.first.applicationFee);

              if (walletBalance < applicationFee) {
                CustomToastification(
                  context,
                  message: 'Insufficient balance to proceed with application',
                );
                return;
              }

              // Apply for the loan
              context.read<CarLoanBloc>().add(
                    ApplyCarLoanEvent(
                      loanId: widget.bank.loans.first.id,
                      productId: widget.carData.id,
                      upfrontPaymentPercentage: widget.selectedUpfrontPayment,
                    ),
                  );
            },
          ),
        ),
   */
      ),
    );
  }
}
