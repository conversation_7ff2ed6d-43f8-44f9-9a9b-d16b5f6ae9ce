import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_wallet_balance.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_bloc.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_event.dart';
import 'package:cbrs/features/my_loan/application_layer/bloc/my_loan_state.dart';
import 'package:cbrs/core/utils/format_date_in_month.dart';
import 'package:cbrs/features/my_loan/presentation/views/screen_success_repay_page.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/build_congratulation.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/build_referance_section.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/features/my_loan/presentation/widgets/custom_app_bar.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/user/domain/entities/user.dart';
import 'package:cbrs/features/user/presentation/bloc/user_bloc.dart';
import 'package:cbrs/features/user/presentation/bloc/user_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:googleapis/cloudsearch/v1.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';

class ScreenConfirmRepayPage extends StatefulWidget {
  const ScreenConfirmRepayPage({
    required this.loanApplication,
    required this.loanId,
    required this.loanType,
    required this.months,
    required this.isRepayment,
    super.key,
    this.isAgent = false,
    this.pageAppBar = 'Loan Repayment',
    this.pageTitle = 'Confirm Repayment',
    this.pageDescription =
        'Proceed to pay the Mortgage Payment to finalize your payment.',
  });
  final String loanId;
  final String loanType;
  final String pageTitle;
  final String pageDescription;
  final String pageAppBar;
  final bool isAgent;
  final bool isRepayment;
  final String months;
  final LoanRepaymentDataEntity loanApplication;

  @override
  State<ScreenConfirmRepayPage> createState() => _ScreenConfirmRepayPageState();
}

class _ScreenConfirmRepayPageState extends State<ScreenConfirmRepayPage> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<RepaymentBloc>(),
      child: ScreenConfrimRepayView(
        isAgent: widget.isAgent,
        pageAppBar: widget.pageAppBar,
        pageTitle: widget.pageTitle,
        pageDescription: widget.pageDescription,
        loanApplication: widget.loanApplication,
        loanId: widget.loanId,
        loanType: widget.loanType,
        isRepayment: widget.isRepayment,
        months: widget.months,
      ),
    );
  }
}

class ScreenConfrimRepayView extends StatelessWidget {
  const ScreenConfrimRepayView({
    required this.loanApplication,
    required this.loanId,
    required this.loanType,
    required this.isRepayment,
    required this.months,
    super.key,
    this.isAgent = false,
    this.pageAppBar = 'Loan Repayment',
    this.pageTitle = 'Confirm Repayment',
    this.pageDescription =
        'Proceed to pay the Mortgage Payment to finalize your payment.',
  });
  final String pageTitle;
  final String pageDescription;
  final String pageAppBar;
  final String loanId;
  final String loanType;
  final String months;
  final bool isAgent;
  final bool isRepayment;
  final LoanRepaymentDataEntity loanApplication;

  double _getWalletBalance(BuildContext context) {
    final userState = context.read<UserBloc>().state;
    if (userState is UserLoaded) {
      final usdWallet = userState.user.wallets.firstWhere(
        (w) => w.currency == 'USD',
        orElse: () => Wallet(currency: 'USD', balance: 0, walletCode: ''),
      );
      return usdWallet.balance;
    }
    return 0;
  }

  Future<String> getUserName() async {
    // var userId = await _authBox.get('userId') ?? '';
    final user = await sl<AuthLocalDataSource>().getCachedUserData();

    debugPrint('y user id::: ${user?.id}');
    final fullName = '${user?.firstName ?? ''} ${user?.lastName ?? ''}';
    return fullName;
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.sizeOf(context);
    final isSmallScreen = screenSize.width < 360;
    final theme = Theme.of(context);
    final userName = getUserName();

    return BlocConsumer<RepaymentBloc, RepaymentLoanState>(
      listener: (context, state) {
        if (state is RepaymentLoadingState) {
          // customToast(context, message: '${state.message}');
        } else if (state is RepaymentPayState) {
          debugPrint('Pepayment paid succeed');
          context.pushNamed(
            AppRouteName.successRepay,
            extra: {
              'successLoan': state.successRepayment,
              'loanApplicationData': loanApplication,
              'loanType': loanType,
              'isRepayment': isRepayment,
            },
          );

          // customToast(context, message: "Paid Successfully", isError: false);
          // context.goNamed('createPin',
          //     extra: {'email': _emailController.text, 'source': 'profile_add'});
        } else if (state is RepaymentErrorState) {
          context.pushNamed(AppRouteName.failureRepay);
          CustomToastification(
            context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: CustomAppBar(
            title: isAgent
                ? 'Agent Code'
                : isRepayment
                    ? pageAppBar
                    : 'Loan Upfront',
          ),
          body: SafeArea(
            bottom: false,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 16.h,
                      ),
                      // padding: EdgeInsets.all(screenSize.width * 0.06),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isAgent
                                ? 'Your Loan Repayment Code'
                                : isRepayment
                                    ? pageTitle
                                    : 'Confirm Upfront Repayment',
                            style: GoogleFonts.outfit(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8.h),

                          Text(
                            isAgent
                                ? 'Proceed with your Monthly Payment or add an extra amount to pay towards your mortgage.'
                                : pageDescription,
                            style: GoogleFonts.outfit(
                              fontSize: 14.sp,
                              color: Colors.black.withOpacity(0.3),
                            ),
                          ),
                          // SizedBox(height: screenSize.height * 0.09),
                          SizedBox(height: 16.h),

                          Container(
                            padding: EdgeInsets.symmetric(
                              vertical: 24.h,
                              horizontal: 16.w,
                            ),
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              shadows: const [
                                BoxShadow(
                                  color: Color(0x0F000000),
                                  blurRadius: 24,
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (isAgent)
                                  Padding(
                                    padding: EdgeInsets.only(bottom: 16.0.h),
                                    child: BuildReferanceSection(
                                      fontSize: 20.sp,
                                      repaymnetCode:
                                          '${loanApplication.currentBill?.repaymentCode}',
                                    ),
                                  ),
                                if (isAgent)
                                  const BuildCongratulation(
                                    hasMargin: false,
                                    message:
                                        'Please share your unique code with the agent to complete your loan payment. You can visit any nearby authorized agent or payment center. month ',
                                  )
                                else
                                  _buildWalletBalance(context),

                                if (!isAgent)
                                  Center(
                                    child: CustomBuildText(
                                      text: isRepayment
                                          ? '\$ ${loanApplication.currentBill?.totalAmount}'
                                          : '\$ ${loanApplication.loanInfo?.downPayment?.amount}',
                                      color: Theme.of(context).primaryColor,
                                      fontSize: 44.sp,
                                      fontWeight: FontWeight.w800,
                                    ),
                                  ),
                                if (!isAgent)
                                  SizedBox(
                                    height: 4.h,
                                  ),
                                if (!isAgent)
                                  Center(
                                    child: CustomBuildText(
                                      text: isRepayment
                                          ? 'Monthly Repayment Amount'
                                          : 'Upfront Payment Amount',
                                      color: const Color(0xFF919191),
                                      fontSize: 16.sp,
                                    ),
                                  ),
                                SizedBox(height: 28.h),
                                Text(
                                  isAgent
                                      ? 'Repayment Details'
                                      : 'Transaction Details',
                                  style: GoogleFonts.outfit(
                                    fontSize: 18.h,
                                    //  _getResponsiveFontSize(context, 18),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SizedBox(height: 8.h),
                                // _buildTransactionDetail(
                                //     context, 'Customer Name:', '${userName}'),
                                _getUsername(context),
                                _buildTransactionDetail(
                                  context,
                                  'Mortgage Name:',
                                  '${loanApplication.productDetails?.name}',
                                ),
                                _buildTransactionDetail(
                                  context,
                                  'Loan type:',
                                  '$loanType Loan',
                                ),
                                _buildTransactionDetail(
                                  context,
                                  'Loan period:',
                                  '${((loanApplication.loanInfo?.loanPeriod ?? 0) / 12).toStringAsFixed(0)} years',
                                ),
                                if (!isRepayment)
                                  _buildTransactionDetail(
                                    context,
                                    'Down payment:',
                                    ' ${loanApplication.loanInfo?.downPayment?.percentage}% (\$${loanApplication.loanInfo?.downPayment?.amount})',
                                  ),
                                if (isRepayment)
                                  _buildTransactionDetail(
                                    context,
                                    'Monthly payment:',
                                    '\$${loanApplication.loanInfo?.monthlyRepayment}',
                                  ),
                                _buildTransactionDetail(
                                  context,
                                  'Interest rate',
                                  '${loanApplication.loanInfo?.interestRate}%',
                                ),
                                _buildTransactionDetail(
                                  context,
                                  'Facilitation fee',
                                  isRepayment
                                      ? '\$${loanApplication.currentBill?.facilitationFee}'
                                      : '\$${loanApplication.loanInfo?.downPayment?.facilitationFee}',
                                ),

                                // "${loanApplication.loanInfo?.facilitationFee}"),
                                if (isRepayment)
                                  _buildTransactionDetail(
                                    context,
                                    'Amount',
                                    '\$ ${loanApplication.currentBill?.principalAmount}',
                                  ),
                                _buildTransactionDetail(
                                  context,
                                  'Payment date:',
                                  formatDateInMonth(
                                    DateTime.now().toString(),
                                    isAbbreviate: true,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                SizedBox(
                                  // padding: EdgeInsets.symmetric(
                                  //   horizontal: screenSize.width * 0.05,
                                  // ),
                                  height: 1,
                                  child: CustomPaint(
                                    painter: DottedLinePainter(),
                                    size: const Size(double.infinity, 1),
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomBuildText(
                                      text: isAgent
                                          ? 'Repayment Amount'
                                          : 'Total',
                                      fontSize: 20.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    SizedBox(width: screenSize.width * 0.05),
                                    Flexible(
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        child: CustomBuildText(
                                          text: isRepayment
                                              ? '${loanApplication.currentBill?.totalAmount}'
                                              : '\$ ${loanApplication.loanInfo?.downPayment?.total}',
                                          fontSize: 20.sp,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x0C001B1A),
                        blurRadius: 3,
                        offset: Offset(0, -1),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  child: Column(
                    children: [
                      _buildBottomNavigationBar(context, theme, state),
                      if (Platform.isIOS)
                        SizedBox(
                          height: 10.h,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomNavigationBar(
    BuildContext context,
    ThemeData theme,
    var state,
  ) {
    final balance = _getWalletBalance(context);
    debugPrint('balance $balance');

    var paymentAmount = isRepayment
        ? (loanApplication.currentBill?.totalAmount ?? '0.0')
        : (loanApplication.loanInfo?.downPayment?.total ?? '0.0');

    var amount = 0.00;

    if (paymentAmount.isNotEmpty) {
      try {
        paymentAmount = paymentAmount.replaceAll(',', '');

        amount = double.parse(paymentAmount);
      } catch (e) {
        debugPrint('Error parsing upfront value: $e');
        // upfront remains 0.0 in case of error
      }
    }

    debugPrint('Upfront $amount');

    final check = balance > amount;

    return Container(
      child: CustomButton(
        text: isAgent ? 'Back to My Loans' : 'Confirm Payment',
        showLoadingIndicator: state is RepaymentLoadingState,
        onPressed: () async {
          if (isAgent) {
            context.go(AppRouteName.home);
          } else {
            if (check) {
              context.read<RepaymentBloc>().add(
                    PayRepaymentEvent(
                      loanId: loanId,
                      isRepayment: isRepayment,
                      months: months,
                    ),
                  );
            } else {
              CustomToastification(
                context,
                message: 'Insufficient balance to proceed.',
              );

              print('Insufficient balance for the upfront payment.');
            }
          }
        },
        options: CustomButtonOptions(
          color: isAgent
              ? theme.primaryColor
              : check
                  ? theme.primaryColor
                  : const Color.fromARGB(255, 168, 167, 167),
          textStyle: GoogleFonts.plusJakartaSans(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  @override
  Widget _getUsername(BuildContext context) {
    return FutureBuilder<String>(
      future: getUserName(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (snapshot.hasError) {
          return Text(
            'Error: ${snapshot.error}',
            style: const TextStyle(color: Colors.red),
          );
        } else if (snapshot.hasData) {
          return _buildTransactionDetail(
            context,
            'Customer Name:',
            '${snapshot.data}',
          );
        } else {
          return const Text(
            'Customer Name: Unknown',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          );
        }
      },
    );
  }

  Widget _buildWalletBalance(BuildContext context) {
    final balance = _getWalletBalance(context);

    return CustomWalletBalance(walletBalance: balance, isBirrBalance: false);
  }

  Widget _buildTransactionDetail(
    BuildContext context,
    String label,
    String value,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: Colors.black.withOpacity(0.5),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
