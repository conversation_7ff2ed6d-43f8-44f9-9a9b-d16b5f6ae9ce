import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';

class BuildMemberLevel extends StatelessWidget {
  const BuildMemberLevel({required this.memberLevel, super.key});

  final String memberLevel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
        ),
        borderRadius: BorderRadius.circular(32),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            MediaRes.memberLevelBadge,
            color: Theme.of(context).primaryColor,
            width: 20,
            height: 20,
          ),
          const SizedBox(
            width: 6,
          ),
          CustomBuildText(
            text: getMember(),
            fontWeight: FontWeight.w500,
            color: Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }

  String getMember() {
    switch (memberLevel.toLowerCase()) {
      case 'level_one':
        return 'Level 1';
      case 'level_two':
        return 'Level 2';
      case 'level_three':
        return 'Level 3';
      default:
        return 'Level 1';
    }
  }
}
