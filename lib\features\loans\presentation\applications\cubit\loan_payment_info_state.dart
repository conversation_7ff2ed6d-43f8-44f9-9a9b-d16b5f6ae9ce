import 'package:equatable/equatable.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';

/// State for loan payment information
abstract class LoanPaymentInfoState extends Equatable {
  const LoanPaymentInfoState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class LoanPaymentInfoInitial extends LoanPaymentInfoState {
  const LoanPaymentInfoInitial();
}

/// Loading payment information
class LoanPaymentInfoLoading extends LoanPaymentInfoState {
  const LoanPaymentInfoLoading({
    required this.bank,
    required this.loanProduct,
    required this.upfrontPayment,
    required this.productId,
    required this.loanType,
  });

  final LoanBank bank;
  final LoanProduct loanProduct;
  final UpfrontPayment upfrontPayment;
  final String productId;
  final LoanItemType loanType;

  @override
  List<Object?> get props =>
      [bank, loanProduct, upfrontPayment, productId, loanType];
}

/// Payment information loaded successfully
class LoanPaymentInfoLoaded extends LoanPaymentInfoState {
  const LoanPaymentInfoLoaded({
    required this.paymentInfo,
    required this.bank,
    required this.loanProduct,
    required this.upfrontPayment,
    required this.productId,
    required this.loanType,
    this.lastUpdated,
  });

  final Map<String, dynamic> paymentInfo;
  final LoanBank bank;
  final LoanProduct loanProduct;
  final UpfrontPayment upfrontPayment;
  final String productId;
  final LoanItemType loanType;
  final DateTime? lastUpdated;

  /// Get monthly payment amount
  String get monthlyPayment =>
      paymentInfo['monthlyPayment']?.toString() ?? 'TBD';

  /// Get total loan amount
  String get totalAmount => paymentInfo['totalAmount']?.toString() ?? 'TBD';

  /// Get interest rate
  String get interestRate =>
      paymentInfo['interestRate']?.toString() ?? loanProduct.facilitationRate;

  /// Get processing fee
  String get processingFee =>
      paymentInfo['processingFee']?.toString() ?? loanProduct.applicationFee;

  /// Get down payment amount
  String get downPaymentAmount =>
      paymentInfo['downPaymentAmount']?.toString() ?? 'TBD';

  /// Get loan term in months
  int get loanTermMonths =>
      int.tryParse(paymentInfo['loanTermMonths']?.toString() ?? '0') ?? 0;

  @override
  List<Object?> get props => [
        paymentInfo,
        bank,
        loanProduct,
        upfrontPayment,
        productId,
        lastUpdated,
      ];
}

/// Error loading payment information
class LoanPaymentInfoError extends LoanPaymentInfoState {
  const LoanPaymentInfoError({
    required this.message,
    required this.errorType,
    this.bank,
    this.loanProduct,
    this.upfrontPayment,
    this.productId,
    this.loanType,
    this.canRetry = true,
    this.retryCount = 0,
  });

  final String message;
  final PaymentInfoErrorType errorType;
  final LoanBank? bank;
  final LoanProduct? loanProduct;
  final UpfrontPayment? upfrontPayment;
  final String? productId;
  final LoanItemType? loanType;
  final bool canRetry;
  final int retryCount;

  /// Get user-friendly error message
  String get userFriendlyMessage {
    switch (errorType) {
      case PaymentInfoErrorType.network:
        return 'Please check your internet connection and try again';
      case PaymentInfoErrorType.server:
        return 'Unable to calculate payment details. Please try again';
      case PaymentInfoErrorType.invalidData:
        return 'Invalid loan parameters. Please select different options';
      case PaymentInfoErrorType.timeout:
        return 'Request timed out. Please try again';
      case PaymentInfoErrorType.unknown:
        return 'Something went wrong. Please try again';
    }
  }

  @override
  List<Object?> get props => [
        message,
        errorType,
        bank,
        loanProduct,
        upfrontPayment,
        productId,
        loanType,
        canRetry,
        retryCount,
      ];
}

/// Types of payment info errors
enum PaymentInfoErrorType {
  network,
  server,
  invalidData,
  timeout,
  unknown,
}
