import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/monthly_repayment_transaction.dart';
import 'package:cbrs/features/my_loan/domain/entity/my_loan.dart';
import 'package:cbrs/features/my_loan/domain/entity/payment_history.dart';
import 'package:cbrs/features/my_loan/domain/entity/success_payment.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_loan_entity.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_transaction_entity.dart';

abstract class LoanRepaymentRepository {
  ResultFuture<LoanRepaymentEntity> fetchLoanRepayments({
    required String status,
    required int page,
  });

  ResultFuture<LoanRepaymentInfoEntity> fetchLoanInfoDetail({
    required String loanId,
    required String months,
  });
  ResultFuture<UpfrontTransactionEntity> generateUpfrontPayment({
    required String loanId,
  });
  ResultFuture<UpfrontLoanEntity> confirmUprontPaymentWithPin({
    required String transactionType,
    required String billRefNo,
    required String pin,
  });

  ResultFuture<bool> confirmWithOtp({
    required String transactionType,
    required String billRefNo,
    required int otpCode,
  });
  ResultFuture<bool> resendOtp({
    required String otpFor,
    required String billRefNo,
  });

  ResultFuture<MonthlyRepaymentEntity> generateMonthlyRepayment({
    required String loanId,
    required String months,
  });

  ResultFuture<MonthlyRepaymentTransactionEntity> confirmMonthlyRepaymentWithPin({
    required String loanId,
    required String months,
    required String transactionType,
    required String billRefNo,
    required String pin,
  });
  ResultFuture<LoanPaymentHistoryEntity> fetchPaymentHistory({
    required String loanPaymentId,
  });
}
