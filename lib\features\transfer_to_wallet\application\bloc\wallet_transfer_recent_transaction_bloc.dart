import 'package:bloc/bloc.dart';
import 'package:cbrs/features/send_money/application/bloc/bank_transfer_bloc.dart';
import 'package:cbrs/features/send_money/domain/entities/recent_transaction_hive.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/usecases/get_users_avatar_use_case.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

class RecentWalletTransferBloc
    extends Bloc<WalletTransferEvent, WalletTransferState> {
  RecentWalletTransferBloc({
    required GetUsersAvatarUseCase getUsersAvatarUseCase,
  })  : _getUsersAvatarUseCase = getUsersAvatarUseCase,
        super(const WalletTransferInitial()) {
    on<GetRecentWalletTransferEvent>(_getRecentTransactions);
    on<SaveRecentWalletTransferEvent>(_saveRecentTransactions);

    //
  }

  final GetUsersAvatarUseCase _getUsersAvatarUseCase;

  Future<void> _saveRecentTransactions(
    SaveRecentWalletTransferEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    /// 1. reading from hive
    /// 2 check if recipent is found
    /// 3 if it is found removing from local and replace with the one
    /// 4. new transaction must be on the top. if it is not it should be sorted.
    final box =
        Hive.box<RecentWalletTransferHive>('recentWalletTransactionsBox');

    // Remove old if exists
    final existingKey = box.keys.firstWhere(
      (key) => box.get(key)!.uniqueKey == event.transaction.uniqueKey,
      orElse: () => null,
    );

    if (existingKey != null) {
      await box.delete(existingKey);
    }

    final newTransaction = RecentWalletTransferHive(
      recipientId: event.transaction.recipientId,
      recipientName: event.transaction.recipientName,
      recipientPhone: event.transaction.recipientPhone,
      recipientEmail: event.transaction.recipientEmail,
      createdAt: DateTime.now(),
      avatar: '',
    );

    debugPrint('saving the transactions $newTransaction');

    await box.add(newTransaction);
  }

  Future<void> _getRecentTransactions(
    GetRecentWalletTransferEvent event,
    Emitter<WalletTransferState> emit,
  ) async {
    emit(const QuickWalletLoading());
    final transactions =
        Hive.box<RecentWalletTransferHive>('recentWalletTransactionsBox');

    if (transactions.isEmpty) {
      emit(const EmptyRecentWalletTransferState());
      return;
    }
    transactions.values.toList().sort(
          (a, b) => a.createdAt.compareTo(b.createdAt),
        );

    debugPrint('hhdhdhdhdhhdhdhdhd😁');
    final transactionWithLimit = transactions.values.take(event.limit).toList();

    //take(event.limit).toList();

    final userIds = <String>[];

    for (final transaction in transactionWithLimit) {
      debugPrint('user is ${transaction.recipientName}');
      userIds.add(transaction.recipientId);
    }

    try {
      final result = await _getUsersAvatarUseCase(
        GetUsersAvatarParams(
          userIds: userIds,
        ),
      );

      debugPrint('hellowllll ');

      result.fold(
        (failure) {
          emit(WalletTransferError(failure.message));
        },
        (data) {
          debugPrint('dddddata $data');
          final transactionResult = transactionWithLimit.map((transaction) {
            debugPrint(
              'Wallet transfer transactionResult ${data[transaction.avatar]} ',
            );

            return RecentWalletTransferHive(
              recipientEmail: transaction.recipientEmail,
              recipientId: transaction.recipientId,
              recipientName: transaction.recipientName,
              recipientPhone: transaction.recipientPhone,
              avatar: data[transaction.recipientId] ?? '',
              createdAt: transaction.createdAt,
            );
          }).toList();

          emit(LoadedRecentWalletTransferState(transactionResult));
        },
      );
    } catch (e) {
      emit(WalletTransferError(e.toString()));
    }

    // return filteredTransactions;
  }
}
