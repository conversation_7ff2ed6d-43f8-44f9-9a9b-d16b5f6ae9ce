import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/my_loan/domain/entity/upfront_loan_entity.dart';
import 'package:cbrs/features/my_loan/domain/repositories/repayment_repositories.dart';
import 'package:cbrs/features/transactions/data/models/confirm_transfer_response_model.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';
import 'package:equatable/equatable.dart';

class ConfirmUpfrontWithPinUsecase

    extends UsecaseWithParams<UpfrontLoanEntity, ConfirmUpfrontTransferParams> {
  const ConfirmUpfrontWithPinUsecase(this._repository);
  final LoanRepaymentRepository _repository;

  @override
  ResultFuture<UpfrontLoanEntity> call(
    ConfirmUpfrontTransferParams params,
  ) async {
    return _repository.confirmUprontPaymentWithPin(
      pin: params.pin,
      billRefNo: params.billRefNo,
      transactionType: params.transactionType.value,
    );
  }
}

class ConfirmUpfrontTransferParams extends Equatable {
  const ConfirmUpfrontTransferParams({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
  });

  final String pin;
  final String billRefNo;
  final TransactionType transactionType;

  @override
  List<Object?> get props => [pin, billRefNo, transactionType];
}
