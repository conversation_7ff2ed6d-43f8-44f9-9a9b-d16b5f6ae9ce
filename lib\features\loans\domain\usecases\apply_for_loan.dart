import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/loans/domain/entities/loan_application.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/repositories/loan_repository.dart';
import 'package:equatable/equatable.dart';

class ApplyForLoan
    extends UsecaseWithParams<LoanApplication, ApplyForLoanParams> {
  const ApplyForLoan(this.repository);

  final LoanRepository repository;

  @override
  ResultFuture<LoanApplication> call(ApplyForLoanParams params) async {
    return repository.applyForLoan(
      loanId: params.loanId,
      productId: params.productId,
      upfrontPaymentPercentage: params.upfrontPaymentPercentage,
      loanType: params.loanType,
      isAutoRepay: params.isAutoRepay,
    );
  }
}

/// Parameters for applying for a loan
class ApplyForLoanParams extends Equatable {
  const ApplyForLoanParams({
    required this.loanId,
    required this.productId,
    required this.upfrontPaymentPercentage,
    required this.loanType,
    this.isAutoRepay = false,
  });

  final String loanId;
  final String productId;
  final String upfrontPaymentPercentage;
  final LoanItemType loanType;
  final bool isAutoRepay;

  @override
  List<Object?> get props => [
        loanId,
        productId,
        upfrontPaymentPercentage,
        loanType,
        isAutoRepay,
      ];
}
