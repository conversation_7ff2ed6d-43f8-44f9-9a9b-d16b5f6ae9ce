import 'package:cbrs/core/utils/app_mapper.dart';

class LoadWalletStatusResponse {
  LoadWalletStatusResponse({
    required this.statusCode,
    required this.success,
    this.message,
    this.data,
  });

  factory LoadWalletStatusResponse.fromJson(Map<String, dynamic> json) {
    return LoadWalletStatusResponse(
      statusCode: (json['statusCode'] as num?)?.toInt() ?? 0,
      success: json['success'] as bool? ?? false,
      message: json['message']?.toString(),
      data: json['data'] != null
          ? LoadWalletStatusData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }
  final int statusCode;
  final bool success;
  final String? message;
  final LoadWalletStatusData? data;
}

class LoadWalletStatusData {
  LoadWalletStatusData({
    required this.id,
    required this.transactionType,
    required this.billAmount,
    required this.paidAmount,
    required this.totalAmount,
    required this.status,
    required this.billRefNo,
    required this.mpgsReference,
    required this.ftNumber,
    required this.paidDate,
    required this.paymentDetails,
    required this.cardHolderName,
    required this.cardNumber,
    required this.serviceCharge,
    required this.vat,
  });

  /*
      'Card Holder Name': transaction.cardHoderName,
        'Card Number': transaction.cardNumber,
        'Service Charge': transaction.serviceCharge,
        'VAT': transaction.vat,
        'Amount': transaction.billAmount,
        'Date': AppMapper.safeFormattedDate(transaction.createdAt),

        */

  factory LoadWalletStatusData.fromJson(Map<String, dynamic> json) {
    return LoadWalletStatusData(
      id: json['id'] as String? ?? '',
      transactionType: json['transactionType'] as String? ?? '',
      billAmount: (json['billAmount'] as num?)?.toDouble() ?? 0.0,
      paidAmount: (json['paidAmount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0.0,
      status: json['status'] as String? ?? '',
      billRefNo: json['billRefNo'] as String? ?? '',
      mpgsReference: AppMapper.safeString(json['mpgsReference']),
      ftNumber: AppMapper.safeString(json['FTNumber']),
      paidDate: AppMapper.safeString(json['paidDate']),
      cardHolderName: AppMapper.safeString(json['cardHolderName']),
      cardNumber: AppMapper.safeString(json['cardNumber']),
      serviceCharge: AppMapper.safeString(json['serviceCharge']),
      vat: AppMapper.safeString(json['vat']),
      paymentDetails: json['paymentDetails'] != null
          ? PaymentDetails.fromJson(
              json['paymentDetails'] as Map<String, dynamic>,
            )
          : null,
    );
  }
  final String id;
  final String transactionType;
  final double billAmount;
  final double paidAmount;
  final double totalAmount;

  final String status;
  final String mpgsReference;
  final String ftNumber;

  final String billRefNo;
  final String paidDate;
  final String cardHolderName;
  final String? cardNumber;
  final String? serviceCharge;
  final String? vat;

  final PaymentDetails? paymentDetails;
}

class PaymentDetails {
  PaymentDetails({
    required this.sourceOfFunds,
  });

  factory PaymentDetails.fromJson(Map<String, dynamic> json) {
    return PaymentDetails(
      sourceOfFunds:
          SourceOfFunds.fromJson(json['sourceOfFunds'] as Map<String, dynamic>),
    );
  }
  final SourceOfFunds sourceOfFunds;

  Map<String, dynamic> toJson() => {
        'sourceOfFunds': sourceOfFunds.toJson(),
      };
}

class SourceOfFunds {
  SourceOfFunds({
    this.provided,
  });

  factory SourceOfFunds.fromJson(Map<String, dynamic> json) {
    return SourceOfFunds(
      provided: json['provided'] != null
          ? CardDetails.fromJson(
              json['provided']['card'] as Map<String, dynamic>,
            )
          : null,
    );
  }
  final CardDetails? provided;

  Map<String, dynamic> toJson() => {
        'provided': provided?.toJson(),
      };
}

class CardDetails {
  CardDetails({
    required this.number,
    required this.nameOnCard,
  });

  factory CardDetails.fromJson(Map<String, dynamic> json) {
    return CardDetails(
      number: json['number'] as String? ?? '',
      nameOnCard: json['nameOnCard'] as String? ?? '',
    );
  }
  final String number;
  final String nameOnCard;

  Map<String, dynamic> toJson() => {
        'number': number,
        'nameOnCard': nameOnCard,
      };
}
