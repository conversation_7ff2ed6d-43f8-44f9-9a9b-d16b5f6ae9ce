import 'package:equatable/equatable.dart';

class MonthlyRepaymentEntity extends Equatable {
  const MonthlyRepaymentEntity({
    required this.elstRef,
    required this.billRefNo,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.senderName,
    required this.senderPhone,
    required this.bankName,
    required this.bankCode,
    required this.senderId,
    required this.transactionOwner,
    required this.authorizationType,
    required this.status,
    required this.vat,
    required this.serviceCharge,
    required this.originalCurrency,
    required this.billAmount,
    required this.createdAt,
    required this.lastModifiedAt,
    required this.transactionType,
  });
  final String elstRef;
  final String billRefNo;
  final String beneficiaryId;
  final String beneficiaryName;
  final String senderName;
  final String senderPhone;
  final String bankName;
  final String bankCode;
  final String senderId;
  final String transactionOwner;
  final String authorizationType;
  final String status;
  final double vat;
  final double serviceCharge;
  final String originalCurrency;
  final double billAmount;
  final String createdAt;
  final String lastModifiedAt;
  final String transactionType;

  @override
  List<Object?> get props => [
        elstRef,
        billRefNo,
        beneficiaryId,
        beneficiaryName,
        senderName,
        senderPhone,
        bankName,
        bankCode,
        senderId,
        transactionOwner,
        authorizationType,
        status,
        vat,
        serviceCharge,
        originalCurrency,
        billAmount,
        createdAt,
        lastModifiedAt,
        transactionType,
      ];
}
