import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/download_reciept_url.dart';
import 'package:cbrs/features/mini_statements/applications/mini_statement_bloc.dart';
import 'package:cbrs/features/mini_statements/applications/mini_statement_event.dart';
import 'package:cbrs/features/mini_statements/applications/mini_statement_state.dart';
import 'package:cbrs/features/mini_statements/presentations/widgets/menu/mini_statement_account_select.dart';
import 'package:cbrs/features/mini_statements/presentations/widgets/menu/mini_statement_date_range.dart';
import 'package:cbrs/features/mini_statements/presentations/widgets/menu/mini_statement_tabs.dart';

import 'package:cbrs/features/mini_statements/presentations/views/mini_statements_downlod_receipt_screen.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class MiniStatementsMenuScreen extends StatefulWidget {
  const MiniStatementsMenuScreen({super.key});

  @override
  State<MiniStatementsMenuScreen> createState() =>
      _MiniStatementsMenuScreenState();
}

class _MiniStatementsMenuScreenState extends State<MiniStatementsMenuScreen> {
  String selectedTab = 'pdf';
  String startDate = '';
  String endDate = '';
  int selectedAccount = -1;

  List<String> wallets = ['USD', 'ETB'];

  void handleTab(String tabName) {
    setState(() {
      selectedTab = tabName;
    });
  }

  void handleAccountSelect(int index) {
    setState(() {
      selectedAccount = index;
    });
  }

  void handleDateChoose(String choosedDate, String type) {
    //type mean start date or end date
    if (type == 'start') {
      setState(() {
        startDate = choosedDate;
      });
    } else {
      setState(() {
        endDate = choosedDate;
      });
    }
  }

  bool get isActive {
    return startDate.isNotEmpty && endDate.isNotEmpty && selectedAccount != -1;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mini Statements'),
      ),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.only(top: 16.h, right: 16.w, left: 16.w),
          child: Center(
            child: Container(
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 20.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.06),
                              blurRadius: 24,
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            MiniStatementAccountSelect(
                              selectedAccount: selectedAccount == -1
                                  ? ''
                                  : wallets[selectedAccount],
                              onTap: handleAccountSelect,
                              selectedIndex: selectedAccount,
                            ),
                            const SizedBox(
                              height: 16,
                            ),
                            MiniStatementDateRange(
                              onTap: handleDateChoose,
                              start: startDate,
                              end: endDate,
                            ),
                            const SizedBox(
                              height: 16,
                            ),
                            const CustomBuildText(
                              text:
                                  'Choose an account, set your transaction range, and generate your statement with ease.',
                              caseType: '',
                              textAlign: TextAlign.center,
                              color: Color(0xFFAAAAAA),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Center(
                    child: BlocConsumer<MiniStatementBloc, MiniStatementState>(
                      listener: (context, state) {
                        if (state is MiniStatementLoaded) {
                          HandleDownloadReciept.downloadReceipt(
                            context,
                            state.miniStatementURL,
                          );
                        } else if (state is MiniStatementError) {
                          CustomToastification(
                            context,
                            message: state.message,
                          );
                        }
                      },
                      builder: (context, state) {
                        return CustomRoundedBtn(
                          btnText: 'Generate Statements',
                          isLoading: state is MiniStatementLoading,
                          onTap: () {
                            debugPrint('where are we');
                            if (state is MiniStatementLoading) return;

                            if (isActive) {
                              // CustomToastification(context,
                              //     message: wallets[selectedAccount]);
                              context.read<MiniStatementBloc>().add(
                                    GenerateMiniStatementEvent(
                                      startDate: startDate,
                                      endDate: endDate,
                                      currency: wallets[selectedAccount],
                                    ),
                                  );
                            }
                            // Navigator.of(context).push(
                            //   MaterialPageRoute(
                            //     builder: (context) =>
                            //         MiniStatementsDownlodReceiptScreen(),
                            //   ),
                            // );
                          },
                          isBtnActive: isActive,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
