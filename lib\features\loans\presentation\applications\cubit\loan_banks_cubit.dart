import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/features/loans/domain/entities/loan_bank.dart';
import 'package:cbrs/features/loans/domain/entities/loan_item.dart';
import 'package:cbrs/features/loans/domain/usecases/get_loan_banks.dart';
import 'package:cbrs/features/loans/presentation/applications/cubit/loan_banks_state.dart';

/// Awesome loan banks cubit - no cache, always fresh data
class LoanBanksCubit extends Cubit<LoanBanksState> {
  LoanBanksCubit({
    required GetLoanBanks getLoanBanks,
  })  : _getLoanBanks = getLoanBanks,
        super(const LoanBanksInitial());

  final GetLoanBanks _getLoanBanks;

  // Retry mechanism
  Timer? _retryTimer;
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  /// Fetch loan banks - always fresh data, no cache
  Future<void> fetchLoanBanks({
    required String productId,
    required LoanItemType loanType,
    int page = 1,
    int limit = 10,
    bool forceRefresh = false,
    bool showLoadingMessage = true,
  }) async {
    // Show loading state
    if (showLoadingMessage) {
      final loadingMessage = forceRefresh
          ? 'Refreshing banks...'
          : 'Finding the best banks for you...';

      if (!isClosed) {
        emit(LoanBanksLoading(
          message: loadingMessage,
          isRefreshing: forceRefresh,
        ));
      }
    }

    await _fetchBanksWithRetry(
      productId: productId,
      loanType: loanType,
      page: page,
      limit: limit,
    );
  }

  Future<void> _fetchBanksWithRetry({
    required String productId,
    required LoanItemType loanType,
    required int page,
    required int limit,
    int retryCount = 0,
  }) async {
    try {
      debugPrint('🚀 Fetching banks for $loanType (attempt ${retryCount + 1})');

      final result = await _getLoanBanks(GetLoanBanksParams(
        productId: productId,
        loanType: loanType,
        page: page,
        limit: limit,
      ));

      result.fold(
        (failure) => _handleFetchError(
          failure,
          productId,
          loanType,
          page,
          limit,
          retryCount,
        ),
        (banks) => _handleFetchSuccess(
          banks,
          loanType,
          productId,
          page,
          limit,
        ),
      );
    } catch (e) {
      debugPrint('❌ Unexpected error fetching banks: $e');
      _handleFetchError(
        ServerFailure(message: e.toString()),
        productId,
        loanType,
        page,
        limit,
        retryCount,
      );
    }
  }

  /// Handle successful bank fetch
  void _handleFetchSuccess(
    List<LoanBank> banks,
    LoanItemType loanType,
    String productId,
    int page,
    int limit,
  ) {
    debugPrint('✅ Successfully fetched ${banks.length} banks for $loanType');

    if (!isClosed) {
      emit(LoanBanksLoaded(
        banks: banks,
        loanType: loanType,
        productId: productId,
        lastUpdated: DateTime.now(),
        currentPage: page,
        hasMoreBanks: banks.length >= limit,
      ));
    }

    // Auto-select best options
    _autoSelectBestOptions();
  }

  void _handleFetchError(
    Failure failure,
    String productId,
    LoanItemType loanType,
    int page,
    int limit,
    int retryCount,
  ) {
    final errorType = _determineErrorType(failure);

    debugPrint(
      '❌ Error fetching banks: ${failure.message} '
      '(attempt ${retryCount + 1})',
    );

    // Try to retry if possible
    if (retryCount < _maxRetryAttempts && _shouldRetry(errorType)) {
      debugPrint('🔄 Retrying in ${_retryDelay.inSeconds} seconds...');

      _retryTimer?.cancel();
      _retryTimer = Timer(_retryDelay, () {
        _fetchBanksWithRetry(
          productId: productId,
          loanType: loanType,
          page: page,
          limit: limit,
          retryCount: retryCount + 1,
        );
      });
      return;
    }

    // Emit error state only if cubit is not closed
    if (!isClosed) {
      emit(LoanBanksError(
        message: failure.message,
        errorType: errorType,
        canRetry: _shouldRetry(errorType),
        retryCount: retryCount,
        lastAttempt: DateTime.now(),
        cachedBanks: null, // No cached banks since we don't cache
      ));
    }
  }

  /// Auto-select the best bank and options for user convenience
  void _autoSelectBestOptions() {
    final currentState = state;
    if (currentState is! LoanBanksLoaded || currentState.banks.isEmpty) return;

    final recommendedBank = currentState.recommendedBank;
    if (recommendedBank == null) return;

    debugPrint('🎯 Auto-selecting recommended bank: ${recommendedBank.name}');
    selectBank(recommendedBank);
  }

  /// Select a specific bank
  void selectBank(LoanBank bank) {
    final currentState = state;
    if (currentState is! LoanBanksLoaded) return;

    // Select first available loan product
    final firstLoanProduct = bank.loans.isNotEmpty ? bank.loans.first : null;

    // Select first available upfront payment
    final firstUpfrontPayment =
        firstLoanProduct?.upfrontPaymentOptions.isNotEmpty == true
            ? firstLoanProduct!.upfrontPaymentOptions.first
            : null;

    debugPrint('🏦 Selected bank: ${bank.name}');
    if (firstLoanProduct != null) {
      debugPrint('💰 Auto-selected loan product: ${firstLoanProduct.loanType}');
    }

    if (!isClosed) {
      emit(currentState.copyWith(
        selectedBank: bank,
        selectedLoanProduct: firstLoanProduct,
        selectedUpfrontPayment: firstUpfrontPayment,
        clearPaymentInfo: true, // Clear previous payment info
      ));
    }
  }

  /// Select upfront payment option
  void selectUpfrontPayment(UpfrontPayment upfrontPayment) {
    final currentState = state;
    if (currentState is! LoanBanksLoaded) return;

    debugPrint('💳 Selected upfront payment: ${upfrontPayment.percentage}%');

    if (!isClosed) {
      emit(currentState.copyWith(
        selectedUpfrontPayment: upfrontPayment,
        clearPaymentInfo: true,
      ));
    }
  }

  /// Clear all data and reset to initial state
  void clearBanks() {
    _retryTimer?.cancel();
    if (!isClosed) {
      emit(const LoanBanksInitial());
    }
  }

  /// Refresh banks data
  Future<void> refreshBanks({
    required String productId,
    required LoanItemType loanType,
  }) async {
    await fetchLoanBanks(
      productId: productId,
      loanType: loanType,
      forceRefresh: true,
    );
  }

  /// Determine error type from failure
  LoanBanksErrorType _determineErrorType(Failure failure) {
    if (failure is ServerFailure) {
      if (failure.message.toLowerCase().contains('network') ||
          failure.message.toLowerCase().contains('connection')) {
        return LoanBanksErrorType.network;
      }
      if (failure.message.toLowerCase().contains('timeout')) {
        return LoanBanksErrorType.timeout;
      }
      return LoanBanksErrorType.server;
    }
    return LoanBanksErrorType.unknown;
  }

  /// Check if we should retry for this error type
  bool _shouldRetry(LoanBanksErrorType errorType) {
    switch (errorType) {
      case LoanBanksErrorType.network:
      case LoanBanksErrorType.timeout:
      case LoanBanksErrorType.server:
        return true;
      case LoanBanksErrorType.noData:
      case LoanBanksErrorType.unknown:
        return false;
    }
  }

  @override
  Future<void> close() {
    _retryTimer?.cancel();
    return super.close();
  }
}
