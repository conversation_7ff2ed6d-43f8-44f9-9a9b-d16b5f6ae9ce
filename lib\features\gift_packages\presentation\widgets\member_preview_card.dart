import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class MemberPreviewCard extends StatelessWidget {
  final Map<String, dynamic> memberData;
  final VoidCallback onTap;

  const MemberPreviewCard({
    super.key,
    required this.memberData,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final firstName = memberData['firstName'] as String;
    final middleName = memberData['middleName'] as String;
    final lastName = memberData['lastName'] as String;
    final phoneNumber = memberData['phoneNumber'] as String;
    
    final initials = '${firstName.substring(0, 1).toUpperCase()}'
        '${middleName.substring(0, 1).toUpperCase()}';
    
    final fullName = '$firstName $middleName $lastName'.trim();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.2),
          ),
        ),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: Theme.of(context).primaryColor,
              radius: 20,
              child: Text(
                initials,
                style: GoogleFonts.outfit(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fullName,
                    style: GoogleFonts.outfit(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    phoneNumber,
                    style: GoogleFonts.outfit(
                      fontSize: 14,
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
