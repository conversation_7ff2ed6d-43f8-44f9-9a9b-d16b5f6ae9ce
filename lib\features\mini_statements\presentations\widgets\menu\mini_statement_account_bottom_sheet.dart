import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/my_loan/presentation/views/screen_confrim_repay_page.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/features/my_loan/domain/entity/loan_info.dart';
import 'package:google_fonts/google_fonts.dart';

class MiniStatementAccountBottomSheet extends StatefulWidget {
  const MiniStatementAccountBottomSheet({
    required this.selectedTerm,
    required this.onTermSelected,
    super.key,
  });
  final int selectedTerm;
  final Function(int) onTermSelected;

  @override
  State<MiniStatementAccountBottomSheet> createState() =>
      _MiniStatementAccountBottomSheetState();
}

class _MiniStatementAccountBottomSheetState
    extends State<MiniStatementAccountBottomSheet> {
  @override
  void initState() {
    setState(() {
      selectedOption = widget.selectedTerm;
    });
  }

  int selectedOption = 0;
  void handleSelection(int term) {
    setState(() {
      selectedOption = term;
    });
    // widget.onTermSelected(term);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildBottomSheetHeader(context),
        SizedBox(
          height: 4.h,
        ),
        CustomBuildText(
          text: 'Select your wallet to genretaaa a transaction staement of it.',
          color: const Color(0xFFAAAAAA),
          fontSize: 14.sp,
          caseType: '',
        ),
        SizedBox(
          height: 16.h,
        ),
        // _buildPaymentRow(
        //   iconString: MediaRes.solarWallet,
        //   onTap: () async {
        //     handleSelection(0);
        //   },
        //   paymentText: 'Both Wallet',
        //   isSelected: selectedOption == 0,
        // ),
        // SizedBox(
        //   height: 8.h,
        // ),
        _buildPaymentRow(
          iconString: MediaRes.solarAgent,
          onTap: () async {
            handleSelection(0);
          },
          paymentText: 'USD Wallet',
          isSelected: selectedOption == 0,
        ),
        SizedBox(
          height: 8.h,
        ),
        _buildPaymentRow(
          iconString: MediaRes.solarAgent,
          onTap: () async {
            handleSelection(1);
          },
          paymentText: 'ETB Wallet',
          isSelected: selectedOption == 1,
        ),
        const SizedBox(
          height: 30,
        ),
        _buildPaymentAction(context, selectedOption),
      ],
    );
  }

  Widget _buildBottomSheetHeader(BuildContext context) {
    return Container(
      // color: Colors.green,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomBuildText(
            text: 'Select Wallet',
            fontWeight: FontWeight.w700,
            fontSize: 18.sp,
          ),
          // GestureDetector(
          //   onTap: () {
          //     Navigator.pop(context);
          //   },
          //   child: Icon(
          //     Icons.close,
          //     color: Colors.black,
          //     size: 20.h,
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildPaymentRow({
    required String iconString,
    required String paymentText,
    required VoidCallback onTap,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        // height: 56.h,
        padding: EdgeInsets.all(16.h),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: const Color(0xFFF8F8F8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            // side: BorderSide(color: Colors.grey[300]!),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Container(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 20.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected
                            ? const Color(0xFF065234).withOpacity(0.8)
                            : Colors.white,
                        border: Border.all(color: Colors.grey.shade500),
                      ),
                      child: isSelected
                          ? Icon(Icons.check, color: Colors.white, size: 15.h)
                          : null,
                    ),
                    SizedBox(width: 12.w),
                    Image.asset(
                      MediaRes.miniStatementWalletIcon,
                      width: 40.w,
                      height: 40.h,
                    ),
                    SizedBox(width: 12.w),
                    CustomBuildText(
                      text: paymentText,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      caseType: '',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentAction(BuildContext context, int selectedOption) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Center(
            child: CustomButton(
              text: 'Continue',
              onPressed: () {
                debugPrint('where are we');
                widget.onTermSelected(selectedOption);
              },
              options: CustomButtonOptions(
                padding: EdgeInsets.symmetric(vertical: 16.h),
                color: Theme.of(context).primaryColor,
                textStyle: GoogleFonts.outfit(
                  fontSize: 16.h,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
